<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API连接测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 AI服务连接测试</h1>
        
        <div class="test-section">
            <h3>🦙 Ollama 服务测试</h3>
            <button class="test-button" onclick="testOllama()">测试 Ollama 连接</button>
            <button class="test-button" onclick="testOllamaModels()">获取 Ollama 模型列表</button>
            <div id="ollama-result" class="result info">点击按钮开始测试...</div>
        </div>

        <div class="test-section">
            <h3>🏢 LM Studio 服务测试</h3>
            <button class="test-button" onclick="testLMStudio()">测试 LM Studio 连接</button>
            <button class="test-button" onclick="testLMStudioModels()">获取 LM Studio 模型列表</button>
            <div id="lmstudio-result" class="result info">点击按钮开始测试...</div>
        </div>

        <div class="test-section">
            <h3>🌐 网络诊断</h3>
            <button class="test-button" onclick="testNetwork()">网络连通性测试</button>
            <div id="network-result" class="result info">点击按钮开始测试...</div>
        </div>

        <div class="test-section">
            <h3>📋 使用说明</h3>
            <p><strong>Ollama:</strong> 确保 Ollama 服务运行在 http://localhost:11434</p>
            <p><strong>LM Studio:</strong> 确保 LM Studio 服务运行在 http://localhost:1234</p>
            <p><strong>调试快捷键:</strong> Ctrl+Shift+R 清理数据并重启</p>
        </div>
    </div>

    <script>
        async function testOllama() {
            const resultDiv = document.getElementById('ollama-result');
            resultDiv.textContent = '🔄 正在测试 Ollama 连接...';
            resultDiv.className = 'result info';

            try {
                const response = await fetch('http://localhost:11434/api/tags', {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });

                if (response.ok) {
                    resultDiv.textContent = `✅ Ollama 连接成功！\n状态码: ${response.status}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `❌ Ollama 连接失败\n状态码: ${response.status}\n状态文本: ${response.statusText}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `❌ Ollama 连接错误:\n${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function testOllamaModels() {
            const resultDiv = document.getElementById('ollama-result');
            resultDiv.textContent = '🔄 正在获取 Ollama 模型列表...';
            resultDiv.className = 'result info';

            try {
                const response = await fetch('http://localhost:11434/api/tags');
                const data = await response.json();

                if (response.ok) {
                    resultDiv.textContent = `✅ Ollama 模型列表:\n${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `❌ 获取模型列表失败:\n${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `❌ 获取模型列表错误:\n${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function testLMStudio() {
            const resultDiv = document.getElementById('lmstudio-result');
            resultDiv.textContent = '🔄 正在测试 LM Studio 连接...';
            resultDiv.className = 'result info';

            try {
                const response = await fetch('http://localhost:1234/v1/models', {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });

                if (response.ok) {
                    resultDiv.textContent = `✅ LM Studio 连接成功！\n状态码: ${response.status}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `❌ LM Studio 连接失败\n状态码: ${response.status}\n状态文本: ${response.statusText}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `❌ LM Studio 连接错误:\n${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function testLMStudioModels() {
            const resultDiv = document.getElementById('lmstudio-result');
            resultDiv.textContent = '🔄 正在获取 LM Studio 模型列表...';
            resultDiv.className = 'result info';

            try {
                const response = await fetch('http://localhost:1234/v1/models');
                const data = await response.json();

                if (response.ok) {
                    resultDiv.textContent = `✅ LM Studio 模型列表:\n${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `❌ 获取模型列表失败:\n${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `❌ 获取模型列表错误:\n${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function testNetwork() {
            const resultDiv = document.getElementById('network-result');
            resultDiv.textContent = '🔄 正在进行网络诊断...';
            resultDiv.className = 'result info';

            const tests = [
                { name: 'Ollama 端口', url: 'http://localhost:11434' },
                { name: 'LM Studio 端口', url: 'http://localhost:1234' }
            ];

            let results = '🌐 网络诊断结果:\n\n';

            for (const test of tests) {
                try {
                    const startTime = Date.now();
                    const response = await fetch(test.url, { 
                        method: 'HEAD',
                        mode: 'no-cors'
                    });
                    const endTime = Date.now();
                    
                    results += `✅ ${test.name}: 可访问 (${endTime - startTime}ms)\n`;
                } catch (error) {
                    results += `❌ ${test.name}: 不可访问 (${error.message})\n`;
                }
            }

            resultDiv.textContent = results;
            resultDiv.className = 'result info';
        }

        // 页面加载时自动运行基础测试
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔧 API连接测试页面已加载');
            testNetwork();
        });
    </script>
</body>
</html>
