<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI聊天助手 - 优化版</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .loading-screen {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #3b82f6, #1d4ed8, #3b82f6);
            background-size: 200% 100%;
            animation: progress-flow 2s linear infinite;
        }
        
        @keyframes progress-flow {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        .pulse-ring {
            animation: pulse-ring 1.25s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
        }
        
        .pulse-dot {
            animation: pulse-dot 1.25s cubic-bezier(0.455, 0.03, 0.515, 0.955) -.4s infinite;
        }
        
        @keyframes pulse-ring {
            0% { transform: scale(0.33); }
            80%, 100% { opacity: 0; }
        }
        
        @keyframes pulse-dot {
            0% { transform: scale(0.8); }
            50% { transform: scale(1); }
            100% { transform: scale(0.8); }
        }
        
        .fade-out {
            opacity: 0;
            transition: opacity 0.5s ease-out;
        }
        
        .status-online { background-color: #10b981; }
        .status-offline { background-color: #ef4444; }
        
        .service-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-weight: 600;
        }
        
        .service-ollama { background-color: #3b82f6; color: white; }
        .service-lmstudio { background-color: #10b981; color: white; }
        
        .user-message {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }
        
        .ai-message {
            background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
            border: 1px solid #d1d5db;
        }
        
        .thinking-dot {
            width: 6px;
            height: 6px;
            background-color: #6b7280;
            border-radius: 50%;
            animation: thinking 1.4s infinite ease-in-out both;
        }
        
        .thinking-dot:nth-child(1) { animation-delay: -0.32s; }
        .thinking-dot:nth-child(2) { animation-delay: -0.16s; }
        
        @keyframes thinking {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <!-- 加载屏幕 -->
    <div id="loading-screen" class="loading-screen fixed inset-0 flex flex-col items-center justify-center text-white z-50">
        <div class="text-center">
            <!-- Logo区域 -->
            <div class="mb-8">
                <div class="w-24 h-24 mx-auto mb-4 relative">
                    <div class="absolute inset-0 bg-white rounded-full pulse-ring"></div>
                    <div class="absolute inset-0 bg-white rounded-full pulse-dot flex items-center justify-center">
                        <i class="fas fa-robot text-4xl text-blue-600"></i>
                    </div>
                </div>
                <h1 class="text-4xl font-bold mb-2">AI聊天助手</h1>
                <p class="text-xl opacity-90">智能服务发现与初始化</p>
            </div>
            
            <!-- 进度条 -->
            <div class="w-80 mx-auto mb-8">
                <div class="flex justify-between text-sm mb-2">
                    <span>初始化进度</span>
                    <span id="progress-percent">0%</span>
                </div>
                <div class="w-full bg-white/20 rounded-full h-3">
                    <div id="progress-bar" class="progress-bar h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                </div>
            </div>
            
            <!-- 状态文本 -->
            <div id="status-message" class="text-lg mb-8 min-h-[2rem]">正在启动系统...</div>
            
            <!-- 信息卡片 -->
            <div class="grid grid-cols-2 gap-4 max-w-md mx-auto">
                <div class="bg-white/10 rounded-lg p-4 text-center">
                    <div class="text-2xl mb-2">🔍</div>
                    <div class="text-sm font-semibold">AI服务状态</div>
                    <div class="text-xs mt-1">
                        <div>Ollama: <span id="ollama-status" class="text-yellow-300">检测中...</span></div>
                        <div>LM Studio: <span id="lmstudio-status" class="text-yellow-300">检测中...</span></div>
                    </div>
                </div>
                <div class="bg-white/10 rounded-lg p-4 text-center">
                    <div class="text-2xl mb-2">🤖</div>
                    <div class="text-sm font-semibold">发现模型</div>
                    <div id="total-models" class="text-xs mt-1">0 个</div>
                </div>
                <div class="bg-white/10 rounded-lg p-4 text-center">
                    <div class="text-2xl mb-2">💻</div>
                    <div class="text-sm font-semibold">系统信息</div>
                    <div class="text-xs mt-1">
                        <div>OS: <span id="os-info">检测中...</span></div>
                        <div>浏览器: <span id="browser-info">检测中...</span></div>
                    </div>
                </div>
                <div class="bg-white/10 rounded-lg p-4 text-center">
                    <div class="text-2xl mb-2">⚙️</div>
                    <div class="text-sm font-semibold">配置状态</div>
                    <div class="text-xs mt-1">
                        <div>可用服务: <span id="available-services">0</span></div>
                        <div>网络: <span id="network-status">检测中...</span></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主应用界面 -->
    <div id="main-app" class="hidden min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <!-- 顶部状态栏 -->
        <div class="sticky top-0 bg-white/90 backdrop-blur-sm border-b border-gray-200 z-40">
            <div class="max-w-6xl mx-auto px-4 py-3">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2">
                            <div id="connection-indicator" class="w-3 h-3 rounded-full status-offline"></div>
                            <span id="connection-text" class="text-sm font-medium text-gray-700">未连接</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-gray-600">当前模型:</span>
                            <span id="current-model" class="text-sm font-semibold text-gray-800">未选择模型</span>
                            <span id="model-service-badge" class="service-badge hidden">未知</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button id="refresh-services" class="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button id="view-models" class="px-3 py-1 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            选择模型
                        </button>
                        <button id="settings-btn" class="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="max-w-6xl mx-auto p-4">
            <!-- 系统监控面板 -->
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <div class="text-xs text-gray-500 mb-1">当前模型</div>
                    <div id="panel-current-model" class="text-sm font-semibold text-gray-800">未选择</div>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <div class="text-xs text-gray-500 mb-1">可用模型</div>
                    <div id="panel-total-models" class="text-sm font-semibold text-blue-600">0</div>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <div class="text-xs text-gray-500 mb-1">活跃服务</div>
                    <div id="panel-active-services" class="text-sm font-semibold text-green-600">0</div>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <div class="text-xs text-gray-500 mb-1">响应时间</div>
                    <div id="panel-response-time" class="text-sm font-semibold text-purple-600">--</div>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <div class="text-xs text-gray-500 mb-1">输出速度</div>
                    <div id="panel-output-speed" class="text-sm font-semibold text-orange-600">--</div>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm">
                    <div class="text-xs text-gray-500 mb-1">消息数量</div>
                    <div id="panel-message-count" class="text-sm font-semibold text-indigo-600">0</div>
                </div>
            </div>

            <!-- 聊天界面 -->
            <div class="bg-white rounded-2xl shadow-lg overflow-hidden flex flex-col" style="height: calc(100vh - 280px);">
                <!-- 聊天消息区域 -->
                <div id="chat-container" class="flex-1 overflow-y-auto p-6 space-y-4 bg-gray-50">
                    <!-- 欢迎消息 -->
                    <div id="welcome-message" class="text-center py-8">
                        <div class="text-6xl mb-4">🤖</div>
                        <h2 class="text-2xl font-bold text-gray-800 mb-2">欢迎使用AI聊天助手</h2>
                        <p class="text-gray-600 mb-6">已发现 <span id="welcome-model-count" class="font-semibold text-blue-600">0</span> 个可用模型</p>
                        <div class="flex justify-center space-x-4">
                            <button id="quick-start" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-rocket mr-2"></i>快速开始
                            </button>
                            <button id="view-models-welcome" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-list mr-2"></i>查看模型
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 输入区域 -->
                <div class="border-t border-gray-200 p-4">
                    <div class="flex space-x-4">
                        <textarea 
                            id="user-input" 
                            placeholder="输入您的消息..." 
                            class="flex-1 resize-none border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            rows="1"
                        ></textarea>
                        <button 
                            id="send-btn" 
                            class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                        >
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('脚本开始执行');
        
        class OptimizedChatApp {
            constructor() {
                console.log('OptimizedChatApp 构造函数开始');
                
                // 基础配置
                this.services = {
                    ollama: { name: 'Ollama', url: 'http://localhost:11434', endpoint: '/api/tags' },
                    lmstudio: { name: 'LM Studio', url: 'http://localhost:1234', endpoint: '/v1/models' }
                };
                
                // 状态变量
                this.discoveredModels = [];
                this.activeServices = [];
                this.currentModel = null;
                this.currentService = null;
                this.stats = {
                    messageCount: 0,
                    lastResponseTime: 0,
                    lastOutputSpeed: 0
                };
                
                console.log('构造函数完成，启动初始化');
                // 延迟启动，确保DOM完全准备好
                setTimeout(() => this.startInit(), 300);
            }

            async startInit() {
                console.log('开始初始化流程');

                const steps = [
                    { name: '检测系统信息', progress: 25, action: () => this.detectSystemInfo() },
                    { name: '扫描AI服务', progress: 50, action: () => this.scanServices() },
                    { name: '收集模型信息', progress: 75, action: () => this.collectModels() },
                    { name: '完成初始化', progress: 100, action: () => this.finishInit() }
                ];

                for (const step of steps) {
                    console.log(`执行步骤: ${step.name}`);
                    this.updateProgress(step.name, step.progress);

                    try {
                        await step.action();
                        await this.delay(1000);
                    } catch (error) {
                        console.error(`步骤失败: ${step.name}`, error);
                    }
                }

                this.completeInit();
            }

            updateProgress(message, progress) {
                console.log(`更新进度: ${progress}% - ${message}`);
                document.getElementById('status-message').textContent = message;
                document.getElementById('progress-percent').textContent = `${progress}%`;
                document.getElementById('progress-bar').style.width = `${progress}%`;
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }

            async detectSystemInfo() {
                console.log('检测系统信息');
                const os = navigator.userAgent.includes('Mac') ? 'macOS' :
                          navigator.userAgent.includes('Windows') ? 'Windows' : 'Linux';
                const browser = navigator.userAgent.includes('Chrome') ? 'Chrome' :
                               navigator.userAgent.includes('Firefox') ? 'Firefox' : 'Safari';

                document.getElementById('os-info').textContent = os;
                document.getElementById('browser-info').textContent = browser;
                document.getElementById('network-status').textContent = '正常';
            }

            async scanServices() {
                console.log('扫描AI服务');
                this.activeServices = [];

                const serviceChecks = Object.keys(this.services).map(async (serviceKey) => {
                    const service = this.services[serviceKey];
                    console.log(`检测服务: ${service.name}`);

                    try {
                        const response = await fetch(service.url + service.endpoint, {
                            method: 'GET',
                            headers: { 'Content-Type': 'application/json' }
                        });

                        if (response.ok) {
                            this.activeServices.push(serviceKey);
                            document.getElementById(`${serviceKey}-status`).textContent = '✅ 在线';
                            document.getElementById(`${serviceKey}-status`).className = 'text-green-300';
                            console.log(`✅ ${service.name} 在线`);
                            return { service: serviceKey, success: true };
                        } else {
                            throw new Error(`HTTP ${response.status}`);
                        }
                    } catch (error) {
                        document.getElementById(`${serviceKey}-status`).textContent = '❌ 离线';
                        document.getElementById(`${serviceKey}-status`).className = 'text-red-300';
                        console.log(`❌ ${service.name} 离线: ${error.message}`);
                        return { service: serviceKey, success: false };
                    }
                });

                await Promise.all(serviceChecks);
                document.getElementById('available-services').textContent = this.activeServices.length;
                console.log(`发现 ${this.activeServices.length} 个活跃服务`);
            }

            async collectModels() {
                console.log('收集模型信息');
                this.discoveredModels = [];

                for (const serviceKey of this.activeServices) {
                    const service = this.services[serviceKey];
                    console.log(`从 ${service.name} 获取模型列表`);

                    try {
                        const response = await fetch(service.url + service.endpoint);
                        if (response.ok) {
                            const data = await response.json();
                            const models = this.parseModels(data, serviceKey);
                            this.discoveredModels.push(...models);
                            console.log(`从 ${service.name} 发现 ${models.length} 个模型`);
                        }
                    } catch (error) {
                        console.error(`获取 ${service.name} 模型失败:`, error);
                    }
                }

                document.getElementById('total-models').textContent = `${this.discoveredModels.length} 个`;
                console.log(`总共发现 ${this.discoveredModels.length} 个模型`);
            }

            parseModels(data, serviceKey) {
                const service = this.services[serviceKey];
                const models = [];

                if (serviceKey === 'ollama' && data.models) {
                    data.models.forEach(model => {
                        models.push({
                            id: model.name,
                            name: model.name,
                            service: serviceKey,
                            serviceName: service.name,
                            size: model.size || 'Unknown'
                        });
                    });
                } else if (serviceKey === 'lmstudio' && data.data) {
                    data.data.forEach(model => {
                        models.push({
                            id: model.id,
                            name: model.id,
                            service: serviceKey,
                            serviceName: service.name,
                            size: 'Unknown'
                        });
                    });
                }

                return models;
            }

            async finishInit() {
                console.log('完成初始化设置');
                this.loadUserConfig();
                this.bindEvents();
                this.updateMainUI();
            }

            completeInit() {
                console.log('切换到主界面');
                document.getElementById('loading-screen').classList.add('fade-out');
                setTimeout(() => {
                    document.getElementById('loading-screen').style.display = 'none';
                    document.getElementById('main-app').classList.remove('hidden');
                }, 500);
            }

            loadUserConfig() {
                try {
                    const savedConfig = localStorage.getItem('optimized-chat-config');
                    if (savedConfig) {
                        const config = JSON.parse(savedConfig);
                        if (config.currentModel && this.discoveredModels.find(m => m.id === config.currentModel.id)) {
                            this.currentModel = config.currentModel;
                            this.currentService = config.currentService;
                        }
                    }
                } catch (error) {
                    console.error('加载配置失败:', error);
                }
            }

            bindEvents() {
                console.log('绑定事件监听器');

                // 刷新服务
                document.getElementById('refresh-services').addEventListener('click', () => this.refreshServices());

                // 查看模型
                document.getElementById('view-models').addEventListener('click', () => this.showModelList());
                document.getElementById('view-models-welcome').addEventListener('click', () => this.showModelList());

                // 快速开始
                document.getElementById('quick-start').addEventListener('click', () => this.quickStart());

                // 设置
                document.getElementById('settings-btn').addEventListener('click', () => this.openSettings());

                // 发送消息
                document.getElementById('send-btn').addEventListener('click', () => this.sendMessage());

                // 输入框事件
                const userInput = document.getElementById('user-input');
                userInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                userInput.addEventListener('input', () => {
                    userInput.style.height = 'auto';
                    userInput.style.height = Math.min(userInput.scrollHeight, 120) + 'px';
                });
            }

            updateMainUI() {
                console.log('更新主界面UI');

                // 更新连接状态
                if (this.activeServices.length > 0) {
                    document.getElementById('connection-indicator').className = 'w-3 h-3 rounded-full status-online';
                    document.getElementById('connection-text').textContent = `已连接 ${this.activeServices.length} 个服务`;
                } else {
                    document.getElementById('connection-indicator').className = 'w-3 h-3 rounded-full status-offline';
                    document.getElementById('connection-text').textContent = '未连接';
                }

                // 更新模型信息
                if (this.currentModel) {
                    document.getElementById('current-model').textContent = this.currentModel.name;
                    const badge = document.getElementById('model-service-badge');
                    badge.textContent = this.currentModel.serviceName;
                    badge.className = `service-badge service-${this.currentModel.service}`;
                    badge.classList.remove('hidden');
                } else {
                    document.getElementById('current-model').textContent = '未选择模型';
                    document.getElementById('model-service-badge').classList.add('hidden');
                }

                // 更新系统面板
                document.getElementById('panel-current-model').textContent = this.currentModel ? this.currentModel.name : '未选择';
                document.getElementById('panel-total-models').textContent = this.discoveredModels.length;
                document.getElementById('panel-active-services').textContent = this.activeServices.length;
                document.getElementById('panel-response-time').textContent = this.stats.lastResponseTime ? `${this.stats.lastResponseTime}ms` : '--';
                document.getElementById('panel-output-speed').textContent = this.stats.lastOutputSpeed ? `${this.stats.lastOutputSpeed.toFixed(1)} 字符/秒` : '--';
                document.getElementById('panel-message-count').textContent = this.stats.messageCount;

                // 更新欢迎消息
                document.getElementById('welcome-model-count').textContent = this.discoveredModels.length;
            }

            async refreshServices() {
                console.log('刷新服务');
                const refreshBtn = document.getElementById('refresh-services');
                const icon = refreshBtn.querySelector('i');

                icon.classList.add('fa-spin');
                refreshBtn.disabled = true;

                try {
                    await this.scanServices();
                    await this.collectModels();
                    this.updateMainUI();
                    this.showNotification('服务刷新完成', 'success');
                } catch (error) {
                    this.showNotification('刷新失败: ' + error.message, 'error');
                } finally {
                    icon.classList.remove('fa-spin');
                    refreshBtn.disabled = false;
                }
            }

            quickStart() {
                if (this.discoveredModels.length === 0) {
                    this.showNotification('未发现可用模型，请检查AI服务状态', 'warning');
                    return;
                }

                this.currentModel = this.discoveredModels[0];
                this.currentService = this.currentModel.service;
                this.saveConfig();
                this.updateMainUI();
                this.hideWelcomeMessage();
                this.showNotification(`已选择模型: ${this.currentModel.name}`, 'success');
            }

            showNotification(message, type = 'info') {
                console.log(`[${type.toUpperCase()}] ${message}`);
                // 这里可以添加更复杂的通知UI
            }

            hideWelcomeMessage() {
                const welcomeMessage = document.getElementById('welcome-message');
                if (welcomeMessage) {
                    welcomeMessage.style.display = 'none';
                }
            }

            saveConfig() {
                const config = {
                    currentModel: this.currentModel,
                    currentService: this.currentService,
                    lastUpdate: Date.now()
                };
                localStorage.setItem('optimized-chat-config', JSON.stringify(config));
            }

            showModelList() {
                if (this.discoveredModels.length === 0) {
                    this.showNotification('未发现可用模型', 'warning');
                    return;
                }

                const modelListHTML = this.discoveredModels.map(model => `
                    <div class="model-item p-3 border border-gray-200 rounded-lg hover:bg-blue-50 cursor-pointer" data-model-id="${model.id}">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="font-semibold text-gray-800">${model.name}</div>
                                <div class="text-sm text-gray-600">来源: ${model.serviceName}</div>
                            </div>
                            <span class="service-badge service-${model.service}">${model.serviceName}</span>
                        </div>
                    </div>
                `).join('');

                const modalHTML = `
                    <div id="model-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div class="bg-white rounded-2xl p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
                            <div class="flex items-center justify-between mb-4">
                                <h2 class="text-2xl font-bold text-gray-800">选择模型</h2>
                                <button id="close-modal" class="text-gray-500 hover:text-gray-700">
                                    <i class="fas fa-times text-xl"></i>
                                </button>
                            </div>
                            <div class="overflow-y-auto max-h-96 space-y-3">
                                ${modelListHTML}
                            </div>
                        </div>
                    </div>
                `;

                document.body.insertAdjacentHTML('beforeend', modalHTML);

                // 绑定事件
                document.getElementById('close-modal').addEventListener('click', () => {
                    document.getElementById('model-modal').remove();
                });

                document.querySelectorAll('.model-item').forEach(item => {
                    item.addEventListener('click', () => {
                        const modelId = item.dataset.modelId;
                        const selectedModel = this.discoveredModels.find(m => m.id === modelId);
                        if (selectedModel) {
                            this.currentModel = selectedModel;
                            this.currentService = selectedModel.service;
                            this.saveConfig();
                            this.updateMainUI();
                            this.hideWelcomeMessage();
                            this.showNotification(`已选择模型: ${selectedModel.name}`, 'success');
                        }
                        document.getElementById('model-modal').remove();
                    });
                });
            }

            async sendMessage() {
                const userInput = document.getElementById('user-input');
                const message = userInput.value.trim();

                if (!message) return;
                if (!this.currentModel) {
                    this.showNotification('请先选择一个模型', 'warning');
                    return;
                }

                userInput.value = '';
                userInput.style.height = 'auto';

                this.hideWelcomeMessage();
                this.addMessage('user', message);
                this.addThinkingMessage();

                const startTime = Date.now();

                try {
                    const response = await this.callAPI(message);
                    const responseTime = Date.now() - startTime;

                    this.removeThinkingMessage();
                    this.addMessage('ai', response);

                    // 更新统计
                    this.stats.messageCount++;
                    this.stats.lastResponseTime = responseTime;
                    this.stats.lastOutputSpeed = response.length / (responseTime / 1000);
                    this.updateMainUI();

                } catch (error) {
                    this.removeThinkingMessage();
                    this.addMessage('ai', `抱歉，发生了错误: ${error.message}`);
                    this.showNotification('发送失败: ' + error.message, 'error');
                }
            }

            addMessage(sender, content) {
                const chatContainer = document.getElementById('chat-container');
                const messageDiv = document.createElement('div');

                if (sender === 'user') {
                    messageDiv.className = 'flex justify-end';
                    messageDiv.innerHTML = `
                        <div class="user-message text-white rounded-2xl px-4 py-3 max-w-xs lg:max-w-md">
                            <div class="flex items-center mb-1">
                                <span class="text-sm opacity-90">👤 您</span>
                            </div>
                            <div>${content}</div>
                        </div>
                    `;
                } else {
                    messageDiv.className = 'flex justify-start';
                    messageDiv.innerHTML = `
                        <div class="ai-message text-gray-800 rounded-2xl px-4 py-3 max-w-xs lg:max-w-md">
                            <div class="flex items-center mb-1">
                                <span class="text-sm text-gray-600">🤖 AI助手</span>
                            </div>
                            <div>${content}</div>
                        </div>
                    `;
                }

                chatContainer.appendChild(messageDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }

            addThinkingMessage() {
                const chatContainer = document.getElementById('chat-container');
                const thinkingDiv = document.createElement('div');
                thinkingDiv.id = 'thinking-message';
                thinkingDiv.className = 'flex justify-start';
                thinkingDiv.innerHTML = `
                    <div class="ai-message text-gray-800 rounded-2xl px-4 py-3">
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-gray-600">🤖 AI助手正在思考</span>
                            <div class="flex space-x-1">
                                <div class="thinking-dot"></div>
                                <div class="thinking-dot"></div>
                                <div class="thinking-dot"></div>
                            </div>
                        </div>
                    </div>
                `;

                chatContainer.appendChild(thinkingDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }

            removeThinkingMessage() {
                const thinkingMessage = document.getElementById('thinking-message');
                if (thinkingMessage) {
                    thinkingMessage.remove();
                }
            }

            async callAPI(message) {
                const service = this.services[this.currentService];

                if (this.currentService === 'ollama') {
                    return await this.callOllamaAPI(message);
                } else if (this.currentService === 'lmstudio') {
                    return await this.callLMStudioAPI(message);
                } else {
                    throw new Error('不支持的服务类型');
                }
            }

            async callOllamaAPI(message) {
                const response = await fetch('http://localhost:11434/api/generate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        model: this.currentModel.name,
                        prompt: message,
                        stream: false
                    })
                });

                if (!response.ok) {
                    throw new Error(`Ollama API 错误: ${response.status}`);
                }

                const data = await response.json();
                return data.response || '抱歉，没有收到回复';
            }

            async callLMStudioAPI(message) {
                const response = await fetch('http://localhost:1234/v1/chat/completions', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        model: this.currentModel.name,
                        messages: [{ role: 'user', content: message }],
                        temperature: 0.7
                    })
                });

                if (!response.ok) {
                    throw new Error(`LM Studio API 错误: ${response.status}`);
                }

                const data = await response.json();
                return data.choices?.[0]?.message?.content || '抱歉，没有收到回复';
            }

            openSettings() {
                this.showNotification('设置功能开发中...', 'info');
            }
        }

        // 启动应用
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM加载完成，启动优化版聊天应用');
            try {
                window.optimizedChatApp = new OptimizedChatApp();
                console.log('应用启动成功');
            } catch (error) {
                console.error('应用启动失败:', error);
                document.getElementById('status-message').textContent = '启动失败: ' + error.message;
            }
        });

        console.log('脚本加载完成');
    </script>
</body>
</html>
