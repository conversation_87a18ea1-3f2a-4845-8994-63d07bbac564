<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI聊天助手 - 优化版</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .loading-screen {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #3b82f6, #1d4ed8, #3b82f6);
            background-size: 200% 100%;
            animation: progress-flow 2s linear infinite;
        }
        
        @keyframes progress-flow {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        .pulse-ring {
            animation: pulse-ring 1.25s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
        }
        
        .pulse-dot {
            animation: pulse-dot 1.25s cubic-bezier(0.455, 0.03, 0.515, 0.955) -.4s infinite;
        }
        
        @keyframes pulse-ring {
            0% { transform: scale(0.33); }
            80%, 100% { opacity: 0; }
        }
        
        @keyframes pulse-dot {
            0% { transform: scale(0.8); }
            50% { transform: scale(1); }
            100% { transform: scale(0.8); }
        }
        
        .fade-out {
            opacity: 0;
            transition: opacity 0.5s ease-out;
        }
        
        .status-online { background-color: #10b981; }
        .status-offline { background-color: #ef4444; }
        
        .service-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-weight: 600;
        }
        
        .service-ollama { background-color: #3b82f6; color: white; }
        .service-lmstudio { background-color: #10b981; color: white; }
        
        .user-message {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            margin-left: auto;
            margin-right: 0;
            max-width: 85%;
        }

        .ai-message {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
            color: #1e293b;
            margin-left: 0;
            margin-right: auto;
            max-width: 85%;
        }

        .message-container {
            position: relative;
            margin-bottom: 1rem;
        }

        .message-actions {
            opacity: 0;
            transition: opacity 0.2s ease;
            position: absolute;
            top: 8px;
            right: 8px;
            display: flex;
            gap: 4px;
        }

        .message-container:hover .message-actions {
            opacity: 1;
        }

        .action-btn {
            background: rgba(0, 0, 0, 0.1);
            border: none;
            border-radius: 4px;
            padding: 4px 6px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.2s ease;
        }

        .action-btn:hover {
            background: rgba(0, 0, 0, 0.2);
        }

        .user-message .action-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .user-message .action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .thinking-dot {
            width: 6px;
            height: 6px;
            background-color: #6b7280;
            border-radius: 50%;
            animation: thinking 1.4s infinite ease-in-out both;
        }

        .thinking-dot:nth-child(1) { animation-delay: -0.32s; }
        .thinking-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes thinking {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }

        .progress-ring {
            transform: rotate(-90deg);
        }

        .progress-ring-circle {
            transition: stroke-dashoffset 0.35s;
            transform-origin: 50% 50%;
        }
    </style>
</head>
<body>
    <!-- 加载屏幕 -->
    <div id="loading-screen" class="loading-screen fixed inset-0 flex flex-col items-center justify-center text-white z-50">
        <div class="text-center">
            <!-- Logo区域 -->
            <div class="mb-8">
                <div class="w-24 h-24 mx-auto mb-4 relative">
                    <div class="absolute inset-0 bg-white rounded-full pulse-ring"></div>
                    <div class="absolute inset-0 bg-white rounded-full pulse-dot flex items-center justify-center">
                        <i class="fas fa-robot text-4xl text-blue-600"></i>
                    </div>
                </div>
                <h1 class="text-4xl font-bold mb-2">AI聊天助手</h1>
                <p class="text-xl opacity-90">智能服务发现与初始化</p>
            </div>
            
            <!-- 进度条 -->
            <div class="w-80 mx-auto mb-8">
                <div class="flex justify-between text-sm mb-2">
                    <span>初始化进度</span>
                    <span id="progress-percent">0%</span>
                </div>
                <div class="w-full bg-white/20 rounded-full h-3">
                    <div id="progress-bar" class="progress-bar h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                </div>
            </div>
            
            <!-- 状态文本 -->
            <div id="status-message" class="text-lg mb-8 min-h-[2rem]">正在启动系统...</div>
            
            <!-- 信息卡片 -->
            <div class="grid grid-cols-2 gap-4 max-w-md mx-auto">
                <div class="bg-white/10 rounded-lg p-4 text-center">
                    <div class="text-2xl mb-2">🔍</div>
                    <div class="text-sm font-semibold">AI服务状态</div>
                    <div class="text-xs mt-1">
                        <div>Ollama: <span id="ollama-status" class="text-yellow-300">检测中...</span></div>
                        <div>LM Studio: <span id="lmstudio-status" class="text-yellow-300">检测中...</span></div>
                    </div>
                </div>
                <div class="bg-white/10 rounded-lg p-4 text-center">
                    <div class="text-2xl mb-2">🤖</div>
                    <div class="text-sm font-semibold">发现模型</div>
                    <div id="total-models" class="text-xs mt-1">0 个</div>
                </div>
                <div class="bg-white/10 rounded-lg p-4 text-center">
                    <div class="text-2xl mb-2">💻</div>
                    <div class="text-sm font-semibold">系统信息</div>
                    <div class="text-xs mt-1">
                        <div>OS: <span id="os-info">检测中...</span></div>
                        <div>浏览器: <span id="browser-info">检测中...</span></div>
                    </div>
                </div>
                <div class="bg-white/10 rounded-lg p-4 text-center">
                    <div class="text-2xl mb-2">⚙️</div>
                    <div class="text-sm font-semibold">配置状态</div>
                    <div class="text-xs mt-1">
                        <div>可用服务: <span id="available-services">0</span></div>
                        <div>网络: <span id="network-status">检测中...</span></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主应用界面 -->
    <div id="main-app" class="hidden min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <!-- 顶部状态栏 -->
        <div class="sticky top-0 bg-white/90 backdrop-blur-sm border-b border-gray-200 z-40">
            <div class="max-w-6xl mx-auto px-4 py-3">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2">
                            <div id="connection-indicator" class="w-3 h-3 rounded-full status-offline"></div>
                            <span id="connection-text" class="text-sm font-medium text-gray-700">未连接</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-gray-600">当前模型:</span>
                            <span id="current-model" class="text-sm font-semibold text-gray-800">未选择模型</span>
                            <span id="model-service-badge" class="service-badge hidden">未知</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button id="refresh-services" class="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button id="view-models" class="px-3 py-1 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            选择模型
                        </button>
                        <button id="settings-btn" class="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="max-w-6xl mx-auto p-4">
            <!-- 系统监控面板 -->
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3 mb-6">
                <!-- 第一行 - 基础信息 -->
                <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                    <div class="text-xs text-gray-500 mb-1">当前模型</div>
                    <div id="panel-current-model" class="text-sm font-semibold text-blue-600">未选择</div>
                    <div id="panel-model-size" class="text-xs text-gray-400 mt-1">--</div>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                    <div class="text-xs text-gray-500 mb-1">可用模型</div>
                    <div id="panel-total-models" class="text-sm font-semibold text-green-600">0</div>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                    <div class="text-xs text-gray-500 mb-1">活跃服务</div>
                    <div id="panel-active-services" class="text-sm font-semibold text-purple-600">0</div>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                    <div class="text-xs text-gray-500 mb-1">响应时间</div>
                    <div id="panel-response-time" class="text-sm font-semibold text-orange-600">--</div>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                    <div class="text-xs text-gray-500 mb-1">输出速度</div>
                    <div id="panel-output-speed" class="text-sm font-semibold text-red-600">--</div>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                    <div class="text-xs text-gray-500 mb-1">消息数量</div>
                    <div id="panel-message-count" class="text-sm font-semibold text-indigo-600">0</div>
                </div>

                <!-- 第二行 - 系统硬件信息 -->
                <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                    <div class="text-xs text-gray-500 mb-1">内存使用</div>
                    <div class="flex items-center space-x-2">
                        <div id="panel-memory-usage" class="text-sm font-semibold text-cyan-600">--</div>
                        <div class="flex-1 bg-gray-200 rounded-full h-2">
                            <div id="memory-progress" class="bg-cyan-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="text-xs text-gray-400 mt-1">JS堆内存</div>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                    <div class="text-xs text-gray-500 mb-1">CPU信息</div>
                    <div id="panel-cpu-info" class="text-sm font-semibold text-yellow-600">检测中...</div>
                    <div class="text-xs text-gray-400 mt-1">逻辑核心</div>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                    <div class="text-xs text-gray-500 mb-1">GPU信息</div>
                    <div id="panel-gpu-info" class="text-sm font-semibold text-pink-600">检测中...</div>
                    <div class="text-xs text-gray-400 mt-1">图形处理器</div>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                    <div class="text-xs text-gray-500 mb-1">设备信息</div>
                    <div id="panel-device-info" class="text-sm font-semibold text-emerald-600">检测中...</div>
                    <div class="text-xs text-gray-400 mt-1">操作系统</div>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                    <div class="text-xs text-gray-500 mb-1">网络状态</div>
                    <div id="panel-network-status" class="text-sm font-semibold text-teal-600">检测中...</div>
                    <div class="text-xs text-gray-400 mt-1">连接状态</div>
                </div>
                <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                    <div class="text-xs text-gray-500 mb-1">运行时间</div>
                    <div id="panel-uptime" class="text-sm font-semibold text-slate-600">--</div>
                    <div class="text-xs text-gray-400 mt-1">会话时长</div>
                </div>
            </div>

            <!-- 聊天界面 -->
            <div class="bg-white rounded-2xl shadow-lg overflow-hidden flex flex-col" style="height: calc(100vh - 280px);">
                <!-- 聊天消息区域 -->
                <div id="chat-container" class="flex-1 overflow-y-auto p-6 space-y-4 bg-gray-50">
                    <!-- 欢迎消息 -->
                    <div id="welcome-message" class="text-center py-8">
                        <div class="text-6xl mb-4">🤖</div>
                        <h2 class="text-2xl font-bold text-gray-800 mb-2">欢迎使用AI聊天助手</h2>
                        <p class="text-gray-600 mb-6">已发现 <span id="welcome-model-count" class="font-semibold text-blue-600">0</span> 个可用模型</p>
                        <div class="flex justify-center space-x-4">
                            <button id="quick-start" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-rocket mr-2"></i>快速开始
                            </button>
                            <button id="view-models-welcome" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-list mr-2"></i>查看模型
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 输入区域 -->
                <div class="border-t border-gray-200 p-4">
                    <!-- 文件预览区域 -->
                    <div id="file-preview-area" class="hidden mb-4 p-4 bg-white rounded-lg border border-gray-200">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">已选择文件:</span>
                            <button id="clear-files" class="text-red-500 hover:text-red-700 text-sm">
                                <i class="fas fa-times"></i> 清除
                            </button>
                        </div>
                        <div id="file-list" class="space-y-2"></div>
                    </div>

                    <div class="flex space-x-4">
                        <div class="flex space-x-2">
                            <!-- 文件上传按钮 -->
                            <label for="file-input" class="px-3 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors cursor-pointer flex items-center space-x-1" title="上传文件">
                                <i class="fas fa-paperclip text-sm"></i>
                                <span class="hidden sm:inline text-sm">附件</span>
                            </label>
                            <input type="file" id="file-input" multiple accept="image/*,.pdf,.txt,.doc,.docx,.json,.csv" class="hidden">

                            <!-- 图片上传按钮 -->
                            <label for="image-input" class="px-3 py-2 bg-green-200 text-green-700 rounded-lg hover:bg-green-300 transition-colors cursor-pointer flex items-center space-x-1" title="上传图片">
                                <i class="fas fa-image text-sm"></i>
                                <span class="hidden sm:inline text-sm">图片</span>
                            </label>
                            <input type="file" id="image-input" multiple accept="image/png,image/jpg,image/jpeg,image/gif,image/webp" class="hidden">
                        </div>

                        <textarea
                            id="user-input"
                            placeholder="输入您的消息..."
                            class="flex-1 resize-none border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            rows="1"
                        ></textarea>
                        <button
                            id="send-btn"
                            class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                        >
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>

                    <!-- 文件上传提示 -->
                    <div class="mt-2 text-xs text-gray-500">
                        支持图片、PDF、文档等格式，单个文件最大10MB
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('脚本开始执行');
        
        class OptimizedChatApp {
            constructor() {
                console.log('OptimizedChatApp 构造函数开始');
                
                // 基础配置
                this.services = {
                    ollama: { name: 'Ollama', url: 'http://localhost:11434', endpoint: '/api/tags' },
                    lmstudio: { name: 'LM Studio', url: 'http://localhost:1234', endpoint: '/v1/models' }
                };
                
                // 状态变量
                this.discoveredModels = [];
                this.activeServices = [];
                this.currentModel = null;
                this.currentService = null;
                this.stats = {
                    messageCount: 0,
                    lastResponseTime: 0,
                    lastOutputSpeed: 0
                };
                this.uploadedFiles = [];
                this.maxFileSize = 10 * 1024 * 1024; // 10MB
                
                console.log('构造函数完成，启动初始化');
                // 延迟启动，确保DOM完全准备好
                setTimeout(() => this.startInit(), 300);
            }

            async startInit() {
                console.log('开始初始化流程');

                const steps = [
                    { name: '检测系统信息', progress: 25, action: () => this.detectSystemInfo() },
                    { name: '扫描AI服务', progress: 50, action: () => this.scanServices() },
                    { name: '收集模型信息', progress: 75, action: () => this.collectModels() },
                    { name: '完成初始化', progress: 100, action: () => this.finishInit() }
                ];

                for (const step of steps) {
                    console.log(`执行步骤: ${step.name}`);
                    this.updateProgress(step.name, step.progress);

                    try {
                        await step.action();
                        await this.delay(1000);
                    } catch (error) {
                        console.error(`步骤失败: ${step.name}`, error);
                    }
                }

                this.completeInit();
            }

            updateProgress(message, progress) {
                console.log(`更新进度: ${progress}% - ${message}`);
                document.getElementById('status-message').textContent = message;
                document.getElementById('progress-percent').textContent = `${progress}%`;
                document.getElementById('progress-bar').style.width = `${progress}%`;
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }

            async detectSystemInfo() {
                console.log('检测系统信息');
                const os = navigator.userAgent.includes('Mac') ? 'macOS' :
                          navigator.userAgent.includes('Windows') ? 'Windows' : 'Linux';
                const browser = navigator.userAgent.includes('Chrome') ? 'Chrome' :
                               navigator.userAgent.includes('Firefox') ? 'Firefox' : 'Safari';

                document.getElementById('os-info').textContent = os;
                document.getElementById('browser-info').textContent = browser;
                document.getElementById('network-status').textContent = '正常';
            }

            async scanServices() {
                console.log('扫描AI服务');
                this.activeServices = [];

                const serviceChecks = Object.keys(this.services).map(async (serviceKey) => {
                    const service = this.services[serviceKey];
                    console.log(`检测服务: ${service.name}`);

                    try {
                        const response = await fetch(service.url + service.endpoint, {
                            method: 'GET',
                            headers: { 'Content-Type': 'application/json' }
                        });

                        if (response.ok) {
                            this.activeServices.push(serviceKey);
                            document.getElementById(`${serviceKey}-status`).textContent = '✅ 在线';
                            document.getElementById(`${serviceKey}-status`).className = 'text-green-300';
                            console.log(`✅ ${service.name} 在线`);
                            return { service: serviceKey, success: true };
                        } else {
                            throw new Error(`HTTP ${response.status}`);
                        }
                    } catch (error) {
                        document.getElementById(`${serviceKey}-status`).textContent = '❌ 离线';
                        document.getElementById(`${serviceKey}-status`).className = 'text-red-300';
                        console.log(`❌ ${service.name} 离线: ${error.message}`);
                        return { service: serviceKey, success: false };
                    }
                });

                await Promise.all(serviceChecks);
                document.getElementById('available-services').textContent = this.activeServices.length;
                console.log(`发现 ${this.activeServices.length} 个活跃服务`);
            }

            async collectModels() {
                console.log('收集模型信息');
                this.discoveredModels = [];

                for (const serviceKey of this.activeServices) {
                    const service = this.services[serviceKey];
                    console.log(`从 ${service.name} 获取模型列表`);

                    try {
                        const response = await fetch(service.url + service.endpoint);
                        if (response.ok) {
                            const data = await response.json();
                            const models = this.parseModels(data, serviceKey);
                            this.discoveredModels.push(...models);
                            console.log(`从 ${service.name} 发现 ${models.length} 个模型`);
                        }
                    } catch (error) {
                        console.error(`获取 ${service.name} 模型失败:`, error);
                    }
                }

                document.getElementById('total-models').textContent = `${this.discoveredModels.length} 个`;
                console.log(`总共发现 ${this.discoveredModels.length} 个模型`);
            }

            parseModels(data, serviceKey) {
                const service = this.services[serviceKey];
                const models = [];

                if (serviceKey === 'ollama' && data.models) {
                    data.models.forEach(model => {
                        models.push({
                            id: model.name,
                            name: model.name,
                            service: serviceKey,
                            serviceName: service.name,
                            size: model.size || 'Unknown'
                        });
                    });
                } else if (serviceKey === 'lmstudio' && data.data) {
                    data.data.forEach(model => {
                        models.push({
                            id: model.id,
                            name: model.id,
                            service: serviceKey,
                            serviceName: service.name,
                            size: 'Unknown'
                        });
                    });
                }

                return models;
            }

            async finishInit() {
                console.log('完成初始化设置');
                this.loadUserConfig();
                this.bindEvents();
                this.updateMainUI();
            }

            completeInit() {
                console.log('切换到主界面');
                document.getElementById('loading-screen').classList.add('fade-out');
                setTimeout(() => {
                    document.getElementById('loading-screen').style.display = 'none';
                    document.getElementById('main-app').classList.remove('hidden');
                    // 启动系统监控
                    this.startSystemMonitoring();
                }, 500);
            }

            loadUserConfig() {
                try {
                    const savedConfig = localStorage.getItem('optimized-chat-config');
                    if (savedConfig) {
                        const config = JSON.parse(savedConfig);
                        if (config.currentModel && this.discoveredModels.find(m => m.id === config.currentModel.id)) {
                            this.currentModel = config.currentModel;
                            this.currentService = config.currentService;
                        }
                    }
                } catch (error) {
                    console.error('加载配置失败:', error);
                }
            }

            bindEvents() {
                console.log('绑定事件监听器');

                // 刷新服务
                document.getElementById('refresh-services').addEventListener('click', () => this.refreshServices());

                // 查看模型
                document.getElementById('view-models').addEventListener('click', () => this.showModelList());
                document.getElementById('view-models-welcome').addEventListener('click', () => this.showModelList());

                // 快速开始
                document.getElementById('quick-start').addEventListener('click', () => this.quickStart());

                // 设置
                document.getElementById('settings-btn').addEventListener('click', () => this.openSettings());

                // 发送消息
                document.getElementById('send-btn').addEventListener('click', () => this.sendMessage());

                // 输入框事件
                const userInput = document.getElementById('user-input');
                userInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                // 文件上传事件
                try {
                    const fileInput = document.getElementById('file-input');
                    const imageInput = document.getElementById('image-input');
                    const clearFilesBtn = document.getElementById('clear-files');

                    if (fileInput) {
                        fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
                    }
                    if (imageInput) {
                        imageInput.addEventListener('change', (e) => this.handleFileUpload(e));
                    }
                    if (clearFilesBtn) {
                        clearFilesBtn.addEventListener('click', () => this.clearFiles());
                    }
                } catch (error) {
                    console.warn('文件上传事件监听器设置失败:', error);
                }

                userInput.addEventListener('input', () => {
                    userInput.style.height = 'auto';
                    userInput.style.height = Math.min(userInput.scrollHeight, 120) + 'px';
                });
            }

            updateMainUI() {
                console.log('更新主界面UI');

                // 更新连接状态
                const connectionIndicator = document.getElementById('connection-indicator');
                const connectionText = document.getElementById('connection-text');

                if (this.activeServices.length > 0) {
                    if (connectionIndicator) {
                        connectionIndicator.className = 'w-3 h-3 rounded-full status-online';
                    }
                    if (connectionText) {
                        connectionText.textContent = `已连接 ${this.activeServices.length} 个服务`;
                    }
                } else {
                    if (connectionIndicator) {
                        connectionIndicator.className = 'w-3 h-3 rounded-full status-offline';
                    }
                    if (connectionText) {
                        connectionText.textContent = '未连接';
                    }
                }

                // 更新模型信息
                const currentModelElement = document.getElementById('current-model');
                const badge = document.getElementById('model-service-badge');

                if (this.currentModel) {
                    if (currentModelElement) {
                        currentModelElement.textContent = this.currentModel.name;
                    }
                    if (badge) {
                        badge.textContent = this.currentModel.serviceName;
                        badge.className = `service-badge service-${this.currentModel.service}`;
                        badge.classList.remove('hidden');
                    }
                } else {
                    if (currentModelElement) {
                        currentModelElement.textContent = '未选择模型';
                    }
                    if (badge) {
                        badge.classList.add('hidden');
                    }
                }

                // 更新系统面板
                const panelElements = {
                    'panel-current-model': this.currentModel ? this.currentModel.name : '未选择',
                    'panel-total-models': this.discoveredModels.length,
                    'panel-active-services': this.activeServices.length,
                    'panel-response-time': this.stats.lastResponseTime ? `${this.stats.lastResponseTime}ms` : '--',
                    'panel-output-speed': this.stats.lastOutputSpeed ? `${this.stats.lastOutputSpeed.toFixed(1)} 字符/秒` : '--',
                    'panel-message-count': this.stats.messageCount
                };

                Object.entries(panelElements).forEach(([id, value]) => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.textContent = value;
                    }
                });

                // 更新模型相关信息
                const modelElements = {
                    'panel-model-size': this.currentModel ? this.getModelSize(this.currentModel.name) : '--',
                    'panel-model-status': this.currentModel ? '已加载' : '未加载',
                    'panel-model-memory': this.currentModel ? this.getModelMemoryUsage(this.currentModel.name) : '--'
                };

                Object.entries(modelElements).forEach(([id, value]) => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.textContent = value;
                    }
                });

                // 更新欢迎消息
                const welcomeModelCount = document.getElementById('welcome-model-count');
                if (welcomeModelCount) {
                    welcomeModelCount.textContent = this.discoveredModels.length;
                }
            }

            async refreshServices() {
                console.log('刷新服务');
                const refreshBtn = document.getElementById('refresh-services');
                const icon = refreshBtn.querySelector('i');

                icon.classList.add('fa-spin');
                refreshBtn.disabled = true;

                try {
                    await this.scanServices();
                    await this.collectModels();
                    this.updateMainUI();
                    this.showNotification('服务刷新完成', 'success');
                } catch (error) {
                    this.showNotification('刷新失败: ' + error.message, 'error');
                } finally {
                    icon.classList.remove('fa-spin');
                    refreshBtn.disabled = false;
                }
            }

            quickStart() {
                if (this.discoveredModels.length === 0) {
                    this.showNotification('未发现可用模型，请检查AI服务状态', 'warning');
                    return;
                }

                this.currentModel = this.discoveredModels[0];
                this.currentService = this.currentModel.service;
                this.saveConfig();
                this.updateMainUI();
                this.hideWelcomeMessage();
                this.showNotification(`已选择模型: ${this.currentModel.name}`, 'success');
            }

            showNotification(message, type = 'info') {
                console.log(`[${type.toUpperCase()}] ${message}`);
                // 这里可以添加更复杂的通知UI
            }

            hideWelcomeMessage() {
                const welcomeMessage = document.getElementById('welcome-message');
                if (welcomeMessage) {
                    welcomeMessage.style.display = 'none';
                }
            }

            saveConfig() {
                const config = {
                    currentModel: this.currentModel,
                    currentService: this.currentService,
                    lastUpdate: Date.now()
                };
                localStorage.setItem('optimized-chat-config', JSON.stringify(config));
            }

            showModelList() {
                if (this.discoveredModels.length === 0) {
                    this.showNotification('未发现可用模型', 'warning');
                    return;
                }

                const modelListHTML = this.discoveredModels.map(model => `
                    <div class="model-item p-3 border border-gray-200 rounded-lg hover:bg-blue-50 cursor-pointer" data-model-id="${model.id}">
                        <div class="flex items-center justify-between">
                            <div>
                                <div class="font-semibold text-gray-800">${model.name}</div>
                                <div class="text-sm text-gray-600">来源: ${model.serviceName}</div>
                            </div>
                            <span class="service-badge service-${model.service}">${model.serviceName}</span>
                        </div>
                    </div>
                `).join('');

                const modalHTML = `
                    <div id="model-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div class="bg-white rounded-2xl p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
                            <div class="flex items-center justify-between mb-4">
                                <h2 class="text-2xl font-bold text-gray-800">选择模型</h2>
                                <button id="close-modal" class="text-gray-500 hover:text-gray-700">
                                    <i class="fas fa-times text-xl"></i>
                                </button>
                            </div>
                            <div class="overflow-y-auto max-h-96 space-y-3">
                                ${modelListHTML}
                            </div>
                        </div>
                    </div>
                `;

                document.body.insertAdjacentHTML('beforeend', modalHTML);

                // 绑定事件
                document.getElementById('close-modal').addEventListener('click', () => {
                    document.getElementById('model-modal').remove();
                });

                document.querySelectorAll('.model-item').forEach(item => {
                    item.addEventListener('click', () => {
                        const modelId = item.dataset.modelId;
                        const selectedModel = this.discoveredModels.find(m => m.id === modelId);
                        if (selectedModel) {
                            this.currentModel = selectedModel;
                            this.currentService = selectedModel.service;
                            this.saveConfig();
                            this.updateMainUI();
                            this.hideWelcomeMessage();
                            this.showNotification(`已选择模型: ${selectedModel.name}`, 'success');
                        }
                        document.getElementById('model-modal').remove();
                    });
                });
            }

            async sendMessage() {
                const userInput = document.getElementById('user-input');
                const message = userInput.value.trim();

                if (!message && this.uploadedFiles.length === 0) return;
                if (!this.currentModel) {
                    this.showNotification('请先选择一个模型', 'warning');
                    return;
                }

                // 构建消息内容
                let messageContent = message;
                let hasImages = false;

                // 处理上传的文件
                if (this.uploadedFiles.length > 0) {
                    const fileDescriptions = [];

                    for (const fileData of this.uploadedFiles) {
                        if (fileData.file.type.startsWith('image/')) {
                            hasImages = true;
                            fileDescriptions.push(`[图片: ${fileData.name}]`);
                        } else {
                            fileDescriptions.push(`[文件: ${fileData.name} (${this.formatFileSize(fileData.size)})]`);
                        }
                    }

                    if (fileDescriptions.length > 0) {
                        messageContent = `${message}\n\n附件:\n${fileDescriptions.join('\n')}`;
                    }
                }

                // 检查模型是否支持多模态
                if (hasImages && !this.isMultimodalModel(this.currentModel.name)) {
                    this.showNotification('当前模型不支持图片分析，请选择支持多模态的模型', 'warning');
                    return;
                }

                userInput.value = '';
                userInput.style.height = 'auto';

                this.hideWelcomeMessage();
                this.addMessage('user', messageContent);
                this.addThinkingMessage();

                const startTime = Date.now();

                try {
                    const response = await this.callAPI(message, this.uploadedFiles);
                    const responseTime = Date.now() - startTime;

                    this.removeThinkingMessage();
                    this.addMessage('ai', response);

                    // 更新统计
                    this.stats.messageCount++;
                    this.stats.lastResponseTime = responseTime;
                    this.stats.lastOutputSpeed = response.length / (responseTime / 1000);
                    this.updateMainUI();

                    // 清除已发送的文件
                    this.clearFiles();

                } catch (error) {
                    this.removeThinkingMessage();
                    this.addMessage('ai', `抱歉，发生了错误: ${error.message}`);
                    this.showNotification('发送失败: ' + error.message, 'error');
                }
            }

            isMultimodalModel(modelName) {
                const multimodalKeywords = ['vision', 'vl', 'visual', 'multimodal', 'qwen2.5-vl', 'llava', 'gpt-4-vision'];
                return multimodalKeywords.some(keyword => modelName.toLowerCase().includes(keyword));
            }

            addMessage(sender, content) {
                const chatContainer = document.getElementById('chat-container');
                const messageId = 'msg-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message-container w-full mb-4';
                messageDiv.id = messageId;

                if (sender === 'user') {
                    messageDiv.innerHTML = `
                        <div class="user-message rounded-2xl px-6 py-4 relative">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm font-medium opacity-90">👤 您</span>
                                    <span class="text-xs opacity-70">${new Date().toLocaleTimeString()}</span>
                                </div>
                            </div>
                            <div class="message-content text-white leading-relaxed">${content}</div>
                            <div class="message-actions">
                                <button class="action-btn" onclick="copyMessage('${messageId}')" title="复制">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="action-btn" onclick="editMessage('${messageId}')" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn" onclick="deleteMessage('${messageId}')" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `;
                } else {
                    messageDiv.innerHTML = `
                        <div class="ai-message rounded-2xl px-6 py-4 relative">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm font-medium text-gray-700">🤖 AI助手</span>
                                    <span class="text-xs text-gray-500">${new Date().toLocaleTimeString()}</span>
                                </div>
                            </div>
                            <div class="message-content text-gray-800 leading-relaxed">${content}</div>
                            <div class="message-actions">
                                <button class="action-btn" onclick="copyMessage('${messageId}')" title="复制">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="action-btn" onclick="regenerateMessage('${messageId}')" title="重新生成">
                                    <i class="fas fa-redo"></i>
                                </button>
                                <button class="action-btn" onclick="deleteMessage('${messageId}')" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `;
                }

                chatContainer.appendChild(messageDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;
                return messageId;
            }

            addThinkingMessage() {
                const chatContainer = document.getElementById('chat-container');
                const thinkingDiv = document.createElement('div');
                thinkingDiv.id = 'thinking-message';
                thinkingDiv.className = 'flex justify-start';
                thinkingDiv.innerHTML = `
                    <div class="ai-message text-gray-800 rounded-2xl px-4 py-3">
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-gray-600">🤖 AI助手正在思考</span>
                            <div class="flex space-x-1">
                                <div class="thinking-dot"></div>
                                <div class="thinking-dot"></div>
                                <div class="thinking-dot"></div>
                            </div>
                        </div>
                    </div>
                `;

                chatContainer.appendChild(thinkingDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }

            removeThinkingMessage() {
                const thinkingMessage = document.getElementById('thinking-message');
                if (thinkingMessage) {
                    thinkingMessage.remove();
                }
            }

            async callAPI(message) {
                const service = this.services[this.currentService];

                if (this.currentService === 'ollama') {
                    return await this.callOllamaAPI(message);
                } else if (this.currentService === 'lmstudio') {
                    return await this.callLMStudioAPI(message);
                } else {
                    throw new Error('不支持的服务类型');
                }
            }

            async callOllamaAPI(message) {
                const response = await fetch('http://localhost:11434/api/generate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        model: this.currentModel.name,
                        prompt: message,
                        stream: false
                    })
                });

                if (!response.ok) {
                    throw new Error(`Ollama API 错误: ${response.status}`);
                }

                const data = await response.json();
                return data.response || '抱歉，没有收到回复';
            }

            async callLMStudioAPI(message) {
                const response = await fetch('http://localhost:1234/v1/chat/completions', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        model: this.currentModel.name,
                        messages: [{ role: 'user', content: message }],
                        temperature: 0.7
                    })
                });

                if (!response.ok) {
                    throw new Error(`LM Studio API 错误: ${response.status}`);
                }

                const data = await response.json();
                return data.choices?.[0]?.message?.content || '抱歉，没有收到回复';
            }

            openSettings() {
                console.log('打开设置界面');
                this.showSettingsModal();
            }

            showSettingsModal() {
                const modalHTML = `
                    <div id="settings-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div class="bg-white rounded-2xl p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
                            <div class="flex items-center justify-between mb-6">
                                <h2 class="text-2xl font-bold text-gray-800">系统设置</h2>
                                <button id="close-settings" class="text-gray-500 hover:text-gray-700">
                                    <i class="fas fa-times text-xl"></i>
                                </button>
                            </div>

                            <div class="space-y-6">
                                <!-- 服务配置 -->
                                <div class="border-b border-gray-200 pb-4">
                                    <h3 class="text-lg font-semibold text-gray-700 mb-3">AI服务配置</h3>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600 mb-2">Ollama 地址</label>
                                            <input id="ollama-url" type="text" value="http://localhost:11434"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600 mb-2">LM Studio 地址</label>
                                            <input id="lmstudio-url" type="text" value="http://localhost:1234"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                    </div>
                                </div>

                                <!-- 聊天设置 -->
                                <div class="border-b border-gray-200 pb-4">
                                    <h3 class="text-lg font-semibold text-gray-700 mb-3">聊天设置</h3>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600 mb-2">温度 (Temperature)</label>
                                            <input id="temperature" type="range" min="0" max="1" step="0.1" value="0.7"
                                                   class="w-full">
                                            <div class="text-sm text-gray-500 mt-1">当前值: <span id="temperature-value">0.7</span></div>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-600 mb-2">最大令牌数</label>
                                            <input id="max-tokens" type="number" value="2048" min="100" max="8192"
                                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                    </div>
                                </div>

                                <!-- 界面设置 -->
                                <div class="pb-4">
                                    <h3 class="text-lg font-semibold text-gray-700 mb-3">界面设置</h3>
                                    <div class="space-y-3">
                                        <label class="flex items-center">
                                            <input id="auto-scroll" type="checkbox" checked class="mr-2">
                                            <span class="text-sm text-gray-600">自动滚动到最新消息</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input id="show-timestamps" type="checkbox" checked class="mr-2">
                                            <span class="text-sm text-gray-600">显示消息时间戳</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input id="enable-sound" type="checkbox" class="mr-2">
                                            <span class="text-sm text-gray-600">启用消息提示音</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                                <button id="reset-settings" class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors">
                                    重置默认
                                </button>
                                <button id="test-connection-settings" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                    测试连接
                                </button>
                                <button id="save-settings" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                    保存设置
                                </button>
                            </div>
                        </div>
                    </div>
                `;

                document.body.insertAdjacentHTML('beforeend', modalHTML);

                // 绑定设置界面事件
                this.bindSettingsEvents();

                // 加载当前设置
                this.loadSettingsValues();
            }

            bindSettingsEvents() {
                // 关闭设置
                document.getElementById('close-settings').addEventListener('click', () => {
                    document.getElementById('settings-modal').remove();
                });

                // 温度滑块
                const temperatureSlider = document.getElementById('temperature');
                const temperatureValue = document.getElementById('temperature-value');
                temperatureSlider.addEventListener('input', (e) => {
                    temperatureValue.textContent = e.target.value;
                });

                // 保存设置
                document.getElementById('save-settings').addEventListener('click', () => {
                    this.saveSettingsValues();
                    document.getElementById('settings-modal').remove();
                    this.showNotification('设置已保存', 'success');
                });

                // 重置设置
                document.getElementById('reset-settings').addEventListener('click', () => {
                    this.resetSettingsToDefault();
                });

                // 测试连接
                document.getElementById('test-connection-settings').addEventListener('click', () => {
                    this.testConnectionFromSettings();
                });
            }

            loadSettingsValues() {
                try {
                    const settings = JSON.parse(localStorage.getItem('optimized-chat-settings') || '{}');

                    if (settings.ollamaUrl) document.getElementById('ollama-url').value = settings.ollamaUrl;
                    if (settings.lmstudioUrl) document.getElementById('lmstudio-url').value = settings.lmstudioUrl;
                    if (settings.temperature !== undefined) {
                        document.getElementById('temperature').value = settings.temperature;
                        document.getElementById('temperature-value').textContent = settings.temperature;
                    }
                    if (settings.maxTokens) document.getElementById('max-tokens').value = settings.maxTokens;
                    if (settings.autoScroll !== undefined) document.getElementById('auto-scroll').checked = settings.autoScroll;
                    if (settings.showTimestamps !== undefined) document.getElementById('show-timestamps').checked = settings.showTimestamps;
                    if (settings.enableSound !== undefined) document.getElementById('enable-sound').checked = settings.enableSound;
                } catch (error) {
                    console.error('加载设置失败:', error);
                }
            }

            saveSettingsValues() {
                const settings = {
                    ollamaUrl: document.getElementById('ollama-url').value,
                    lmstudioUrl: document.getElementById('lmstudio-url').value,
                    temperature: parseFloat(document.getElementById('temperature').value),
                    maxTokens: parseInt(document.getElementById('max-tokens').value),
                    autoScroll: document.getElementById('auto-scroll').checked,
                    showTimestamps: document.getElementById('show-timestamps').checked,
                    enableSound: document.getElementById('enable-sound').checked
                };

                localStorage.setItem('optimized-chat-settings', JSON.stringify(settings));

                // 更新服务URL
                this.services.ollama.url = settings.ollamaUrl;
                this.services.lmstudio.url = settings.lmstudioUrl;
            }

            resetSettingsToDefault() {
                document.getElementById('ollama-url').value = 'http://localhost:11434';
                document.getElementById('lmstudio-url').value = 'http://localhost:1234';
                document.getElementById('temperature').value = '0.7';
                document.getElementById('temperature-value').textContent = '0.7';
                document.getElementById('max-tokens').value = '2048';
                document.getElementById('auto-scroll').checked = true;
                document.getElementById('show-timestamps').checked = true;
                document.getElementById('enable-sound').checked = false;
            }

            async testConnectionFromSettings() {
                const testBtn = document.getElementById('test-connection-settings');
                const originalText = testBtn.textContent;
                testBtn.textContent = '测试中...';
                testBtn.disabled = true;

                try {
                    const ollamaUrl = document.getElementById('ollama-url').value;
                    const lmstudioUrl = document.getElementById('lmstudio-url').value;

                    const results = [];

                    // 测试 Ollama
                    try {
                        const ollamaResponse = await fetch(ollamaUrl + '/api/tags');
                        if (ollamaResponse.ok) {
                            results.push('✅ Ollama 连接成功');
                        } else {
                            results.push('❌ Ollama 连接失败');
                        }
                    } catch (error) {
                        results.push('❌ Ollama 连接失败: ' + error.message);
                    }

                    // 测试 LM Studio
                    try {
                        const lmstudioResponse = await fetch(lmstudioUrl + '/v1/models');
                        if (lmstudioResponse.ok) {
                            results.push('✅ LM Studio 连接成功');
                        } else {
                            results.push('❌ LM Studio 连接失败');
                        }
                    } catch (error) {
                        results.push('❌ LM Studio 连接失败: ' + error.message);
                    }

                    alert(results.join('\n'));

                } catch (error) {
                    alert('测试连接时发生错误: ' + error.message);
                } finally {
                    testBtn.textContent = originalText;
                    testBtn.disabled = false;
                }
            }

            // 启动系统监控
            startSystemMonitoring() {
                this.startTime = Date.now();

                // 初始化系统硬件信息检测
                this.detectSystemHardware();

                // 每1秒更新一次系统信息（降低频率，避免过度消耗资源）
                setInterval(() => {
                    this.updateSystemMetrics();
                }, 1000);

                // 每1秒更新一次网络延迟
                setInterval(() => {
                    this.updateNetworkLatency();
                }, 1000);

                // 每秒更新运行时间
                setInterval(() => {
                    this.updateUptime();
                }, 1000);
            }

            async detectSystemHardware() {
                try {
                    // 检测基础系统信息
                    const systemInfo = {
                        platform: navigator.platform,
                        userAgent: navigator.userAgent,
                        language: navigator.language,
                        cookieEnabled: navigator.cookieEnabled,
                        onLine: navigator.onLine
                    };

                    // 尝试获取内存信息（仅在支持的浏览器中）
                    if ('memory' in performance) {
                        const memory = performance.memory;
                        const usedMemory = (memory.usedJSHeapSize / 1024 / 1024).toFixed(1);
                        const totalMemory = (memory.totalJSHeapSize / 1024 / 1024).toFixed(1);
                        const limitMemory = (memory.jsHeapSizeLimit / 1024 / 1024).toFixed(1);

                        const memoryElement = document.getElementById('panel-memory-usage');
                        const progressElement = document.getElementById('memory-progress');

                        if (memoryElement) {
                            memoryElement.textContent = `${usedMemory}MB`;
                            const parentElement = memoryElement.parentElement;
                            if (parentElement) {
                                const textElement = parentElement.querySelector('.text-xs');
                                if (textElement) {
                                    textElement.textContent = `/ ${limitMemory}MB`;
                                }
                            }
                        }

                        if (progressElement) {
                            const percentage = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
                            progressElement.style.width = `${percentage}%`;
                        }
                    } else {
                        // 隐藏内存监控项
                        this.hideMonitoringCard('panel-memory-usage');
                    }

                    // 尝试获取CPU核心数
                    if ('hardwareConcurrency' in navigator) {
                        const cores = navigator.hardwareConcurrency;
                        const cpuElement = document.getElementById('panel-cpu-info');
                        if (cpuElement) {
                            cpuElement.textContent = `${cores}核心`;
                        }
                    } else {
                        this.hideMonitoringCard('panel-cpu-info');
                    }

                    // 检测GPU信息（通过WebGL）
                    this.detectGPUInfo();

                    // 检测计算机名称（有限支持）
                    this.detectComputerName();

                } catch (error) {
                    console.warn('系统硬件检测失败:', error);
                }
            }

            detectGPUInfo() {
                try {
                    const canvas = document.createElement('canvas');
                    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

                    const gpuElement = document.getElementById('panel-gpu-info');
                    if (!gpuElement) {
                        console.warn('GPU信息元素未找到');
                        return;
                    }

                    if (gl) {
                        const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
                        if (debugInfo) {
                            const vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
                            const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
                            gpuElement.textContent = `${vendor} ${renderer}`.substring(0, 30);
                        } else {
                            gpuElement.textContent = 'WebGL支持';
                        }
                    } else {
                        this.hideMonitoringCard('panel-gpu-info');
                    }
                } catch (error) {
                    console.warn('GPU信息检测失败:', error);
                    this.hideMonitoringCard('panel-gpu-info');
                }
            }

            detectComputerName() {
                // 浏览器环境下无法直接获取计算机名，显示用户代理信息
                const userAgent = navigator.userAgent;
                let deviceInfo = '未知设备';

                if (userAgent.includes('Windows')) {
                    deviceInfo = 'Windows设备';
                } else if (userAgent.includes('Mac')) {
                    deviceInfo = 'Mac设备';
                } else if (userAgent.includes('Linux')) {
                    deviceInfo = 'Linux设备';
                } else if (userAgent.includes('Android')) {
                    deviceInfo = 'Android设备';
                } else if (userAgent.includes('iPhone') || userAgent.includes('iPad')) {
                    deviceInfo = 'iOS设备';
                }

                document.getElementById('panel-device-info').textContent = deviceInfo;
            }

            hideMonitoringCard(elementId) {
                try {
                    const element = document.getElementById(elementId);
                    if (element) {
                        const card = element.closest('.bg-white');
                        if (card) {
                            card.style.display = 'none';
                        }
                    } else {
                        console.warn(`监控卡片元素未找到: ${elementId}`);
                    }
                } catch (error) {
                    console.warn(`隐藏监控卡片失败: ${elementId}`, error);
                }
            }

            updateSystemMetrics() {
                // 只更新可以真实获取的数据
                try {
                    // 更新内存使用（如果支持）
                    if ('memory' in performance) {
                        const memory = performance.memory;
                        const usedMemory = (memory.usedJSHeapSize / 1024 / 1024).toFixed(1);
                        const limitMemory = (memory.jsHeapSizeLimit / 1024 / 1024).toFixed(1);

                        const memoryElement = document.getElementById('panel-memory-usage');
                        const progressElement = document.getElementById('memory-progress');

                        if (memoryElement) {
                            memoryElement.textContent = `${usedMemory}MB`;
                        }

                        if (progressElement) {
                            const percentage = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
                            progressElement.style.width = `${percentage.toFixed(1)}%`;
                        }
                    }

                    // 更新网络状态
                    const networkElement = document.getElementById('panel-network-status');
                    if (networkElement) {
                        networkElement.textContent = navigator.onLine ? '在线' : '离线';
                    }

                } catch (error) {
                    console.warn('系统指标更新失败:', error);
                }
            }

            updateUptime() {
                const uptime = Math.floor((Date.now() - this.startTime) / 1000);
                const hours = Math.floor(uptime / 3600);
                const minutes = Math.floor((uptime % 3600) / 60);
                const seconds = uptime % 60;
                document.getElementById('panel-uptime').textContent =
                    `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }

            getModelSize(modelName) {
                // 根据模型名称推断参数量
                const name = modelName.toLowerCase();
                if (name.includes('7b')) return '7B';
                if (name.includes('13b')) return '13B';
                if (name.includes('30b')) return '30B';
                if (name.includes('70b')) return '70B';
                if (name.includes('3b')) return '3B';
                if (name.includes('1b')) return '1B';
                if (name.includes('small')) return '小型';
                if (name.includes('large')) return '大型';
                return '未知';
            }

            getModelMemoryUsage(modelName) {
                // 根据模型大小估算内存使用
                const size = this.getModelSize(modelName);
                switch(size) {
                    case '1B': return '~2GB';
                    case '3B': return '~6GB';
                    case '7B': return '~14GB';
                    case '13B': return '~26GB';
                    case '30B': return '~60GB';
                    case '70B': return '~140GB';
                    default: return '~4GB';
                }
            }

            async updateNetworkLatency() {
                if (this.currentService && this.services[this.currentService]) {
                    const startTime = Date.now();
                    try {
                        const response = await fetch(this.services[this.currentService].url + '/api/tags', {
                            method: 'HEAD',
                            mode: 'no-cors'
                        });
                        const latency = Date.now() - startTime;
                        const latencyElement = document.getElementById('panel-network-latency');
                        if (latencyElement) {
                            latencyElement.textContent = `${latency}ms`;
                        }
                    } catch (error) {
                        const latencyElement = document.getElementById('panel-network-latency');
                        if (latencyElement) {
                            latencyElement.textContent = '超时';
                        }
                    }
                }
            }

            // 文件上传处理
            handleFileUpload(event) {
                const files = Array.from(event.target.files);

                for (const file of files) {
                    // 检查文件大小
                    if (file.size > this.maxFileSize) {
                        this.showNotification(`文件 "${file.name}" 超过10MB限制`, 'error');
                        continue;
                    }

                    // 检查文件类型
                    if (!this.isValidFileType(file)) {
                        this.showNotification(`不支持的文件类型: ${file.name}`, 'error');
                        continue;
                    }

                    // 添加到上传列表
                    this.uploadedFiles.push({
                        file: file,
                        id: Date.now() + Math.random(),
                        name: file.name,
                        size: file.size,
                        type: file.type,
                        preview: null
                    });
                }

                // 清空input
                event.target.value = '';

                // 更新预览
                this.updateFilePreview();
            }

            isValidFileType(file) {
                const validTypes = [
                    'image/png', 'image/jpg', 'image/jpeg', 'image/gif', 'image/webp',
                    'application/pdf', 'text/plain', 'text/csv',
                    'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'application/json'
                ];
                return validTypes.includes(file.type) || file.name.toLowerCase().endsWith('.txt');
            }

            async updateFilePreview() {
                const previewArea = document.getElementById('file-preview-area');
                const fileList = document.getElementById('file-list');

                if (this.uploadedFiles.length === 0) {
                    previewArea.classList.add('hidden');
                    return;
                }

                previewArea.classList.remove('hidden');
                fileList.innerHTML = '';

                for (const fileData of this.uploadedFiles) {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'flex items-center space-x-3 p-2 bg-gray-50 rounded-lg';

                    // 文件图标或预览
                    let preview = '';
                    if (fileData.file.type.startsWith('image/')) {
                        const imageUrl = await this.createImagePreview(fileData.file);
                        preview = `<img src="${imageUrl}" class="w-12 h-12 object-cover rounded border">`;
                    } else {
                        const icon = this.getFileIcon(fileData.file.type);
                        preview = `<div class="w-12 h-12 flex items-center justify-center bg-blue-100 rounded border">
                                    <i class="${icon} text-blue-600"></i>
                                   </div>`;
                    }

                    fileItem.innerHTML = `
                        ${preview}
                        <div class="flex-1">
                            <div class="text-sm font-medium text-gray-900">${fileData.name}</div>
                            <div class="text-xs text-gray-500">${this.formatFileSize(fileData.size)}</div>
                        </div>
                        <button onclick="window.optimizedChatApp.removeFile('${fileData.id}')"
                                class="text-red-500 hover:text-red-700">
                            <i class="fas fa-times"></i>
                        </button>
                    `;

                    fileList.appendChild(fileItem);
                }
            }

            createImagePreview(file) {
                return new Promise((resolve) => {
                    const reader = new FileReader();
                    reader.onload = (e) => resolve(e.target.result);
                    reader.readAsDataURL(file);
                });
            }

            getFileIcon(fileType) {
                if (fileType.includes('pdf')) return 'fas fa-file-pdf';
                if (fileType.includes('word') || fileType.includes('document')) return 'fas fa-file-word';
                if (fileType.includes('text') || fileType.includes('csv')) return 'fas fa-file-alt';
                if (fileType.includes('json')) return 'fas fa-file-code';
                return 'fas fa-file';
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            removeFile(fileId) {
                this.uploadedFiles = this.uploadedFiles.filter(f => f.id !== fileId);
                this.updateFilePreview();
            }

            clearFiles() {
                this.uploadedFiles = [];
                this.updateFilePreview();
            }
        }

        // 全局消息操作函数
        function copyMessage(messageId) {
            const messageElement = document.getElementById(messageId);
            const content = messageElement.querySelector('.message-content').textContent;
            navigator.clipboard.writeText(content).then(() => {
                showToast('消息已复制到剪贴板');
            });
        }

        function editMessage(messageId) {
            const messageElement = document.getElementById(messageId);
            const contentElement = messageElement.querySelector('.message-content');
            const currentContent = contentElement.textContent;

            const newContent = prompt('编辑消息:', currentContent);
            if (newContent && newContent !== currentContent) {
                contentElement.textContent = newContent;
                showToast('消息已更新');
            }
        }

        function deleteMessage(messageId) {
            if (confirm('确定要删除这条消息吗？')) {
                const messageElement = document.getElementById(messageId);
                messageElement.remove();
                showToast('消息已删除');
            }
        }

        function regenerateMessage(messageId) {
            const messageElement = document.getElementById(messageId);
            const contentElement = messageElement.querySelector('.message-content');

            // 找到上一条用户消息
            let prevElement = messageElement.previousElementSibling;
            while (prevElement && !prevElement.querySelector('.user-message')) {
                prevElement = prevElement.previousElementSibling;
            }

            if (prevElement) {
                const userMessage = prevElement.querySelector('.message-content').textContent;
                contentElement.innerHTML = '<div class="flex items-center space-x-2"><span>🔄 正在重新生成...</span></div>';

                // 重新调用API
                if (window.optimizedChatApp) {
                    window.optimizedChatApp.callAPI(userMessage).then(response => {
                        contentElement.textContent = response;
                        showToast('回复已重新生成');
                    }).catch(error => {
                        contentElement.textContent = '重新生成失败: ' + error.message;
                    });
                }
            }
        }

        function showToast(message) {
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-gray-800 text-white px-4 py-2 rounded-lg shadow-lg z-50';
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        // 启动应用
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM加载完成，启动优化版聊天应用');
            try {
                window.optimizedChatApp = new OptimizedChatApp();
                console.log('应用启动成功');
            } catch (error) {
                console.error('应用启动失败:', error);
                document.getElementById('status-message').textContent = '启动失败: ' + error.message;
            }
        });

        console.log('脚本加载完成');
    </script>
</body>
</html>
