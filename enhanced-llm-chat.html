<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强版本地LLM聊天界面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/9.1.6/marked.min.js"></script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        /* 基础样式 */
        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        /* PC端对话历史 - 右侧浮动面板 */
        .history-panel {
            position: fixed;
            top: 80px;
            right: -350px;
            width: 350px;
            height: calc(100vh - 100px);
            background: white;
            border-radius: 15px 0 0 15px;
            box-shadow: -5px 0 20px rgba(0,0,0,0.1);
            transition: right 0.3s ease-in-out;
            z-index: 1000;
            overflow: hidden;
        }

        .history-panel.open {
            right: 0;
        }

        .history-toggle {
            position: fixed;
            top: 50%;
            right: 10px;
            transform: translateY(-50%);
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
            z-index: 1001;
        }

        .history-toggle:hover {
            background: #2563eb;
            transform: translateY(-50%) scale(1.1);
        }

        /* 模型状态栏 */
        .model-status-bar {
            position: sticky;
            top: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid #e5e7eb;
            padding: 12px 20px;
            z-index: 100;
        }

        /* 搜索功能样式 */
        .search-toggle {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .search-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .search-toggle.active {
            background: linear-gradient(135deg, #059669, #047857);
        }

        /* 流式输出光标 */
        .streaming-cursor {
            display: inline-block;
            width: 2px;
            height: 1.2em;
            background-color: #3b82f6;
            animation: blink 1s infinite;
            margin-left: 2px;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        /* 思考指示器 */
        .thinking-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            background: #f3f4f6;
            border-radius: 12px;
            margin: 8px 0;
        }

        .thinking-dots {
            display: flex;
            gap: 4px;
        }

        .thinking-dot {
            width: 8px;
            height: 8px;
            background: #6b7280;
            border-radius: 50%;
            animation: thinking 1.4s ease-in-out infinite both;
        }

        .thinking-dot:nth-child(1) { animation-delay: -0.32s; }
        .thinking-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes thinking {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1.2); opacity: 1; }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .history-panel {
                position: fixed;
                top: 0;
                right: -100%;
                width: 100%;
                height: 100vh;
                border-radius: 0;
            }
            
            .history-toggle {
                right: 20px;
                top: 20px;
                transform: none;
            }
        }

        /* 模型大小标签 */
        .model-size-badge {
            display: inline-block;
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            margin-left: 8px;
        }

        /* 搜索结果样式 */
        .search-result {
            background: #f0f9ff;
            border-left: 4px solid #0ea5e9;
            padding: 12px;
            margin: 8px 0;
            border-radius: 0 8px 8px 0;
        }

        .search-source {
            font-size: 12px;
            color: #0ea5e9;
            text-decoration: none;
            font-weight: 500;
        }

        .search-source:hover {
            text-decoration: underline;
        }

        /* 消息样式 */
        .message-container {
            max-width: 100%;
            margin: 16px 0;
        }

        .user-message {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            margin-left: auto;
            margin-right: 0;
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px 18px 4px 18px;
            word-wrap: break-word;
        }

        .ai-message {
            background: white;
            color: #1f2937;
            margin-left: 0;
            margin-right: auto;
            max-width: 85%;
            padding: 16px;
            border-radius: 18px 18px 18px 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            word-wrap: break-word;
        }

        /* 消息操作按钮 */
        .message-actions {
            opacity: 0;
            transition: opacity 0.2s ease;
            display: flex;
            gap: 8px;
            margin-top: 8px;
        }

        .message-container:hover .message-actions {
            opacity: 1;
        }

        .action-btn {
            background: #f3f4f6;
            border: none;
            padding: 6px 12px;
            border-radius: 16px;
            cursor: pointer;
            font-size: 12px;
            color: #6b7280;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background: #e5e7eb;
            color: #374151;
        }
    </style>
</head>
<body>
    <!-- 模型状态栏 -->
    <div class="model-status-bar">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 rounded-full bg-green-500"></div>
                    <span class="font-medium text-gray-700">当前模型:</span>
                    <span id="current-model-name" class="text-blue-600 font-semibold">未选择</span>
                    <span id="current-model-size" class="model-size-badge">0B</span>
                </div>
                <div class="flex items-center gap-2 text-sm text-gray-500">
                    <i class="fas fa-clock"></i>
                    <span id="response-time">0ms</span>
                    <i class="fas fa-tachometer-alt ml-2"></i>
                    <span id="output-speed">0 字/秒</span>
                </div>
            </div>
            <div class="flex items-center gap-3">
                <button id="search-toggle" class="search-toggle">
                    <i class="fas fa-globe mr-2"></i>
                    <span id="search-status">联网搜索: 关闭</span>
                </button>
                <button id="settings-btn" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                    <i class="fas fa-cog mr-2"></i>设置
                </button>
            </div>
        </div>
    </div>

    <!-- 主聊天区域 -->
    <div class="flex flex-col h-screen pt-16">
        <!-- 聊天消息区域 -->
        <div id="chat-messages" class="flex-1 overflow-y-auto px-4 py-6 space-y-4">
            <div class="text-center text-gray-500 py-8">
                <i class="fas fa-robot text-4xl mb-4"></i>
                <p class="text-lg">开始您的AI对话</p>
                <p class="text-sm mt-2">选择模型并发送消息开始聊天</p>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="border-t bg-white p-4">
            <div class="max-w-4xl mx-auto">
                <div class="flex items-end gap-3">
                    <div class="flex-1">
                        <div class="relative">
                            <textarea 
                                id="message-input" 
                                placeholder="输入您的消息..." 
                                class="w-full resize-none border border-gray-300 rounded-lg px-4 py-3 pr-12 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                rows="1"
                                style="min-height: 44px; max-height: 120px;"
                            ></textarea>
                            <button id="file-upload-btn" class="absolute right-3 top-3 text-gray-400 hover:text-gray-600">
                                <i class="fas fa-paperclip"></i>
                            </button>
                            <input type="file" id="file-input" class="hidden" multiple accept="image/*,text/*,.pdf,.doc,.docx">
                        </div>
                    </div>
                    <button 
                        id="send-btn" 
                        class="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                    >
                        <i class="fas fa-paper-plane"></i>
                    </button>
                    <button 
                        id="stop-btn" 
                        class="px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 hidden transition-colors"
                    >
                        <i class="fas fa-stop"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 对话历史面板 -->
    <div id="history-panel" class="history-panel">
        <div class="h-full flex flex-col">
            <!-- 历史面板头部 -->
            <div class="p-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-800">对话历史</h3>
                    <button id="close-history" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="mt-3 flex gap-2">
                    <button id="new-chat-btn" class="flex-1 px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm">
                        <i class="fas fa-plus mr-2"></i>新对话
                    </button>
                    <button id="clear-history-btn" class="px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors text-sm">
                        <i class="fas fa-trash mr-2"></i>清空
                    </button>
                </div>
                <div class="mt-3">
                    <input 
                        type="text" 
                        id="search-conversations" 
                        placeholder="搜索对话..." 
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                    >
                </div>
            </div>
            
            <!-- 历史列表 -->
            <div id="history-list" class="flex-1 overflow-y-auto p-4 space-y-2">
                <!-- 对话历史项目将在这里动态生成 -->
            </div>
        </div>
    </div>

    <!-- 对话历史切换按钮 -->
    <button id="history-toggle" class="history-toggle" title="对话历史">
        <i class="fas fa-history"></i>
    </button>

    <script>
        // MathJax 配置
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };

        class EnhancedChatApp {
            constructor() {
                console.log('🚀 初始化增强版聊天应用');
                
                // 基础配置
                this.currentModel = null;
                this.currentService = null;
                this.isStreaming = false;
                this.searchEnabled = false;
                this.maxFileSize = 10 * 1024 * 1024; // 10MB
                
                // 对话管理
                this.conversations = [];
                this.currentConversationId = null;
                this.messageHistory = [];
                
                // 统计信息
                this.stats = {
                    messageCount: 0,
                    lastResponseTime: null,
                    lastOutputSpeed: null
                };
                
                // 服务发现
                this.services = {
                    ollama: { url: 'http://localhost:11434', name: 'Ollama' },
                    lmstudio: { url: 'http://localhost:1234', name: 'LM Studio' }
                };
                this.activeServices = [];
                this.discoveredModels = [];
                
                this.init();
            }

            async init() {
                try {
                    console.log('🔧 开始初始化应用');

                    this.setupEventListeners();
                    this.loadSettings();
                    this.loadConversations();

                    // 如果没有历史对话，创建一个新对话
                    if (this.conversations.length === 0) {
                        this.createNewConversation();
                    } else {
                        // 如果有历史对话，切换到最新的对话
                        this.currentConversationId = this.conversations[0].id;
                        this.messageHistory = this.conversations[0].messages || [];
                        this.updateChatMessages();
                    }

                    await this.detectServices();
                    this.updateUI();
                    this.updateHistoryList();

                    console.log('✅ 应用初始化完成');
                } catch (error) {
                    console.error('❌ 应用初始化失败:', error);
                }
            }

            // 事件监听器设置
            setupEventListeners() {
                // 对话历史面板
                const historyToggle = document.getElementById('history-toggle');
                const historyPanel = document.getElementById('history-panel');
                const closeHistory = document.getElementById('close-history');

                if (historyToggle) {
                    historyToggle.addEventListener('click', () => {
                        historyPanel.classList.toggle('open');
                    });
                }

                if (closeHistory) {
                    closeHistory.addEventListener('click', () => {
                        historyPanel.classList.remove('open');
                    });
                }

                // 搜索功能切换
                const searchToggle = document.getElementById('search-toggle');
                if (searchToggle) {
                    searchToggle.addEventListener('click', () => {
                        this.toggleSearch();
                    });
                }

                // 发送消息
                const sendBtn = document.getElementById('send-btn');
                const messageInput = document.getElementById('message-input');
                const stopBtn = document.getElementById('stop-btn');

                if (sendBtn) {
                    sendBtn.addEventListener('click', () => this.sendMessage());
                }

                if (stopBtn) {
                    stopBtn.addEventListener('click', () => this.stopStreaming());
                }

                if (messageInput) {
                    messageInput.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            this.sendMessage();
                        }
                    });

                    // 自动调整高度
                    messageInput.addEventListener('input', () => {
                        messageInput.style.height = 'auto';
                        messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
                    });
                }

                // 文件上传
                const fileUploadBtn = document.getElementById('file-upload-btn');
                const fileInput = document.getElementById('file-input');

                if (fileUploadBtn) {
                    fileUploadBtn.addEventListener('click', () => fileInput.click());
                }

                if (fileInput) {
                    fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
                }

                // 新对话和清空历史
                const newChatBtn = document.getElementById('new-chat-btn');
                const clearHistoryBtn = document.getElementById('clear-history-btn');

                if (newChatBtn) {
                    newChatBtn.addEventListener('click', () => this.createNewConversation());
                }

                if (clearHistoryBtn) {
                    clearHistoryBtn.addEventListener('click', () => this.clearAllHistory());
                }

                // 搜索对话
                const searchInput = document.getElementById('search-conversations');
                if (searchInput) {
                    searchInput.addEventListener('input', (e) => this.searchConversations(e.target.value));
                }

                // 设置按钮
                const settingsBtn = document.getElementById('settings-btn');
                if (settingsBtn) {
                    settingsBtn.addEventListener('click', () => this.openSettings());
                }

                console.log('📋 事件监听器设置完成');
            }

            // 搜索功能切换
            toggleSearch() {
                this.searchEnabled = !this.searchEnabled;
                const searchToggle = document.getElementById('search-toggle');
                const searchStatus = document.getElementById('search-status');

                if (searchToggle && searchStatus) {
                    if (this.searchEnabled) {
                        searchToggle.classList.add('active');
                        searchStatus.textContent = '联网搜索: 开启';
                    } else {
                        searchToggle.classList.remove('active');
                        searchStatus.textContent = '联网搜索: 关闭';
                    }
                }

                this.saveSettings();
                console.log(`🔍 联网搜索已${this.searchEnabled ? '开启' : '关闭'}`);
            }

            // 服务检测
            async detectServices() {
                console.log('🔍 开始检测AI服务');

                this.activeServices = [];
                this.discoveredModels = [];

                for (const [serviceKey, service] of Object.entries(this.services)) {
                    console.log(`🔗 正在检测 ${service.name} (${service.url})`);

                    try {
                        const controller = new AbortController();
                        const timeoutId = setTimeout(() => controller.abort(), 5000);

                        // 根据服务类型使用不同的API端点
                        let apiUrl;
                        if (serviceKey === 'ollama') {
                            apiUrl = `${service.url}/api/tags`;
                        } else if (serviceKey === 'lmstudio') {
                            apiUrl = `${service.url}/v1/models`;
                        }

                        const response = await fetch(apiUrl, {
                            method: 'GET',
                            signal: controller.signal,
                            headers: { 'Content-Type': 'application/json' }
                        });

                        clearTimeout(timeoutId);

                        if (response.ok) {
                            const data = await response.json();
                            this.activeServices.push(serviceKey);

                            // 根据服务类型解析模型数据
                            let models = [];
                            if (serviceKey === 'ollama' && data.models && Array.isArray(data.models)) {
                                models = data.models.map(model => ({
                                    name: model.name,
                                    size: this.parseModelSize(model.name, model.size),
                                    sizeBytes: model.size || 0
                                }));
                            } else if (serviceKey === 'lmstudio' && data.data && Array.isArray(data.data)) {
                                models = data.data.map(model => ({
                                    name: model.id,
                                    size: this.parseModelSize(model.id, model.size),
                                    sizeBytes: model.size || 0
                                }));
                            }

                            models.forEach(model => {
                                this.discoveredModels.push({
                                    id: `${serviceKey}-${model.name}`,
                                    name: model.name,
                                    service: serviceKey,
                                    serviceName: service.name,
                                    size: model.size,
                                    sizeBytes: model.sizeBytes
                                });
                            });

                            console.log(`✅ ${service.name} 检测成功，发现 ${models.length} 个模型`);
                        }
                    } catch (error) {
                        console.log(`❌ ${service.name} 服务检测失败:`, error.message);
                    }
                }

                console.log(`🎯 检测完成: ${this.activeServices.length} 个服务, ${this.discoveredModels.length} 个模型`);
            }

            // 解析模型大小
            parseModelSize(modelName, sizeBytes) {
                // 从模型名称中提取参数量信息
                const sizePatterns = [
                    { pattern: /(\d+\.?\d*)b/i, unit: 'B' },
                    { pattern: /(\d+\.?\d*)m/i, unit: 'M' },
                    { pattern: /(\d+\.?\d*)k/i, unit: 'K' }
                ];

                for (const { pattern, unit } of sizePatterns) {
                    const match = modelName.match(pattern);
                    if (match) {
                        return `${match[1]}${unit}`;
                    }
                }

                // 如果没有从名称中找到，尝试从字节数计算
                if (sizeBytes && sizeBytes > 0) {
                    const gb = sizeBytes / (1024 * 1024 * 1024);
                    if (gb >= 1) {
                        return `${gb.toFixed(1)}GB`;
                    }
                    const mb = sizeBytes / (1024 * 1024);
                    return `${mb.toFixed(0)}MB`;
                }

                return '未知';
            }

            // 更新UI
            updateUI() {
                this.updateModelStatus();
                this.updateSearchStatus();
            }

            // 更新模型状态显示
            updateModelStatus() {
                const modelNameEl = document.getElementById('current-model-name');
                const modelSizeEl = document.getElementById('current-model-size');

                if (this.currentModel && this.discoveredModels.length > 0) {
                    const model = this.discoveredModels.find(m => m.id === this.currentModel);
                    if (model) {
                        if (modelNameEl) modelNameEl.textContent = model.name;
                        if (modelSizeEl) modelSizeEl.textContent = model.size;
                    }
                } else {
                    if (modelNameEl) modelNameEl.textContent = '未选择';
                    if (modelSizeEl) modelSizeEl.textContent = '0B';
                }
            }

            // 更新搜索状态
            updateSearchStatus() {
                const searchStatus = document.getElementById('search-status');
                if (searchStatus) {
                    searchStatus.textContent = `联网搜索: ${this.searchEnabled ? '开启' : '关闭'}`;
                }
            }

            // 对话管理
            createNewConversation() {
                const conversation = {
                    id: Date.now().toString(),
                    title: '新对话',
                    messages: [],
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };

                this.conversations.unshift(conversation);
                this.currentConversationId = conversation.id;
                this.messageHistory = [];

                this.saveConversations();
                this.updateHistoryList();
                this.updateChatMessages();

                console.log('📝 创建新对话:', conversation.id);
            }

            loadConversations() {
                try {
                    const saved = localStorage.getItem('enhanced-chat-conversations');
                    this.conversations = saved ? JSON.parse(saved) : [];
                    console.log(`📚 加载了 ${this.conversations.length} 个历史对话`);
                } catch (error) {
                    console.error('❌ 加载对话历史失败:', error);
                    this.conversations = [];
                }
            }

            saveConversations() {
                try {
                    localStorage.setItem('enhanced-chat-conversations', JSON.stringify(this.conversations));
                } catch (error) {
                    console.error('❌ 保存对话历史失败:', error);
                }
            }

            loadSettings() {
                try {
                    const saved = localStorage.getItem('enhanced-chat-settings');
                    const settings = saved ? JSON.parse(saved) : {};

                    this.searchEnabled = settings.searchEnabled || false;
                    this.currentModel = settings.currentModel || null;
                    this.currentService = settings.currentService || null;

                    console.log('⚙️ 设置已加载');
                } catch (error) {
                    console.error('❌ 加载设置失败:', error);
                }
            }

            saveSettings() {
                try {
                    const settings = {
                        searchEnabled: this.searchEnabled,
                        currentModel: this.currentModel,
                        currentService: this.currentService
                    };
                    localStorage.setItem('enhanced-chat-settings', JSON.stringify(settings));
                } catch (error) {
                    console.error('❌ 保存设置失败:', error);
                }
            }

            // 更新历史列表
            updateHistoryList() {
                const historyList = document.getElementById('history-list');
                if (!historyList) return;

                historyList.innerHTML = '';

                this.conversations.forEach(conversation => {
                    const item = document.createElement('div');
                    item.className = `p-3 rounded-lg cursor-pointer transition-colors ${
                        conversation.id === this.currentConversationId
                            ? 'bg-blue-100 border border-blue-300'
                            : 'bg-gray-50 hover:bg-gray-100'
                    }`;

                    const title = conversation.title || '新对话';
                    const messageCount = conversation.messages ? conversation.messages.length : 0;
                    const lastUpdate = new Date(conversation.updatedAt).toLocaleDateString();

                    item.innerHTML = `
                        <div class="flex items-center justify-between">
                            <div class="flex-1 min-w-0">
                                <h4 class="text-sm font-medium text-gray-900 truncate">${title}</h4>
                                <p class="text-xs text-gray-500 mt-1">${messageCount} 条消息 • ${lastUpdate}</p>
                            </div>
                            <div class="flex items-center gap-1 ml-2">
                                <button class="p-1 text-gray-400 hover:text-gray-600 rename-btn" title="重命名">
                                    <i class="fas fa-edit text-xs"></i>
                                </button>
                                <button class="p-1 text-gray-400 hover:text-red-600 delete-btn" title="删除">
                                    <i class="fas fa-trash text-xs"></i>
                                </button>
                            </div>
                        </div>
                    `;

                    // 点击切换对话
                    item.addEventListener('click', (e) => {
                        if (!e.target.closest('.rename-btn') && !e.target.closest('.delete-btn')) {
                            this.switchConversation(conversation.id);
                        }
                    });

                    // 重命名按钮
                    const renameBtn = item.querySelector('.rename-btn');
                    renameBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.renameConversation(conversation.id);
                    });

                    // 删除按钮
                    const deleteBtn = item.querySelector('.delete-btn');
                    deleteBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.deleteConversation(conversation.id);
                    });

                    historyList.appendChild(item);
                });
            }

            // 切换对话
            switchConversation(conversationId) {
                const conversation = this.conversations.find(c => c.id === conversationId);
                if (!conversation) return;

                this.currentConversationId = conversationId;
                this.messageHistory = conversation.messages || [];

                this.updateChatMessages();
                this.updateHistoryList();

                // 关闭历史面板（移动端）
                const historyPanel = document.getElementById('history-panel');
                if (historyPanel) {
                    historyPanel.classList.remove('open');
                }

                console.log('🔄 切换到对话:', conversationId);
            }

            // 发送消息
            async sendMessage() {
                const messageInput = document.getElementById('message-input');
                const sendBtn = document.getElementById('send-btn');
                const stopBtn = document.getElementById('stop-btn');

                if (!messageInput || !messageInput.value.trim()) return;
                if (this.isStreaming) return;

                const userMessage = messageInput.value.trim();
                messageInput.value = '';
                messageInput.style.height = 'auto';

                // 检查是否选择了模型
                if (!this.currentModel) {
                    this.showError('请先选择一个AI模型');
                    return;
                }

                // 添加用户消息
                this.addMessage('user', userMessage);

                // 更新UI状态
                if (sendBtn) sendBtn.style.display = 'none';
                if (stopBtn) stopBtn.style.display = 'block';
                this.isStreaming = true;

                try {
                    // 如果启用了联网搜索，先进行搜索
                    let searchResults = null;
                    if (this.searchEnabled) {
                        searchResults = await this.performWebSearch(userMessage);
                    }

                    // 构建完整的提示词
                    let fullPrompt = userMessage;
                    if (searchResults && searchResults.length > 0) {
                        const searchContext = searchResults.map(result =>
                            `来源: ${result.title}\n链接: ${result.url}\n内容: ${result.snippet}`
                        ).join('\n\n');

                        fullPrompt = `基于以下搜索结果回答问题：\n\n${searchContext}\n\n用户问题: ${userMessage}`;
                    }

                    // 发送到AI模型
                    await this.streamResponse(fullPrompt, searchResults);

                } catch (error) {
                    console.error('❌ 发送消息失败:', error);
                    this.showError('发送消息失败: ' + error.message);
                } finally {
                    // 恢复UI状态
                    if (sendBtn) sendBtn.style.display = 'block';
                    if (stopBtn) stopBtn.style.display = 'none';
                    this.isStreaming = false;
                }
            }

            // 联网搜索
            async performWebSearch(query) {
                try {
                    console.log('🔍 开始联网搜索:', query);

                    // 这里使用一个简单的搜索API示例
                    // 实际使用时需要替换为真实的搜索API
                    const searchUrl = `https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json&no_html=1&skip_disambig=1`;

                    const response = await fetch(searchUrl);
                    const data = await response.json();

                    // 解析搜索结果
                    const results = [];
                    if (data.RelatedTopics && Array.isArray(data.RelatedTopics)) {
                        data.RelatedTopics.slice(0, 3).forEach(topic => {
                            if (topic.Text && topic.FirstURL) {
                                results.push({
                                    title: topic.Text.split(' - ')[0] || '搜索结果',
                                    url: topic.FirstURL,
                                    snippet: topic.Text
                                });
                            }
                        });
                    }

                    console.log(`✅ 搜索完成，找到 ${results.length} 个结果`);
                    return results;

                } catch (error) {
                    console.error('❌ 联网搜索失败:', error);
                    // 搜索失败时返回空数组，不影响正常对话
                    return [];
                }
            }

            // 流式响应
            async streamResponse(prompt, searchResults = null) {
                const startTime = Date.now();
                let responseText = '';
                let charCount = 0;

                // 添加AI消息容器
                const messageId = this.addMessage('assistant', '', true);
                const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
                const contentElement = messageElement?.querySelector('.message-content');

                if (!contentElement) {
                    throw new Error('无法找到消息容器');
                }

                // 显示思考指示器
                this.showThinkingIndicator(contentElement);

                try {
                    // 根据当前服务构建API请求
                    const model = this.discoveredModels.find(m => m.id === this.currentModel);
                    if (!model) {
                        throw new Error('未找到选择的模型');
                    }

                    let apiUrl, requestBody;

                    if (model.service === 'ollama') {
                        apiUrl = `${this.services.ollama.url}/api/generate`;
                        requestBody = {
                            model: model.name,
                            prompt: prompt,
                            stream: true
                        };
                    } else if (model.service === 'lmstudio') {
                        apiUrl = `${this.services.lmstudio.url}/v1/chat/completions`;
                        requestBody = {
                            model: model.name,
                            messages: [{ role: 'user', content: prompt }],
                            stream: true
                        };
                    }

                    const response = await fetch(apiUrl, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(requestBody)
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    // 隐藏思考指示器
                    this.hideThinkingIndicator(contentElement);

                    // 如果有搜索结果，先显示搜索信息
                    if (searchResults && searchResults.length > 0) {
                        this.displaySearchResults(contentElement, searchResults);
                    }

                    // 处理流式响应
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();

                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;

                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\n').filter(line => line.trim());

                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                const data = line.slice(6);
                                if (data === '[DONE]') continue;

                                try {
                                    const parsed = JSON.parse(data);
                                    let newText = '';

                                    if (model.service === 'ollama' && parsed.response) {
                                        newText = parsed.response;
                                    } else if (model.service === 'lmstudio' && parsed.choices?.[0]?.delta?.content) {
                                        newText = parsed.choices[0].delta.content;
                                    }

                                    if (newText) {
                                        responseText += newText;
                                        charCount += newText.length;
                                        this.updateStreamingMessage(contentElement, responseText);
                                    }
                                } catch (e) {
                                    console.warn('解析响应数据失败:', e);
                                }
                            }
                        }
                    }

                } catch (error) {
                    this.hideThinkingIndicator(contentElement);
                    throw error;
                }

                // 更新统计信息
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                const outputSpeed = charCount > 0 ? Math.round((charCount / responseTime) * 1000) : 0;

                this.stats.lastResponseTime = responseTime;
                this.stats.lastOutputSpeed = outputSpeed;
                this.stats.messageCount++;

                this.updateStatsDisplay();

                // 保存消息到历史
                this.updateCurrentConversation();
            }

            // 添加消息
            addMessage(role, content, isStreaming = false) {
                const messageId = Date.now().toString() + Math.random().toString(36).substr(2, 9);
                const message = {
                    id: messageId,
                    role: role,
                    content: content,
                    timestamp: new Date().toISOString()
                };

                this.messageHistory.push(message);
                this.renderMessage(message, isStreaming);

                return messageId;
            }

            // 渲染消息
            renderMessage(message, isStreaming = false) {
                const chatMessages = document.getElementById('chat-messages');
                if (!chatMessages) return;

                const messageDiv = document.createElement('div');
                messageDiv.className = 'message-container';
                messageDiv.setAttribute('data-message-id', message.id);

                const isUser = message.role === 'user';
                const messageClass = isUser ? 'user-message' : 'ai-message';

                messageDiv.innerHTML = `
                    <div class="${messageClass}">
                        <div class="flex items-start gap-3">
                            <div class="flex-shrink-0">
                                ${isUser ?
                                    '<div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm">👤</div>' :
                                    '<div class="w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center text-white text-sm">🤖</div>'
                                }
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center gap-2 mb-1">
                                    <span class="text-sm font-medium">${isUser ? '您' : 'AI助手'}</span>
                                    <span class="text-xs text-gray-500">${new Date(message.timestamp).toLocaleTimeString()}</span>
                                </div>
                                <div class="message-content prose prose-sm max-w-none">
                                    ${isStreaming ? '' : this.formatMessageContent(message.content)}
                                </div>
                                ${!isUser ? `
                                    <div class="message-actions">
                                        <button class="action-btn copy-btn" title="复制">
                                            <i class="fas fa-copy"></i> 复制
                                        </button>
                                        <button class="action-btn regenerate-btn" title="重新生成">
                                            <i class="fas fa-redo"></i> 重新生成
                                        </button>
                                        <button class="action-btn edit-btn" title="编辑">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                        <button class="action-btn delete-btn" title="删除">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                `;

                // 添加事件监听器
                if (!isUser) {
                    this.setupMessageActions(messageDiv, message);
                }

                chatMessages.appendChild(messageDiv);
                this.scrollToBottom();
            }

            // 格式化消息内容
            formatMessageContent(content) {
                if (!content) return '';

                // 使用marked.js处理Markdown
                try {
                    let html = marked.parse(content);

                    // 处理代码高亮
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = html;

                    tempDiv.querySelectorAll('pre code').forEach(block => {
                        hljs.highlightElement(block);
                    });

                    return tempDiv.innerHTML;
                } catch (error) {
                    console.error('格式化消息内容失败:', error);
                    return content.replace(/\n/g, '<br>');
                }
            }

            // 设置消息操作按钮
            setupMessageActions(messageDiv, message) {
                const copyBtn = messageDiv.querySelector('.copy-btn');
                const regenerateBtn = messageDiv.querySelector('.regenerate-btn');
                const editBtn = messageDiv.querySelector('.edit-btn');
                const deleteBtn = messageDiv.querySelector('.delete-btn');

                if (copyBtn) {
                    copyBtn.addEventListener('click', () => {
                        navigator.clipboard.writeText(message.content).then(() => {
                            this.showToast('已复制到剪贴板');
                        });
                    });
                }

                if (regenerateBtn) {
                    regenerateBtn.addEventListener('click', () => {
                        this.regenerateMessage(message.id);
                    });
                }

                if (editBtn) {
                    editBtn.addEventListener('click', () => {
                        this.editMessage(message.id);
                    });
                }

                if (deleteBtn) {
                    deleteBtn.addEventListener('click', () => {
                        this.deleteMessage(message.id);
                    });
                }
            }

            // 显示搜索结果
            displaySearchResults(contentElement, searchResults) {
                const searchDiv = document.createElement('div');
                searchDiv.className = 'mb-4';

                let searchHtml = '<div class="text-sm text-blue-600 font-medium mb-2">🔍 联网搜索结果:</div>';

                searchResults.forEach((result, index) => {
                    searchHtml += `
                        <div class="search-result">
                            <div class="font-medium text-sm mb-1">${result.title}</div>
                            <div class="text-xs text-gray-600 mb-2">${result.snippet}</div>
                            <a href="${result.url}" target="_blank" class="search-source">
                                <i class="fas fa-external-link-alt mr-1"></i>查看来源
                            </a>
                        </div>
                    `;
                });

                searchDiv.innerHTML = searchHtml;
                contentElement.appendChild(searchDiv);
            }

            // 显示思考指示器
            showThinkingIndicator(contentElement) {
                const thinkingDiv = document.createElement('div');
                thinkingDiv.className = 'thinking-indicator';
                thinkingDiv.innerHTML = `
                    <div class="thinking-dots">
                        <div class="thinking-dot"></div>
                        <div class="thinking-dot"></div>
                        <div class="thinking-dot"></div>
                    </div>
                    <span class="text-sm text-gray-600">AI正在思考...</span>
                `;
                contentElement.appendChild(thinkingDiv);
            }

            // 隐藏思考指示器
            hideThinkingIndicator(contentElement) {
                const thinkingIndicator = contentElement.querySelector('.thinking-indicator');
                if (thinkingIndicator) {
                    thinkingIndicator.remove();
                }
            }

            // 更新流式消息
            updateStreamingMessage(contentElement, text) {
                // 移除之前的内容（除了搜索结果）
                const existingContent = contentElement.querySelector('.streaming-content');
                if (existingContent) {
                    existingContent.remove();
                }

                const streamingDiv = document.createElement('div');
                streamingDiv.className = 'streaming-content';
                streamingDiv.innerHTML = this.formatMessageContent(text) + '<span class="streaming-cursor"></span>';

                contentElement.appendChild(streamingDiv);
                this.scrollToBottom();

                // 处理数学公式
                if (window.MathJax) {
                    MathJax.typesetPromise([streamingDiv]).catch(err => console.log(err));
                }
            }

            // 更新统计显示
            updateStatsDisplay() {
                const responseTimeEl = document.getElementById('response-time');
                const outputSpeedEl = document.getElementById('output-speed');

                if (responseTimeEl && this.stats.lastResponseTime) {
                    responseTimeEl.textContent = `${this.stats.lastResponseTime}ms`;
                }

                if (outputSpeedEl && this.stats.lastOutputSpeed) {
                    outputSpeedEl.textContent = `${this.stats.lastOutputSpeed} 字/秒`;
                }
            }

            // 更新聊天消息显示
            updateChatMessages() {
                const chatMessages = document.getElementById('chat-messages');
                if (!chatMessages) return;

                chatMessages.innerHTML = '';

                if (this.messageHistory.length === 0) {
                    chatMessages.innerHTML = `
                        <div class="text-center text-gray-500 py-8">
                            <i class="fas fa-robot text-4xl mb-4"></i>
                            <p class="text-lg">开始您的AI对话</p>
                            <p class="text-sm mt-2">选择模型并发送消息开始聊天</p>
                        </div>
                    `;
                } else {
                    this.messageHistory.forEach(message => {
                        this.renderMessage(message);
                    });
                }
            }

            // 滚动到底部
            scrollToBottom() {
                const chatMessages = document.getElementById('chat-messages');
                if (chatMessages) {
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }
            }

            // 显示错误信息
            showError(message) {
                this.showToast(message, 'error');
            }

            // 显示提示信息
            showToast(message, type = 'info') {
                const toast = document.createElement('div');
                toast.className = `fixed top-4 right-4 px-4 py-2 rounded-lg text-white z-50 ${
                    type === 'error' ? 'bg-red-500' : 'bg-blue-500'
                }`;
                toast.textContent = message;

                document.body.appendChild(toast);

                setTimeout(() => {
                    toast.remove();
                }, 3000);
            }

            // 停止流式输出
            stopStreaming() {
                this.isStreaming = false;
                const sendBtn = document.getElementById('send-btn');
                const stopBtn = document.getElementById('stop-btn');

                if (sendBtn) sendBtn.style.display = 'block';
                if (stopBtn) stopBtn.style.display = 'none';
            }

            // 更新当前对话
            updateCurrentConversation() {
                if (!this.currentConversationId) return;

                const conversation = this.conversations.find(c => c.id === this.currentConversationId);
                if (conversation) {
                    conversation.messages = [...this.messageHistory];
                    conversation.updatedAt = new Date().toISOString();

                    // 更新标题（使用第一条用户消息）
                    const firstUserMessage = this.messageHistory.find(m => m.role === 'user');
                    if (firstUserMessage && conversation.title === '新对话') {
                        conversation.title = firstUserMessage.content.slice(0, 30) + (firstUserMessage.content.length > 30 ? '...' : '');
                    }

                    this.saveConversations();
                    this.updateHistoryList();
                }
            }

            // 搜索对话
            searchConversations(query) {
                const historyList = document.getElementById('history-list');
                if (!historyList) return;

                const items = historyList.querySelectorAll('.message-container');
                items.forEach(item => {
                    const title = item.querySelector('h4').textContent.toLowerCase();
                    const visible = !query || title.includes(query.toLowerCase());
                    item.style.display = visible ? 'block' : 'none';
                });
            }

            // 清空所有历史
            clearAllHistory() {
                if (confirm('确定要清空所有对话历史吗？此操作不可撤销。')) {
                    this.conversations = [];
                    this.messageHistory = [];
                    this.currentConversationId = null;

                    this.saveConversations();
                    this.updateHistoryList();
                    this.updateChatMessages();
                    this.createNewConversation();
                }
            }

            // 重命名对话
            renameConversation(conversationId) {
                const conversation = this.conversations.find(c => c.id === conversationId);
                if (!conversation) return;

                const newTitle = prompt('请输入新的对话标题:', conversation.title);
                if (newTitle && newTitle.trim()) {
                    conversation.title = newTitle.trim();
                    this.saveConversations();
                    this.updateHistoryList();
                }
            }

            // 删除对话
            deleteConversation(conversationId) {
                if (confirm('确定要删除这个对话吗？')) {
                    this.conversations = this.conversations.filter(c => c.id !== conversationId);

                    if (this.currentConversationId === conversationId) {
                        if (this.conversations.length > 0) {
                            this.switchConversation(this.conversations[0].id);
                        } else {
                            this.createNewConversation();
                        }
                    }

                    this.saveConversations();
                    this.updateHistoryList();
                }
            }

            // 文件上传处理
            handleFileUpload(event) {
                const files = Array.from(event.target.files);
                if (files.length === 0) return;

                files.forEach(file => {
                    if (file.size > this.maxFileSize) {
                        this.showError(`文件 ${file.name} 超过大小限制 (10MB)`);
                        return;
                    }

                    // 这里可以添加文件处理逻辑
                    console.log('上传文件:', file.name, file.type, file.size);
                    this.showToast(`文件 ${file.name} 上传成功`);
                });

                // 清空文件输入
                event.target.value = '';
            }

            // 打开设置
            openSettings() {
                this.showSettingsModal();
            }

            // 显示设置模态框
            showSettingsModal() {
                // 创建设置模态框
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold">设置</h3>
                            <button class="close-modal text-gray-400 hover:text-gray-600">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">选择模型</label>
                                <select id="model-select" class="w-full border border-gray-300 rounded-lg px-3 py-2">
                                    <option value="">请选择模型</option>
                                    ${this.discoveredModels.map(model => `
                                        <option value="${model.id}" ${model.id === this.currentModel ? 'selected' : ''}>
                                            ${model.name} (${model.serviceName}) - ${model.size}
                                        </option>
                                    `).join('')}
                                </select>
                            </div>

                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" id="search-enabled" ${this.searchEnabled ? 'checked' : ''} class="mr-2">
                                    <span class="text-sm">启用联网搜索</span>
                                </label>
                            </div>

                            <div class="flex gap-2 pt-4">
                                <button class="save-settings flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                                    保存设置
                                </button>
                                <button class="close-modal px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400">
                                    取消
                                </button>
                            </div>
                        </div>
                    </div>
                `;

                // 添加事件监听器
                const closeButtons = modal.querySelectorAll('.close-modal');
                closeButtons.forEach(btn => {
                    btn.addEventListener('click', () => modal.remove());
                });

                const saveButton = modal.querySelector('.save-settings');
                saveButton.addEventListener('click', () => {
                    const modelSelect = modal.querySelector('#model-select');
                    const searchEnabled = modal.querySelector('#search-enabled');

                    this.currentModel = modelSelect.value;
                    this.searchEnabled = searchEnabled.checked;

                    this.saveSettings();
                    this.updateUI();
                    this.showToast('设置已保存');
                    modal.remove();
                });

                document.body.appendChild(modal);
            }
        }
        }

        // 启动应用
        document.addEventListener('DOMContentLoaded', () => {
            try {
                console.log('🎯 DOM加载完成，启动应用');
                window.enhancedChatApp = new EnhancedChatApp();
            } catch (error) {
                console.error('❌ 应用启动失败:', error);
            }
        });
    </script>
</body>
</html>
