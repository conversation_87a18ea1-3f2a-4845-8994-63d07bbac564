<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地大模型对话界面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#10B981',
                        tertiary: '#F59E0B',
                        layer1: '#E0F2FE',
                        layer2: '#DBEAFE',
                        layer3: '#E0E7FF',
                        layer4: '#EEF2FF',
                        component: 'rgba(255, 255, 255, 0.7)',
                        chatbot: '#4F46E5',
                        user: '#10B981',
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .module-border {
                @apply border border-gray-200 rounded-lg;
            }
            .layer-padding {
                @apply p-4;
            }
            .component-padding {
                @apply p-3;
            }
            .text-shadow-sm {
                text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            }
            .chat-message {
                @apply rounded-lg p-4 max-w-[85%] my-2 animate-fadeIn;
            }
            .chat-message-bot {
                @apply bg-chatbot text-white rounded-tl-none;
            }
            .chat-message-user {
                @apply bg-user text-white rounded-tr-none ml-auto;
            }
            .animate-fadeIn {
                animation: fadeIn 0.3s ease-in-out;
            }
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }
            .typing-indicator {
                @apply inline-block space-x-1;
            }
            .typing-indicator span {
                @apply inline-block w-2 h-2 bg-white rounded-full animate-bounce;
            }
            .typing-indicator span:nth-child(2) {
                animation-delay: 0.2s;
            }
            .typing-indicator span:nth-child(3) {
                animation-delay: 0.4s;
            }
            .modal-backdrop {
                @apply fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300 opacity-0 pointer-events-none;
            }
            .modal {
                @apply fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full max-w-lg bg-white rounded-xl shadow-xl z-50 transition-all duration-300 scale-95 opacity-0;
            }
            .modal.active {
                @apply scale-100 opacity-100;
            }
            .modal-backdrop.active {
                @apply opacity-100 pointer-events-auto;
            }
            .btn-hover {
                @apply transition-all duration-200 hover:shadow-md hover:-translate-y-0.5;
            }
            .card-hover {
                @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
            }
            .gradient-text {
                @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-tertiary;
            }
            .cursor {
                animation: blink 1s infinite;
            }
            @keyframes blink {
                0%, 50% { opacity: 1; }
                51%, 100% { opacity: 0; }
            }
            .streaming-content {
                white-space: pre-wrap;
            }
        }
    </style>
</head>
<body class="bg-gray-50 font-sans text-gray-800 min-h-screen flex flex-col">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm sticky top-0 z-10 transition-all duration-300">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center">
                <i class="fa fa-brain text-primary text-2xl mr-2"></i>
                <h1 class="text-xl font-bold text-primary">本地大模型助手</h1>
            </div>
            <div class="flex items-center space-x-4">
                <button id="clear-chat" class="text-gray-500 hover:text-primary transition-colors btn-hover" title="清空对话">
                    <i class="fa fa-trash"></i>
                </button>
                <button id="settings-button" class="text-gray-500 hover:text-primary transition-colors btn-hover" title="设置">
                    <i class="fa fa-cog"></i>
                </button>
                <button class="text-gray-500 hover:text-primary transition-colors btn-hover" title="帮助">
                    <i class="fa fa-question-circle"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- 主要内容区 -->
    <main class="flex-1 container mx-auto p-4 md:p-6 flex flex-col">
        <!-- 模型状态卡片 -->
        <div class="bg-white rounded-xl p-4 shadow-sm mb-6 flex flex-col md:flex-row items-center justify-between card-hover">
            <div class="flex items-center mb-4 md:mb-0">
                <div class="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center mr-3">
                    <i class="fa fa-server text-primary"></i>
                </div>
                <div>
                    <h2 class="font-bold text-gray-800">模型状态</h2>
                    <div class="flex items-center mt-1">
                        <span class="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                        <span class="text-sm text-gray-600">推理模型 & 多模态模型 已加载</span>
                    </div>
                </div>
            </div>
            <!-- 增强的监控面板 -->
            <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3 w-full">
                <!-- 第一行：基础统计 -->
                <div class="bg-layer4 rounded-lg p-3 text-center card-hover border-l-4 border-blue-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-comments mr-1"></i>对话轮数
                    </div>
                    <div id="conversation-count-display" class="font-semibold text-blue-600">0</div>
                </div>

                <div class="bg-layer4 rounded-lg p-3 text-center card-hover border-l-4 border-green-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-clock mr-1"></i>响应时间
                    </div>
                    <div id="response-time-display" class="font-semibold text-green-600">--</div>
                </div>

                <div class="bg-layer4 rounded-lg p-3 text-center card-hover border-l-4 border-purple-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-font mr-1"></i>总字符数
                    </div>
                    <div id="total-char-count-display" class="font-semibold text-purple-600">0</div>
                </div>

                <div class="bg-layer4 rounded-lg p-3 text-center card-hover border-l-4 border-indigo-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-hourglass-half mr-1"></i>会话时长
                    </div>
                    <div id="session-duration-display" class="font-semibold text-indigo-600">00:00:00</div>
                </div>

                <div class="bg-layer4 rounded-lg p-3 text-center card-hover border-l-4 border-orange-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-calculator mr-1"></i>平均字符
                    </div>
                    <div id="average-chars-display" class="font-semibold text-orange-600">--</div>
                </div>

                <div class="bg-layer4 rounded-lg p-3 text-center card-hover border-l-4 border-teal-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-cog mr-1"></i>当前模式
                    </div>
                    <div id="current-mode-display" class="font-semibold text-teal-600">文本</div>
                </div>
            </div>

            <!-- 第二行：性能指标 -->
            <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3 w-full mt-3">
                <div class="bg-layer4 rounded-lg p-3 text-center card-hover border-l-4 border-red-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-bolt mr-1"></i>首字节延迟
                    </div>
                    <div id="first-byte-time-display" class="font-semibold text-red-600">--</div>
                </div>

                <div class="bg-layer4 rounded-lg p-3 text-center card-hover border-l-4 border-yellow-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-tachometer-alt mr-1"></i>输出速度
                    </div>
                    <div id="streaming-speed-display" class="font-semibold text-yellow-600">--</div>
                </div>

                <div class="bg-layer4 rounded-lg p-3 text-center card-hover border-l-4 border-green-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-link mr-1"></i>连接状态
                    </div>
                    <div id="connection-status-display" class="font-semibold text-green-600">未连接</div>
                </div>

                <div class="bg-layer4 rounded-lg p-3 text-center card-hover border-l-4 border-blue-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-cube mr-1"></i>模型状态
                    </div>
                    <div id="model-load-status-display" class="font-semibold text-blue-600">未知</div>
                </div>

                <div class="bg-layer4 rounded-lg p-3 text-center card-hover border-l-4 border-pink-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-wifi mr-1"></i>网络延迟
                    </div>
                    <div id="network-latency-display" class="font-semibold text-pink-600">--</div>
                </div>

                <div class="bg-layer4 rounded-lg p-3 text-center card-hover border-l-4 border-gray-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-chart-bar mr-1"></i>详细统计
                    </div>
                    <button id="stats-detail-btn" class="font-semibold text-gray-600 hover:text-gray-800 transition-colors">查看</button>
                </div>
            </div>

            <!-- 详细统计弹窗（隐藏） -->
            <div id="stats-detail-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
                <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-800">详细统计信息</h3>
                        <button id="close-stats-modal" class="text-gray-500 hover:text-gray-700">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="space-y-3">
                            <div class="bg-blue-50 p-3 rounded-lg">
                                <div class="text-sm text-blue-600 font-medium">用户输入字符</div>
                                <div id="user-char-count-display" class="text-xl font-bold text-blue-700">0</div>
                            </div>
                            <div class="bg-green-50 p-3 rounded-lg">
                                <div class="text-sm text-green-600 font-medium">AI输出字符</div>
                                <div id="ai-char-count-display" class="text-xl font-bold text-green-700">0</div>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div class="bg-purple-50 p-3 rounded-lg">
                                <div class="text-sm text-purple-600 font-medium">字符比例 (AI:用户)</div>
                                <div id="char-ratio-display" class="text-xl font-bold text-purple-700">--</div>
                            </div>
                            <div class="bg-orange-50 p-3 rounded-lg">
                                <div class="text-sm text-orange-600 font-medium">会话效率 (字符/分钟)</div>
                                <div id="session-efficiency-display" class="text-xl font-bold text-orange-700">--</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 对话区域 -->
        <div class="flex-1 bg-white rounded-xl shadow-sm overflow-hidden flex flex-col mb-6 transition-all duration-300 hover:shadow-md">
            <div class="bg-primary text-white p-4 border-b border-gray-100 flex items-center">
                <i class="fa fa-comments-o mr-2"></i>
                <h2 class="font-bold">智能对话</h2>
            </div>
            
            <div id="chat-container" class="flex-1 overflow-y-auto p-4 space-y-4">
                <!-- 系统消息 -->
                <div class="flex items-start">
                    <div class="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <i class="fa fa-info text-primary"></i>
                    </div>
                    <div class="bg-layer3 rounded-lg p-3 max-w-[85%] card-hover">
                        <div class="font-semibold text-primary">系统提示</div>
                        <p class="text-sm text-gray-700">欢迎使用本地大模型助手！我支持文本对话和图像理解。您可以直接输入问题，或使用右侧按钮上传图片进行多模态交互。</p>
                    </div>
                </div>
                
                <!-- 聊天机器人消息 -->
                <div class="flex items-start">
                    <div class="w-8 h-8 rounded-full bg-chatbot flex items-center justify-center mr-3 flex-shrink-0">
                        <i class="fa fa-robot text-white"></i>
                    </div>
                    <div class="chat-message chat-message-bot card-hover">
                        <p>您好！我是您的本地大模型助手。请问有什么可以帮助您的吗？</p>
                    </div>
                </div>
                
                <!-- 用户消息 -->
                <div class="flex items-start justify-end">
                    <div class="chat-message chat-message-user card-hover">
                        <p>你能识别图片中的内容吗？</p>
                    </div>
                    <div class="w-8 h-8 rounded-full bg-user flex items-center justify-center ml-3 flex-shrink-0">
                        <i class="fa fa-user text-white"></i>
                    </div>
                </div>
                
                <!-- 聊天机器人消息 -->
                <div class="flex items-start">
                    <div class="w-8 h-8 rounded-full bg-chatbot flex items-center justify-center mr-3 flex-shrink-0">
                        <i class="fa fa-robot text-white"></i>
                    </div>
                    <div class="chat-message chat-message-bot card-hover">
                        <p>当然可以！我集成了多模态理解能力，请上传一张图片，我会为您识别并描述其中的内容。</p>
                    </div>
                </div>
            </div>
            
            <!-- 输入区域 -->
            <div class="p-4 border-t border-gray-100 bg-gray-50">
                <div class="relative">
                    <textarea id="user-input" class="w-full border border-gray-200 rounded-lg p-3 pr-12 focus:outline-none focus:ring-2 focus:ring-primary/50 resize-none transition-all duration-200 hover:border-primary/30" rows="2" placeholder="输入问题或上传图片..."></textarea>
                    <div class="absolute right-3 bottom-3 flex items-center space-x-2">
                        <label for="image-upload" class="text-gray-500 hover:text-primary cursor-pointer transition-colors btn-hover" title="上传图片">
                            <i class="fa fa-image text-lg"></i>
                        </label>
                        <input id="image-upload" type="file" accept="image/*" class="hidden">
                        <button id="send-button" class="bg-primary hover:bg-primary/90 text-white rounded-full w-9 h-9 flex items-center justify-center transition-all duration-200 hover:shadow-md hover:scale-105 active:scale-95" title="发送">
                            <i class="fa fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
                <div class="flex items-center justify-between mt-3 text-xs text-gray-500">
                    <div>支持文本、图片多模态交互</div>
                    <div>按 Enter 发送，Shift+Enter 换行</div>
                </div>
            </div>
        </div>
        
        <!-- 功能卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="bg-white rounded-xl p-4 shadow-sm border-l-4 border-primary hover:shadow-md transition-shadow card-hover cursor-pointer" onclick="selectMode('文本')">
                <div class="flex items-center mb-3">
                    <i class="fa fa-file-text-o text-primary text-xl mr-3"></i>
                    <h3 class="font-bold text-gray-800">文本推理</h3>
                </div>
                <p class="text-sm text-gray-600">基于7B参数的大型语言模型，提供专业的文本理解和生成能力。</p>
            </div>
            
            <div class="bg-white rounded-xl p-4 shadow-sm border-l-4 border-tertiary hover:shadow-md transition-shadow card-hover cursor-pointer" onclick="selectMode('图像')">
                <div class="flex items-center mb-3">
                    <i class="fa fa-image text-tertiary text-xl mr-3"></i>
                    <h3 class="font-bold text-gray-800">图像理解</h3>
                </div>
                <p class="text-sm text-gray-600">基于CLIP架构的视觉模型，支持图片识别、场景分析和内容描述。</p>
            </div>
            
            <div class="bg-white rounded-xl p-4 shadow-sm border-l-4 border-secondary hover:shadow-md transition-shadow card-hover cursor-pointer" onclick="selectMode('语音')">
                <div class="flex items-center mb-3">
                    <i class="fa fa-microphone text-secondary text-xl mr-3"></i>
                    <h3 class="font-bold text-gray-800">语音交互</h3>
                </div>
                <p class="text-sm text-gray-600">集成Wav2Vec2模型，支持语音输入和音频内容理解，提供更自然的交互体验。</p>
            </div>
        </div>
    </main>

    <!-- 设置模态框 -->
    <div id="settings-modal" class="modal">
        <div class="p-5">
            <div class="flex justify-between items-center mb-4">
                <div>
                    <h3 class="text-lg font-bold text-gray-800">模型对接设置</h3>
                    <div id="connection-status" class="flex items-center space-x-2 mt-1">
                        <div id="status-indicator" class="w-2 h-2 rounded-full bg-gray-400"></div>
                        <span id="status-text" class="text-xs text-gray-600">检查连接中...</span>
                    </div>
                </div>
                <button id="close-settings" class="text-gray-400 hover:text-gray-600 btn-hover" title="关闭">
                    <i class="fa fa-times"></i>
                </button>
            </div>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">默认模式</label>
                    <div class="grid grid-cols-3 gap-2">
                        <button class="mode-btn bg-primary text-white py-2 px-3 rounded-lg text-sm btn-hover" data-mode="多模态">多模态</button>
                        <button class="mode-btn bg-gray-200 text-gray-700 py-2 px-3 rounded-lg text-sm btn-hover" data-mode="文本">文本</button>
                        <button class="mode-btn bg-gray-200 text-gray-700 py-2 px-3 rounded-lg text-sm btn-hover" data-mode="图像">图像</button>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">API类型</label>
                    <select id="api-type" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-200 hover:border-primary/30">
                        <option value="ollama">Ollama (推荐)</option>
                        <option value="lm-studio">LM Studio</option>
                        <option value="openai-compatible">OpenAI兼容 (LocalAI等)</option>
                        <option value="custom">自定义API</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">API地址</label>
                    <input type="text" id="api-url" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-200 hover:border-primary/30" value="http://localhost:1234/v1" placeholder="http://localhost:1234/v1">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">API密钥 (可选)</label>
                    <input type="password" id="api-key" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-200 hover:border-primary/30" placeholder="留空表示无需密钥">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">模型名称</label>
                    <div class="flex space-x-2">
                        <select id="model-name" class="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-200 hover:border-primary/30">
                            <option value="llama3.2">llama3.2</option>
                        </select>
                        <button id="refresh-models" class="px-3 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 transition-all duration-200" title="刷新模型列表">
                            <i class="fa fa-refresh"></i>
                        </button>
                    </div>
                    <div id="model-status" class="mt-1 text-xs text-gray-500"></div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">系统提示词</label>
                    <textarea id="system-prompt" rows="3" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-200 hover:border-primary/30" placeholder="你是一个有用的AI助手。">你是一个有用的AI助手。</textarea>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">推理参数</label>
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label class="text-xs text-gray-500">温度 (Temperature)</label>
                            <input type="range" id="temperature" min="0" max="2" step="0.1" value="0.7" class="w-full accent-primary">
                            <div class="flex justify-between text-xs text-gray-500">
                                <span>0</span>
                                <span id="temp-value">0.7</span>
                                <span>2</span>
                            </div>
                        </div>
                        <div>
                            <label class="text-xs text-gray-500">最大生成长度</label>
                            <input type="range" id="max-length" min="100" max="2000" step="100" value="1000" class="w-full accent-primary">
                            <div class="flex justify-between text-xs text-gray-500">
                                <span>100</span>
                                <span id="length-value">1000</span>
                                <span>2000</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div>
                    <label class="flex items-center cursor-pointer">
                        <input type="checkbox" id="use-gpu" class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary cursor-pointer">
                        <span class="ml-2 text-sm text-gray-700">使用GPU加速</span>
                    </label>
                </div>
                
                <div>
                    <label class="flex items-center cursor-pointer">
                        <input type="checkbox" id="stream-output" class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary cursor-pointer">
                        <span class="ml-2 text-sm text-gray-700">流式输出</span>
                    </label>
                </div>
            </div>
            
            <div class="mt-6 flex justify-between">
                <button id="test-connection" class="px-4 py-2 border border-primary text-primary rounded-lg text-sm font-medium hover:bg-primary/10 transition-all duration-200 btn-hover">测试连接</button>
                <div class="flex space-x-3">
                    <button id="cancel-settings" class="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-all duration-200 btn-hover">取消</button>
                    <button id="save-settings" class="px-4 py-2 bg-primary border border-transparent rounded-lg text-sm font-medium text-white hover:bg-primary/90 transition-all duration-200 btn-hover">保存设置</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 模态框背景 -->
    <div id="modal-backdrop" class="modal-backdrop"></div>

    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-200 py-4 text-center text-sm text-gray-500">
        <p>本地大模型助手 © 2025 | 基于双引擎架构</p>
    </footer>

    <script>
        // 聊天功能实现
        document.addEventListener('DOMContentLoaded', function() {
            const chatContainer = document.getElementById('chat-container');
            const userInput = document.getElementById('user-input');
            const sendButton = document.getElementById('send-button');
            const imageUpload = document.getElementById('image-upload');
            const settingsButton = document.getElementById('settings-button');
            const closeSettings = document.getElementById('close-settings');
            const cancelSettings = document.getElementById('cancel-settings');
            const saveSettings = document.getElementById('save-settings');
            const testConnection = document.getElementById('test-connection');
            const clearChat = document.getElementById('clear-chat');
            const settingsModal = document.getElementById('settings-modal');
            const modalBackdrop = document.getElementById('modal-backdrop');
            const modeButtons = document.querySelectorAll('.mode-btn');
            const currentModeElement = document.getElementById('current-mode');
            const temperatureSlider = document.getElementById('temperature');
            const tempValue = document.getElementById('temp-value');
            const maxLengthSlider = document.getElementById('max-length');
            const lengthValue = document.getElementById('length-value');
            const header = document.querySelector('header');
            const refreshModels = document.getElementById('refresh-models');
            const modelSelect = document.getElementById('model-name');
            const modelStatus = document.getElementById('model-status');
            const statusIndicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('status-text');

            let currentMode = '多模态';
            let conversationHistory = [];
            let currentImageData = null;

            // API配置管理
            class APIConfig {
                constructor() {
                    this.loadConfig();
                }

                loadConfig() {
                    const saved = localStorage.getItem('llm-chat-config');
                    const defaults = {
                        apiType: 'ollama',
                        baseURL: 'http://localhost:11434',
                        apiKey: '',
                        model: 'llama3.2',
                        temperature: 0.7,
                        maxTokens: 1000,
                        useGPU: true,
                        streamOutput: true,
                        systemPrompt: '你是一个有用的AI助手，请用中文回答问题。'
                    };

                    this.config = saved ? { ...defaults, ...JSON.parse(saved) } : defaults;
                    this.updateUI();
                }

                saveConfig() {
                    localStorage.setItem('llm-chat-config', JSON.stringify(this.config));
                }

                updateUI() {
                    document.getElementById('temperature').value = this.config.temperature;
                    document.getElementById('temp-value').textContent = this.config.temperature;
                    document.getElementById('max-length').value = this.config.maxTokens;
                    document.getElementById('length-value').textContent = this.config.maxTokens;
                    document.getElementById('use-gpu').checked = this.config.useGPU;
                    document.getElementById('stream-output').checked = this.config.streamOutput;
                    document.getElementById('api-type').value = this.config.apiType;
                    document.getElementById('api-url').value = this.config.baseURL;
                    document.getElementById('api-key').value = this.config.apiKey;
                    document.getElementById('model-name').value = this.config.model;
                    document.getElementById('system-prompt').value = this.config.systemPrompt;
                }

                updateFromUI() {
                    this.config.temperature = parseFloat(document.getElementById('temperature').value);
                    this.config.maxTokens = parseInt(document.getElementById('max-length').value);
                    this.config.useGPU = document.getElementById('use-gpu').checked;
                    this.config.streamOutput = document.getElementById('stream-output').checked;
                    this.config.apiType = document.getElementById('api-type').value;
                    this.config.baseURL = document.getElementById('api-url').value;
                    this.config.apiKey = document.getElementById('api-key').value;
                    this.config.model = document.getElementById('model-name').value;
                    this.config.systemPrompt = document.getElementById('system-prompt').value;
                }

                async getOllamaModels() {
                    try {
                        const response = await fetch(`${this.config.baseURL}/api/tags`);
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        const data = await response.json();
                        return data.models || [];
                    } catch (error) {
                        console.error('获取Ollama模型列表失败:', error);
                        throw error;
                    }
                }

                async checkOllamaConnection() {
                    try {
                        const response = await fetch(`${this.config.baseURL}/api/version`);
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        const data = await response.json();
                        return { connected: true, version: data.version };
                    } catch (error) {
                        return { connected: false, error: error.message };
                    }
                }
            }

            const apiConfig = new APIConfig();

            // 增强的性能监控类
            class PerformanceMonitor {
                constructor() {
                    // 基础统计
                    this.conversationCount = 0;
                    this.lastResponseTime = 0;
                    this.startTime = 0;
                    this.isConnected = false;
                    this.currentModel = '';
                    this.currentMode = 'text';

                    // 新增：对话统计信息
                    this.userCharCount = 0;        // 用户输入字符数
                    this.aiCharCount = 0;          // AI输出字符数
                    this.sessionStartTime = Date.now(); // 会话开始时间
                    this.firstMessageTime = null;   // 首次对话时间

                    // 新增：性能指标
                    this.firstByteTime = 0;        // 首字节响应时间
                    this.streamingSpeed = 0;       // 流式输出速度
                    this.currentStreamStart = 0;   // 当前流式开始时间
                    this.currentStreamChars = 0;   // 当前流式字符数
                    this.networkLatency = 0;       // 网络延迟

                    // 新增：技术状态
                    this.connectionStatus = 'disconnected'; // disconnected/connecting/connected/reconnecting/failed
                    this.modelLoadStatus = 'unknown';       // unknown/loading/loaded/failed
                    this.serverMemory = 0;                  // 服务器内存使用

                    // 定时器
                    this.sessionTimer = null;
                    this.streamingTimer = null;
                    this.latencyTimer = null;

                    this.initializeTimers();
                }

                initializeTimers() {
                    // 会话时间更新定时器
                    this.sessionTimer = setInterval(() => {
                        this.updateSessionDurationDisplay();
                    }, 1000);

                    // 网络延迟检测定时器
                    this.latencyTimer = setInterval(() => {
                        this.checkNetworkLatency();
                    }, 10000); // 每10秒检测一次
                }

                // 开始计时（发送消息时调用）
                startTiming() {
                    this.startTime = Date.now();
                    this.firstByteTime = 0;
                    this.currentStreamStart = 0;
                    this.currentStreamChars = 0;
                    this.streamingSpeed = 0;

                    if (!this.firstMessageTime) {
                        this.firstMessageTime = Date.now();
                    }
                }

                // 记录首字节响应时间
                recordFirstByte() {
                    if (this.startTime > 0 && this.firstByteTime === 0) {
                        this.firstByteTime = Date.now() - this.startTime;
                        this.updateFirstByteTimeDisplay();

                        // 开始流式输出监控
                        this.currentStreamStart = Date.now();
                        this.currentStreamChars = 0;
                        this.startStreamingMonitor();
                    }
                }

                // 开始流式输出监控
                startStreamingMonitor() {
                    if (this.streamingTimer) {
                        clearInterval(this.streamingTimer);
                    }

                    this.streamingTimer = setInterval(() => {
                        this.updateStreamingSpeed();
                    }, 500); // 每500ms更新一次流式速度
                }

                // 停止流式输出监控
                stopStreamingMonitor() {
                    if (this.streamingTimer) {
                        clearInterval(this.streamingTimer);
                        this.streamingTimer = null;
                    }
                }

                // 更新流式输出字符数
                updateStreamingChars(newChars) {
                    this.currentStreamChars += newChars;
                    this.aiCharCount += newChars;
                    this.updateCharCountDisplays();
                }

                // 计算并更新流式输出速度
                updateStreamingSpeed() {
                    if (this.currentStreamStart > 0 && this.currentStreamChars > 0) {
                        const elapsed = (Date.now() - this.currentStreamStart) / 1000;
                        this.streamingSpeed = Math.round(this.currentStreamChars / elapsed);
                        this.updateStreamingSpeedDisplay();
                    }
                }

                // 结束计时（响应完成时调用）
                endTiming() {
                    if (this.startTime > 0) {
                        this.lastResponseTime = Date.now() - this.startTime;
                        this.updateResponseTimeDisplay();
                        this.startTime = 0;
                        this.stopStreamingMonitor();
                    }
                }

                // 增加对话轮数
                incrementConversation() {
                    this.conversationCount++;
                    this.updateConversationCountDisplay();
                    this.updateAverageCharsDisplay();
                }

                // 添加用户输入字符数
                addUserChars(count) {
                    this.userCharCount += count;
                    this.updateCharCountDisplays();
                    this.updateAverageCharsDisplay();
                }

                // 设置连接状态
                setConnectionStatus(status, modelName = '') {
                    this.connectionStatus = status;
                    this.currentModel = modelName;
                    this.isConnected = (status === 'connected');
                    this.updateConnectionStatusDisplay();
                }

                // 设置模型加载状态
                setModelLoadStatus(status) {
                    this.modelLoadStatus = status;
                    this.updateModelLoadStatusDisplay();
                }

                // 设置当前模式
                setCurrentMode(mode) {
                    this.currentMode = mode;
                    this.updateCurrentModeDisplay();
                }

                // 网络延迟检测
                async checkNetworkLatency() {
                    if (!this.isConnected) return;

                    try {
                        const startTime = Date.now();
                        const response = await fetch(apiConfig.config.baseURL + '/models', {
                            method: 'GET',
                            headers: { 'Content-Type': 'application/json' }
                        });

                        if (response.ok) {
                            this.networkLatency = Date.now() - startTime;
                            this.updateNetworkLatencyDisplay();
                        }
                    } catch (error) {
                        this.networkLatency = -1; // 表示检测失败
                        this.updateNetworkLatencyDisplay();
                    }
                }

                // 重置所有统计数据
                reset() {
                    this.conversationCount = 0;
                    this.lastResponseTime = 0;
                    this.userCharCount = 0;
                    this.aiCharCount = 0;
                    this.sessionStartTime = Date.now();
                    this.firstMessageTime = null;
                    this.firstByteTime = 0;
                    this.streamingSpeed = 0;
                    this.currentStreamStart = 0;
                    this.currentStreamChars = 0;

                    this.updateAllDisplays();
                }

                // 更新所有显示
                updateAllDisplays() {
                    this.updateResponseTimeDisplay();
                    this.updateConversationCountDisplay();
                    this.updateCharCountDisplays();
                    this.updateSessionDurationDisplay();
                    this.updateAverageCharsDisplay();
                    this.updateFirstByteTimeDisplay();
                    this.updateStreamingSpeedDisplay();
                    this.updateConnectionStatusDisplay();
                    this.updateModelLoadStatusDisplay();
                    this.updateCurrentModeDisplay();
                    this.updateNetworkLatencyDisplay();
                }

                // === 显示更新方法 ===

                updateResponseTimeDisplay() {
                    const display = document.getElementById('response-time-display');
                    if (display) {
                        if (this.lastResponseTime > 0) {
                            display.textContent = `${(this.lastResponseTime / 1000).toFixed(1)}s`;
                            display.className = 'font-semibold text-green-600';
                        } else {
                            display.textContent = '--';
                            display.className = 'font-semibold text-gray-500';
                        }
                    }
                }

                updateConversationCountDisplay() {
                    const display = document.getElementById('conversation-count-display');
                    if (display) {
                        display.textContent = this.conversationCount.toString();
                        display.className = 'font-semibold text-blue-600';
                    }
                }

                updateCharCountDisplays() {
                    // 用户字符数
                    const userDisplay = document.getElementById('user-char-count-display');
                    if (userDisplay) {
                        userDisplay.textContent = this.userCharCount.toLocaleString();
                        userDisplay.className = 'font-semibold text-blue-600';
                    }

                    // AI字符数
                    const aiDisplay = document.getElementById('ai-char-count-display');
                    if (aiDisplay) {
                        aiDisplay.textContent = this.aiCharCount.toLocaleString();
                        aiDisplay.className = 'font-semibold text-green-600';
                    }

                    // 总字符数
                    const totalDisplay = document.getElementById('total-char-count-display');
                    if (totalDisplay) {
                        const total = this.userCharCount + this.aiCharCount;
                        totalDisplay.textContent = total.toLocaleString();
                        totalDisplay.className = 'font-semibold text-purple-600';
                    }
                }

                updateSessionDurationDisplay() {
                    const display = document.getElementById('session-duration-display');
                    if (display) {
                        const startTime = this.firstMessageTime || this.sessionStartTime;
                        const duration = Math.floor((Date.now() - startTime) / 1000);

                        const hours = Math.floor(duration / 3600);
                        const minutes = Math.floor((duration % 3600) / 60);
                        const seconds = duration % 60;

                        const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                        display.textContent = timeStr;
                        display.className = 'font-semibold text-indigo-600';
                    }
                }

                updateAverageCharsDisplay() {
                    const display = document.getElementById('average-chars-display');
                    if (display) {
                        if (this.conversationCount > 0) {
                            const total = this.userCharCount + this.aiCharCount;
                            const average = Math.round(total / this.conversationCount);
                            display.textContent = average.toLocaleString();
                            display.className = 'font-semibold text-orange-600';
                        } else {
                            display.textContent = '--';
                            display.className = 'font-semibold text-gray-500';
                        }
                    }
                }

                updateFirstByteTimeDisplay() {
                    const display = document.getElementById('first-byte-time-display');
                    if (display) {
                        if (this.firstByteTime > 0) {
                            display.textContent = `${this.firstByteTime}ms`;

                            // 根据延迟设置颜色
                            if (this.firstByteTime < 500) {
                                display.className = 'font-semibold text-green-600';
                            } else if (this.firstByteTime < 1000) {
                                display.className = 'font-semibold text-yellow-600';
                            } else {
                                display.className = 'font-semibold text-red-600';
                            }
                        } else {
                            display.textContent = '--';
                            display.className = 'font-semibold text-gray-500';
                        }
                    }
                }

                updateStreamingSpeedDisplay() {
                    const display = document.getElementById('streaming-speed-display');
                    if (display) {
                        if (this.streamingSpeed > 0) {
                            display.textContent = `${this.streamingSpeed} 字符/秒`;

                            // 根据速度设置颜色
                            if (this.streamingSpeed > 50) {
                                display.className = 'font-semibold text-green-600';
                            } else if (this.streamingSpeed > 20) {
                                display.className = 'font-semibold text-yellow-600';
                            } else {
                                display.className = 'font-semibold text-red-600';
                            }
                        } else {
                            display.textContent = '--';
                            display.className = 'font-semibold text-gray-500';
                        }
                    }
                }

                updateConnectionStatusDisplay() {
                    const display = document.getElementById('connection-status-display');
                    if (display) {
                        const statusMap = {
                            'connected': { text: '已连接', class: 'font-semibold text-green-600' },
                            'connecting': { text: '连接中', class: 'font-semibold text-yellow-600' },
                            'reconnecting': { text: '重连中', class: 'font-semibold text-orange-600' },
                            'failed': { text: '连接失败', class: 'font-semibold text-red-600' },
                            'disconnected': { text: '未连接', class: 'font-semibold text-gray-600' }
                        };

                        const status = statusMap[this.connectionStatus] || statusMap['disconnected'];
                        display.textContent = status.text;
                        display.className = status.class;
                    }
                }

                updateModelLoadStatusDisplay() {
                    const display = document.getElementById('model-load-status-display');
                    if (display) {
                        const statusMap = {
                            'loaded': { text: '已加载', class: 'font-semibold text-green-600' },
                            'loading': { text: '加载中', class: 'font-semibold text-yellow-600' },
                            'failed': { text: '加载失败', class: 'font-semibold text-red-600' },
                            'unknown': { text: '未知', class: 'font-semibold text-gray-600' }
                        };

                        const status = statusMap[this.modelLoadStatus] || statusMap['unknown'];
                        display.textContent = status.text;
                        display.className = status.class;
                    }
                }

                updateCurrentModeDisplay() {
                    const display = document.getElementById('current-mode-display');
                    if (display) {
                        const modeNames = {
                            'text': '文本',
                            'image': '图像',
                            'multimodal': '多模态'
                        };
                        display.textContent = modeNames[this.currentMode] || '文本';

                        // 根据模式设置颜色
                        const colorClasses = {
                            'text': 'font-semibold text-blue-600',
                            'image': 'font-semibold text-purple-600',
                            'multimodal': 'font-semibold text-indigo-600'
                        };
                        display.className = colorClasses[this.currentMode] || 'font-semibold text-gray-600';
                    }
                }

                updateNetworkLatencyDisplay() {
                    const display = document.getElementById('network-latency-display');
                    if (display) {
                        if (this.networkLatency > 0) {
                            display.textContent = `${this.networkLatency}ms`;

                            // 根据延迟设置颜色
                            if (this.networkLatency < 100) {
                                display.className = 'font-semibold text-green-600';
                            } else if (this.networkLatency < 300) {
                                display.className = 'font-semibold text-yellow-600';
                            } else {
                                display.className = 'font-semibold text-red-600';
                            }
                        } else if (this.networkLatency === -1) {
                            display.textContent = '检测失败';
                            display.className = 'font-semibold text-red-600';
                        } else {
                            display.textContent = '--';
                            display.className = 'font-semibold text-gray-500';
                        }
                    }
                }

                // 清理定时器
                destroy() {
                    if (this.sessionTimer) {
                        clearInterval(this.sessionTimer);
                    }
                    if (this.streamingTimer) {
                        clearInterval(this.streamingTimer);
                    }
                    if (this.latencyTimer) {
                        clearInterval(this.latencyTimer);
                    }
                }
            }

            const performanceMonitor = new PerformanceMonitor();

            // 模型兼容性检查
            class ModelCompatibilityChecker {
                constructor() {
                    this.incompatiblePatterns = [
                        /embedding/i,
                        /embed/i,
                        /retrieval/i,
                        /rerank/i,
                        /classifier/i
                    ];

                    this.conversationalPatterns = [
                        /chat/i,
                        /instruct/i,
                        /conversation/i,
                        /llama/i,
                        /qwen/i,
                        /mistral/i,
                        /deepseek/i,
                        /yi/i,
                        /baichuan/i
                    ];
                }

                isConversationalModel(modelName) {
                    if (!modelName) return false;

                    // 检查是否为不兼容的模型类型
                    for (const pattern of this.incompatiblePatterns) {
                        if (pattern.test(modelName)) {
                            return false;
                        }
                    }

                    // 检查是否为已知的对话模型
                    for (const pattern of this.conversationalPatterns) {
                        if (pattern.test(modelName)) {
                            return true;
                        }
                    }

                    // 默认假设是对话模型（保守策略）
                    return true;
                }

                getModelWarning(modelName) {
                    if (!modelName) return null;

                    for (const pattern of this.incompatiblePatterns) {
                        if (pattern.test(modelName)) {
                            if (/embedding|embed/i.test(modelName)) {
                                return {
                                    type: 'error',
                                    message: '⚠️ 检测到嵌入模型：此模型用于生成文本向量，不支持对话功能。请选择对话模型（如llama、qwen、mistral等）。'
                                };
                            } else if (/retrieval/i.test(modelName)) {
                                return {
                                    type: 'error',
                                    message: '⚠️ 检测到检索模型：此模型用于信息检索，不支持对话功能。请选择对话模型。'
                                };
                            } else {
                                return {
                                    type: 'warning',
                                    message: '⚠️ 此模型可能不适合对话任务，建议选择专门的对话模型。'
                                };
                            }
                        }
                    }

                    return null;
                }

                showModelWarning(warning) {
                    if (!warning) return;

                    // 创建警告提示
                    const warningDiv = document.createElement('div');
                    warningDiv.className = `p-3 rounded-lg mb-4 ${warning.type === 'error' ? 'bg-red-50 border border-red-200' : 'bg-yellow-50 border border-yellow-200'}`;
                    warningDiv.innerHTML = `
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <i class="fa fa-exclamation-triangle ${warning.type === 'error' ? 'text-red-500' : 'text-yellow-500'}"></i>
                            </div>
                            <div class="ml-3">
                                <p class="${warning.type === 'error' ? 'text-red-700' : 'text-yellow-700'} text-sm">${warning.message}</p>
                            </div>
                            <button class="ml-auto flex-shrink-0 text-gray-400 hover:text-gray-600" onclick="this.parentElement.parentElement.remove()">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>
                    `;

                    // 插入到聊天容器前
                    const chatContainer = document.getElementById('chat-container');
                    if (chatContainer && chatContainer.parentNode) {
                        chatContainer.parentNode.insertBefore(warningDiv, chatContainer);
                    }
                }

                clearWarnings() {
                    // 移除所有警告
                    const warnings = document.querySelectorAll('.bg-red-50, .bg-yellow-50');
                    warnings.forEach(warning => {
                        if (warning.querySelector('.fa-exclamation-triangle')) {
                            warning.remove();
                        }
                    });
                }
            }

            const modelChecker = new ModelCompatibilityChecker();

            // 模型能力数据库 - 基于用户实际安装的模型
            const modelCapabilities = {
                // === 用户的Ollama模型 ===

                // Llama 3.3 (42GB) - 最新的大型文本生成模型
                'llama3.3:latest': {
                    text: { supported: true, description: 'Meta最新的Llama 3.3模型，70B参数规模，在推理、代码生成和复杂问答方面表现卓越，支持多语言对话。' },
                    image: { supported: false, description: '此模型专注于文本生成，不支持图像理解功能。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },
                'llama3.3': {
                    text: { supported: true, description: 'Meta最新的Llama 3.3模型，70B参数规模，在推理、代码生成和复杂问答方面表现卓越，支持多语言对话。' },
                    image: { supported: false, description: '此模型专注于文本生成，不支持图像理解功能。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },

                // Mistral Small 3.2 (15GB) - 高效文本模型
                'mistral-small3.2:latest': {
                    text: { supported: true, description: 'Mistral AI的Small 3.2模型，22B参数，在保持高质量输出的同时提供快速响应，擅长逻辑推理和代码任务。' },
                    image: { supported: false, description: '此模型专注于文本处理，不支持图像理解功能。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },
                'mistral-small3.2': {
                    text: { supported: true, description: 'Mistral AI的Small 3.2模型，22B参数，在保持高质量输出的同时提供快速响应，擅长逻辑推理和代码任务。' },
                    image: { supported: false, description: '此模型专注于文本处理，不支持图像理解功能。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },

                // Qwen2.5-7B-Instruct-1M (4.7GB) - 长上下文文本模型
                'yasserrmd/Qwen2.5-7B-Instruct-1M:latest': {
                    text: { supported: true, description: 'Qwen2.5-7B指令微调版本，支持100万token超长上下文，在中英文理解、长文档分析和复杂推理方面表现优秀。' },
                    image: { supported: false, description: '此模型专注于长文本处理，不支持图像理解功能。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },
                'yasserrmd/Qwen2.5-7B-Instruct-1M': {
                    text: { supported: true, description: 'Qwen2.5-7B指令微调版本，支持100万token超长上下文，在中英文理解、长文档分析和复杂推理方面表现优秀。' },
                    image: { supported: false, description: '此模型专注于长文本处理，不支持图像理解功能。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },

                // DeepSeek Janus Pro 7B (4.2GB) - 多模态模型
                'erwan2/DeepSeek-Janus-Pro-7B:latest': {
                    text: { supported: true, description: 'DeepSeek Janus Pro多模态模型，7B参数，专为视觉-语言任务设计，在图像理解和文本生成方面均表现出色。' },
                    image: { supported: true, description: '强大的视觉理解能力，支持图像描述、场景分析、OCR文字识别、图表解读和复杂视觉推理任务。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },
                'erwan2/DeepSeek-Janus-Pro-7B': {
                    text: { supported: true, description: 'DeepSeek Janus Pro多模态模型，7B参数，专为视觉-语言任务设计，在图像理解和文本生成方面均表现出色。' },
                    image: { supported: true, description: '强大的视觉理解能力，支持图像描述、场景分析、OCR文字识别、图表解读和复杂视觉推理任务。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },

                // Qwen2.5VL (6.0GB) - 多模态视觉语言模型
                'qwen2.5vl:latest': {
                    text: { supported: true, description: '阿里巴巴Qwen2.5-VL多模态模型，在中英文文本生成和理解方面表现优异，特别适合中文用户。' },
                    image: { supported: true, description: '先进的视觉语言模型，支持图像理解、中文OCR、图表分析、场景描述和多轮视觉对话，在中文视觉问答方面表现突出。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },
                'qwen2.5vl': {
                    text: { supported: true, description: '阿里巴巴Qwen2.5-VL多模态模型，在中英文文本生成和理解方面表现优异，特别适合中文用户。' },
                    image: { supported: true, description: '先进的视觉语言模型，支持图像理解、中文OCR、图表分析、场景描述和多轮视觉对话，在中文视觉问答方面表现突出。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },

                // LM Studio中的Qwen2.5-VL变体（用户实际使用的模型名称）
                'qwen/qwen2.5-vl-7b': {
                    text: { supported: true, description: '阿里巴巴Qwen2.5-VL多模态模型7B版本，专为LM Studio优化，在中英文文本生成和理解方面表现优异。' },
                    image: { supported: true, description: '强大的视觉语言模型，支持图像理解、中文OCR、图表分析、场景描述和多轮视觉对话，特别适合中文视觉问答任务。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },
                'qwen/qwen2.5-vl': {
                    text: { supported: true, description: '阿里巴巴Qwen2.5-VL多模态模型，专为LM Studio优化，在中英文文本生成和理解方面表现优异。' },
                    image: { supported: true, description: '强大的视觉语言模型，支持图像理解、中文OCR、图表分析、场景描述和多轮视觉对话，特别适合中文视觉问答任务。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },
                'qwen2.5-vl-7b': {
                    text: { supported: true, description: '阿里巴巴Qwen2.5-VL多模态模型7B版本，在中英文文本生成和理解方面表现优异。' },
                    image: { supported: true, description: '强大的视觉语言模型，支持图像理解、中文OCR、图表分析、场景描述和多轮视觉对话，特别适合中文视觉问答任务。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },
                'qwen2.5-vl': {
                    text: { supported: true, description: '阿里巴巴Qwen2.5-VL多模态模型，在中英文文本生成和理解方面表现优异。' },
                    image: { supported: true, description: '强大的视觉语言模型，支持图像理解、中文OCR、图表分析、场景描述和多轮视觉对话，特别适合中文视觉问答任务。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },

                // LM Studio中的Qwen2.5-VL变体
                'qwen/qwen2.5-vl-7b': {
                    text: { supported: true, description: '阿里巴巴Qwen2.5-VL多模态模型7B版本，专为LM Studio优化，在中英文文本生成和理解方面表现优异。' },
                    image: { supported: true, description: '强大的视觉语言模型，支持图像理解、中文OCR、图表分析、场景描述和多轮视觉对话，特别适合中文视觉问答任务。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },
                'qwen/qwen2.5-vl': {
                    text: { supported: true, description: '阿里巴巴Qwen2.5-VL多模态模型，专为LM Studio优化，在中英文文本生成和理解方面表现优异。' },
                    image: { supported: true, description: '强大的视觉语言模型，支持图像理解、中文OCR、图表分析、场景描述和多轮视觉对话，特别适合中文视觉问答任务。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },
                'qwen2.5-vl-7b': {
                    text: { supported: true, description: '阿里巴巴Qwen2.5-VL多模态模型7B版本，在中英文文本生成和理解方面表现优异。' },
                    image: { supported: true, description: '强大的视觉语言模型，支持图像理解、中文OCR、图表分析、场景描述和多轮视觉对话，特别适合中文视觉问答任务。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },
                'qwen2.5-vl': {
                    text: { supported: true, description: '阿里巴巴Qwen2.5-VL多模态模型，在中英文文本生成和理解方面表现优异。' },
                    image: { supported: true, description: '强大的视觉语言模型，支持图像理解、中文OCR、图表分析、场景描述和多轮视觉对话，特别适合中文视觉问答任务。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },

                // Qwen3 (5.2GB) - 新一代文本模型
                'qwen3:latest': {
                    text: { supported: true, description: 'Qwen3最新版本，在中英文理解、代码生成、数学推理和创意写作方面有显著提升，响应速度快。' },
                    image: { supported: false, description: '此模型专注于文本处理，不支持图像理解功能。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },
                'qwen3': {
                    text: { supported: true, description: 'Qwen3最新版本，在中英文理解、代码生成、数学推理和创意写作方面有显著提升，响应速度快。' },
                    image: { supported: false, description: '此模型专注于文本处理，不支持图像理解功能。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },

                // Nomic Embed Text (274MB) - 文本嵌入模型
                'nomic-embed-text:latest': {
                    text: { supported: false, description: '这是一个文本嵌入模型，用于生成文本向量表示，不适用于对话生成任务。请选择其他对话模型。' },
                    image: { supported: false, description: '嵌入模型不支持图像理解功能。' },
                    audio: { supported: false, description: '嵌入模型不支持语音交互功能。' }
                },
                'nomic-embed-text': {
                    text: { supported: false, description: '这是一个文本嵌入模型，用于生成文本向量表示，不适用于对话生成任务。请选择其他对话模型。' },
                    image: { supported: false, description: '嵌入模型不支持图像理解功能。' },
                    audio: { supported: false, description: '嵌入模型不支持语音交互功能。' }
                },

                // === 通用模型名称匹配（不带版本标签） ===
                'llama3.3': {
                    text: { supported: true, description: 'Meta最新的Llama 3.3模型，70B参数规模，在推理、代码生成和复杂问答方面表现卓越，支持多语言对话。' },
                    image: { supported: false, description: '此模型专注于文本生成，不支持图像理解功能。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },

                // === LM Studio 兼容性支持 ===
                // LM Studio通常使用不同的模型命名格式，添加常见的LM Studio模型
                'microsoft/DialoGPT-medium': {
                    text: { supported: true, description: 'Microsoft DialoGPT对话模型，专为多轮对话设计，在对话连贯性方面表现良好。' },
                    image: { supported: false, description: '此模型专注于对话生成，不支持图像理解功能。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },

                // === 默认配置（用于未知模型） ===
                'default': {
                    text: { supported: true, description: '未知模型类型，假设支持基础文本对话功能。建议查看模型文档确认具体能力。' },
                    image: { supported: false, description: '未知模型的图像理解能力，建议查看模型文档或尝试上传图片测试。' },
                    audio: { supported: false, description: '未知模型的语音交互能力，建议查看模型文档确认是否支持。' }
                }
            };

            // 智能模型匹配函数
            function getModelCapabilities(modelName) {
                // 直接匹配
                if (modelCapabilities[modelName]) {
                    return modelCapabilities[modelName];
                }

                // 增强的模糊匹配算法
                const normalizedName = modelName.toLowerCase();
                const modelKeys = Object.keys(modelCapabilities);

                // 第一轮：精确匹配（包括命名空间）
                for (const key of modelKeys) {
                    if (key === 'default') continue;
                    const keyLower = key.toLowerCase();
                    if (normalizedName === keyLower) {
                        return modelCapabilities[key];
                    }
                }

                // 第二轮：包含关系匹配
                for (const key of modelKeys) {
                    if (key === 'default') continue;
                    const keyLower = key.toLowerCase();
                    if (normalizedName.includes(keyLower) || keyLower.includes(normalizedName)) {
                        return modelCapabilities[key];
                    }
                }

                // 第三轮：智能模式匹配（处理连字符、版本号等）
                for (const key of modelKeys) {
                    if (key === 'default') continue;

                    // 标准化处理：移除连字符、点、版本号等
                    const normalizeForMatching = (name) => {
                        return name.toLowerCase()
                            .replace(/[-_.]/g, '')  // 移除连字符、下划线、点
                            .replace(/:\w+$/, '')   // 移除版本标签 (:latest, :v1等)
                            .replace(/\/.*?\//, '') // 移除命名空间前缀
                            .replace(/\d+b$/, '')   // 移除模型大小标识 (7b, 13b等)
                            .replace(/v\d+/, '')    // 移除版本号 (v1, v2等)
                            .trim();
                    };

                    const normalizedInput = normalizeForMatching(normalizedName);
                    const normalizedKey = normalizeForMatching(key);

                    if (normalizedInput === normalizedKey ||
                        normalizedInput.includes(normalizedKey) ||
                        normalizedKey.includes(normalizedInput)) {
                        return modelCapabilities[key];
                    }
                }

                // 第四轮：基础名称匹配（移除命名空间和版本）
                const baseModelName = normalizedName.split(':')[0].split('/').pop();
                for (const key of modelKeys) {
                    if (key === 'default') continue;
                    const baseKeyName = key.toLowerCase().split(':')[0].split('/').pop();
                    if (baseModelName === baseKeyName) {
                        return modelCapabilities[key];
                    }
                }

                // 如果没有找到匹配，返回默认配置
                return modelCapabilities['default'];
            }

            // 更新模型能力显示
            function updateModelCapabilities(modelName) {
                // 清除之前的警告
                modelChecker.clearWarnings();

                // 检查模型兼容性
                const warning = modelChecker.getModelWarning(modelName);
                if (warning) {
                    modelChecker.showModelWarning(warning);
                }

                // 更新性能监控显示
                const isConversational = modelChecker.isConversationalModel(modelName);
                performanceMonitor.setConnectionStatus(isConversational, modelName);

                // 获取模型能力信息（使用智能匹配）
                const capabilities = getModelCapabilities(modelName);

                // 在控制台显示匹配结果（用于调试）
                console.log(`模型 "${modelName}" 匹配到能力配置:`, capabilities);
                console.log(`模型兼容性检查:`, { isConversational, warning });

                // 详细的匹配调试信息
                console.log('=== 模型匹配调试信息 ===');
                console.log('原始模型名称:', modelName);
                console.log('标准化名称:', modelName.toLowerCase());
                console.log('基础名称:', modelName.split(':')[0].split('/').pop());
                console.log('可用的模型键:', Object.keys(modelCapabilities).filter(k => k !== 'default'));
                console.log('匹配结果:', capabilities === modelCapabilities['default'] ? '使用默认配置（未匹配）' : '成功匹配');
                console.log('========================');

                // 更新文本推理卡片
                const textCard = document.querySelector('[onclick="selectMode(\'文本\')"]');
                if (textCard) {
                    const title = textCard.querySelector('h3');
                    const description = textCard.querySelector('p');
                    const icon = textCard.querySelector('i');

                    if (capabilities.text.supported) {
                        textCard.className = 'bg-white rounded-xl p-4 shadow-sm border-l-4 border-primary hover:shadow-md transition-shadow card-hover cursor-pointer';
                        icon.className = 'fa fa-file-text-o text-primary text-xl mr-3';
                        title.textContent = '文本推理';
                        description.textContent = capabilities.text.description;
                    } else {
                        textCard.className = 'bg-gray-100 rounded-xl p-4 shadow-sm border-l-4 border-gray-400 cursor-not-allowed opacity-60';
                        icon.className = 'fa fa-file-text-o text-gray-400 text-xl mr-3';
                        title.textContent = '文本推理 (不支持)';
                        description.textContent = capabilities.text.description;
                    }
                }

                // 更新图像理解卡片
                const imageCard = document.querySelector('[onclick="selectMode(\'图像\')"]');
                if (imageCard) {
                    const title = imageCard.querySelector('h3');
                    const description = imageCard.querySelector('p');
                    const icon = imageCard.querySelector('i');

                    if (capabilities.image.supported) {
                        imageCard.className = 'bg-white rounded-xl p-4 shadow-sm border-l-4 border-tertiary hover:shadow-md transition-shadow card-hover cursor-pointer';
                        icon.className = 'fa fa-image text-tertiary text-xl mr-3';
                        title.textContent = '图像理解';
                        description.textContent = capabilities.image.description;
                    } else {
                        imageCard.className = 'bg-gray-100 rounded-xl p-4 shadow-sm border-l-4 border-gray-400 cursor-not-allowed opacity-60';
                        icon.className = 'fa fa-image text-gray-400 text-xl mr-3';
                        title.textContent = '图像理解 (不支持)';
                        description.textContent = capabilities.image.description;
                    }
                }

                // 更新语音交互卡片
                const audioCard = document.querySelector('[onclick="selectMode(\'语音\')"]');
                if (audioCard) {
                    const title = audioCard.querySelector('h3');
                    const description = audioCard.querySelector('p');
                    const icon = audioCard.querySelector('i');

                    if (capabilities.audio.supported) {
                        audioCard.className = 'bg-white rounded-xl p-4 shadow-sm border-l-4 border-secondary hover:shadow-md transition-shadow card-hover cursor-pointer';
                        icon.className = 'fa fa-microphone text-secondary text-xl mr-3';
                        title.textContent = '语音交互';
                        description.textContent = capabilities.audio.description;
                    } else {
                        audioCard.className = 'bg-gray-100 rounded-xl p-4 shadow-sm border-l-4 border-gray-400 cursor-not-allowed opacity-60';
                        icon.className = 'fa fa-microphone text-gray-400 text-xl mr-3';
                        title.textContent = '语音交互 (不支持)';
                        description.textContent = capabilities.audio.description;
                    }
                }

                // 更新系统消息中的能力描述
                const systemMessage = document.querySelector('.bg-layer3 p');
                if (systemMessage) {
                    let supportedFeatures = [];
                    if (capabilities.text.supported) supportedFeatures.push('文本对话');
                    if (capabilities.image.supported) supportedFeatures.push('图像理解');
                    if (capabilities.audio.supported) supportedFeatures.push('语音交互');

                    const featuresText = supportedFeatures.length > 0 ? supportedFeatures.join('和') : '基础功能';
                    systemMessage.textContent = `欢迎使用本地大模型助手！当前模型 ${modelName} 支持${featuresText}。您可以直接输入问题${capabilities.image.supported ? '，或使用右侧按钮上传图片进行多模态交互' : ''}。`;
                }
            }

            // 连接状态管理
            function updateConnectionStatus(connected, message = '', version = '') {
                if (connected) {
                    statusIndicator.className = 'w-2 h-2 rounded-full bg-green-500';
                    statusText.textContent = version ? `已连接 (${version})` : '已连接';
                    statusText.className = 'text-xs text-green-600';
                } else {
                    statusIndicator.className = 'w-2 h-2 rounded-full bg-red-500';
                    statusText.textContent = message || '连接失败';
                    statusText.className = 'text-xs text-red-600';
                }
            }

            // 检查连接状态
            async function checkConnectionStatus() {
                if (apiConfig.config.apiType === 'ollama') {
                    const result = await apiConfig.checkOllamaConnection();
                    updateConnectionStatus(result.connected, result.error, result.version);
                    return result.connected;
                } else if (apiConfig.config.apiType === 'lm-studio') {
                    try {
                        const response = await fetch(`${apiConfig.config.baseURL}/models`, {
                            method: 'GET',
                            headers: { 'Content-Type': 'application/json' }
                        });

                        if (response.ok) {
                            updateConnectionStatus(true, '', 'LM Studio');
                            return true;
                        } else {
                            updateConnectionStatus(false, `HTTP ${response.status}`, '');
                            return false;
                        }
                    } catch (error) {
                        updateConnectionStatus(false, error.message, '');
                        return false;
                    }
                } else {
                    // 对于其他API类型，暂时显示未知状态
                    statusIndicator.className = 'w-2 h-2 rounded-full bg-yellow-500';
                    statusText.textContent = '未检测';
                    statusText.className = 'text-xs text-yellow-600';
                    return true;
                }
            }

            // 刷新模型列表
            async function refreshModelList() {
                if (apiConfig.config.apiType === 'ollama') {
                    await refreshOllamaModels();
                } else if (apiConfig.config.apiType === 'lm-studio') {
                    await refreshLMStudioModels();
                } else {
                    modelStatus.textContent = '此API类型不支持自动检测模型，请手动输入模型名称';
                    return;
                }
            }

            // 刷新Ollama模型列表
            async function refreshOllamaModels() {

                refreshModels.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
                refreshModels.disabled = true;
                modelStatus.textContent = '正在获取Ollama模型列表...';

                try {
                    const models = await apiConfig.getOllamaModels();

                    // 清空现有选项
                    modelSelect.innerHTML = '';

                    if (models.length === 0) {
                        modelSelect.innerHTML = '<option value="">未找到模型</option>';
                        modelStatus.textContent = '未找到已安装的模型，请先使用 ollama pull 下载模型';
                        modelStatus.className = 'mt-1 text-xs text-red-500';
                    } else {
                        // 添加模型选项
                        models.forEach(model => {
                            const option = document.createElement('option');
                            option.value = model.name;
                            option.textContent = `${model.name} (${(model.size / 1024 / 1024 / 1024).toFixed(1)}GB)`;
                            modelSelect.appendChild(option);
                        });

                        // 设置当前选中的模型
                        if (apiConfig.config.model && models.find(m => m.name === apiConfig.config.model)) {
                            modelSelect.value = apiConfig.config.model;
                        } else if (models.length > 0) {
                            modelSelect.value = models[0].name;
                            apiConfig.config.model = models[0].name;
                        }

                        modelStatus.textContent = `找到 ${models.length} 个Ollama模型`;
                        modelStatus.className = 'mt-1 text-xs text-green-600';

                        // 更新模型能力显示
                        updateModelCapabilities(modelSelect.value);
                    }
                } catch (error) {
                    console.error('获取Ollama模型列表失败:', error);
                    modelStatus.textContent = `获取Ollama模型失败: ${error.message}`;
                    modelStatus.className = 'mt-1 text-xs text-red-500';

                    // 添加默认选项
                    modelSelect.innerHTML = '<option value="llama3.3:latest">llama3.3:latest (手动输入)</option>';
                } finally {
                    refreshModels.innerHTML = '<i class="fa fa-refresh"></i>';
                    refreshModels.disabled = false;
                }
            }

            // 刷新LM Studio模型列表
            async function refreshLMStudioModels() {
                refreshModels.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
                refreshModels.disabled = true;
                modelStatus.textContent = '正在获取LM Studio模型列表...';

                try {
                    // LM Studio使用OpenAI兼容的API获取模型列表
                    const response = await fetch(`${apiConfig.config.baseURL}/models`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            ...(apiConfig.config.apiKey && { 'Authorization': `Bearer ${apiConfig.config.apiKey}` })
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    const models = data.data || [];

                    // 清空现有选项
                    modelSelect.innerHTML = '';

                    if (models.length === 0) {
                        modelSelect.innerHTML = '<option value="">未找到模型</option>';
                        modelStatus.textContent = '未找到已加载的模型，请在LM Studio中加载模型';
                        modelStatus.className = 'mt-1 text-xs text-red-500';
                    } else {
                        // 添加模型选项
                        models.forEach(model => {
                            const option = document.createElement('option');
                            option.value = model.id;
                            option.textContent = model.id;
                            modelSelect.appendChild(option);
                        });

                        // 设置当前选中的模型
                        if (apiConfig.config.model && models.find(m => m.id === apiConfig.config.model)) {
                            modelSelect.value = apiConfig.config.model;
                        } else if (models.length > 0) {
                            modelSelect.value = models[0].id;
                            apiConfig.config.model = models[0].id;
                        }

                        modelStatus.textContent = `找到 ${models.length} 个LM Studio模型`;
                        modelStatus.className = 'mt-1 text-xs text-green-600';

                        // 更新模型能力显示
                        updateModelCapabilities(modelSelect.value);
                    }
                } catch (error) {
                    console.error('获取LM Studio模型列表失败:', error);
                    modelStatus.textContent = `获取LM Studio模型失败: ${error.message}`;
                    modelStatus.className = 'mt-1 text-xs text-red-500';

                    // 添加默认选项
                    modelSelect.innerHTML = '<option value="local-model">local-model (手动输入)</option>';
                } finally {
                    refreshModels.innerHTML = '<i class="fa fa-refresh"></i>';
                    refreshModels.disabled = false;
                }
            }

            // API通信类
            class LLMAPIClient {
                constructor(config) {
                    this.config = config;
                }

                async sendMessage(messages, onChunk = null) {
                    try {
                        let endpoint, requestBody, headers;

                        if (this.config.config.apiType === 'ollama') {
                            // Ollama API格式
                            endpoint = `${this.config.config.baseURL}/api/chat`;
                            requestBody = {
                                model: this.config.config.model,
                                messages: messages,
                                stream: this.config.config.streamOutput && onChunk !== null,
                                options: {
                                    temperature: this.config.config.temperature,
                                    num_predict: this.config.config.maxTokens
                                }
                            };
                            headers = { 'Content-Type': 'application/json' };
                        } else {
                            // OpenAI兼容API格式
                            endpoint = `${this.config.config.baseURL}/chat/completions`;

                            // 确保消息格式正确
                            const formattedMessages = messages.map(msg => {
                                if (typeof msg.content === 'string') {
                                    return msg;
                                } else if (Array.isArray(msg.content)) {
                                    // 处理多模态消息格式
                                    return {
                                        role: msg.role,
                                        content: msg.content
                                    };
                                } else {
                                    return {
                                        role: msg.role,
                                        content: String(msg.content || '')
                                    };
                                }
                            });

                            requestBody = {
                                model: this.config.config.model,
                                messages: formattedMessages,
                                temperature: this.config.config.temperature,
                                max_tokens: this.config.config.maxTokens,
                                stream: this.config.config.streamOutput && onChunk !== null
                            };

                            headers = {
                                'Content-Type': 'application/json',
                                ...(this.config.config.apiKey && { 'Authorization': `Bearer ${this.config.config.apiKey}` })
                            };
                        }

                        console.log('API请求详情:', {
                            endpoint,
                            headers,
                            requestBody: JSON.stringify(requestBody, null, 2)
                        });

                        const response = await fetch(endpoint, {
                            method: 'POST',
                            headers: headers,
                            body: JSON.stringify(requestBody)
                        });

                        if (!response.ok) {
                            let errorDetails = '';
                            try {
                                const errorData = await response.text();
                                errorDetails = errorData;
                                console.error('API错误响应:', errorData);
                            } catch (e) {
                                console.error('无法读取错误响应:', e);
                            }

                            throw new Error(`API请求失败: ${response.status} ${response.statusText}${errorDetails ? '\n详情: ' + errorDetails : ''}`);
                        }

                        if (this.config.config.streamOutput && onChunk) {
                            return this.handleStreamResponse(response, onChunk);
                        } else {
                            const data = await response.json();
                            if (this.config.config.apiType === 'ollama') {
                                return data.message.content;
                            } else {
                                return data.choices[0].message.content;
                            }
                        }
                    } catch (error) {
                        console.error('API调用错误:', error);
                        throw error;
                    }
                }

                async handleStreamResponse(response, onChunk) {
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    let fullResponse = '';

                    try {
                        while (true) {
                            const { done, value } = await reader.read();
                            if (done) break;

                            const chunk = decoder.decode(value);
                            const lines = chunk.split('\n');

                            for (const line of lines) {
                                if (line.trim() === '') continue;

                                if (this.config.config.apiType === 'ollama') {
                                    // Ollama流式响应格式
                                    try {
                                        const parsed = JSON.parse(line);
                                        if (parsed.message?.content) {
                                            const content = parsed.message.content;
                                            fullResponse += content;
                                            onChunk(content);
                                        }
                                        if (parsed.done) return fullResponse;
                                    } catch (e) {
                                        // 忽略解析错误
                                    }
                                } else {
                                    // OpenAI兼容流式响应格式
                                    if (line.startsWith('data: ')) {
                                        const data = line.slice(6);
                                        if (data === '[DONE]') return fullResponse;

                                        try {
                                            const parsed = JSON.parse(data);
                                            const content = parsed.choices?.[0]?.delta?.content;
                                            if (content) {
                                                fullResponse += content;
                                                onChunk(content);
                                            }
                                        } catch (e) {
                                            // 忽略解析错误
                                        }
                                    }
                                }
                            }
                        }
                    } finally {
                        reader.releaseLock();
                    }

                    return fullResponse;
                }

                async sendImageMessage(imageData, prompt, onChunk = null) {
                    try {
                        let messages;

                        if (this.config.config.apiType === 'ollama') {
                            // Ollama支持图像的格式
                            messages = [
                                {
                                    role: 'user',
                                    content: prompt,
                                    images: [imageData.split(',')[1]] // 移除data:image/...;base64,前缀
                                }
                            ];
                        } else {
                            // OpenAI兼容格式
                            messages = [
                                {
                                    role: 'user',
                                    content: [
                                        { type: 'text', text: prompt },
                                        { type: 'image_url', image_url: { url: imageData } }
                                    ]
                                }
                            ];
                        }

                        return await this.sendMessage(messages, onChunk);
                    } catch (error) {
                        console.error('图像API调用错误:', error);
                        throw error;
                    }
                }
            }

            const apiClient = new LLMAPIClient(apiConfig);

            // 监听滚动事件，改变导航栏样式
            window.addEventListener('scroll', function() {
                if (window.scrollY > 10) {
                    header.classList.add('shadow-md', 'py-2');
                    header.classList.remove('py-3');
                } else {
                    header.classList.remove('shadow-md', 'py-2');
                    header.classList.add('py-3');
                }
            });
            
            // 发送消息
            async function sendMessage() {
                const message = userInput.value.trim();
                if (!message && !currentImageData) return;

                // 检查模型兼容性（使用智能匹配）
                const currentModel = apiConfig.config.model;

                // 检查模型是否适合对话
                if (!modelChecker.isConversationalModel(currentModel)) {
                    alert('当前选择的模型不适合对话任务，请在设置中选择合适的对话模型（如llama、qwen、mistral等）。');
                    openSettings();
                    return;
                }

                // 开始性能计时和监控
                performanceMonitor.startTiming();
                performanceMonitor.setConnectionStatus('connecting', currentModel);
                performanceMonitor.addUserChars(message.length);

                const capabilities = getModelCapabilities(currentModel);

                // 如果有图片但模型不支持图像理解
                if (currentImageData && !capabilities.image.supported) {
                    alert(`当前模型 "${currentModel}" 不支持图像理解功能。\n请先切换到支持图像的模型（如 llama3.2-vision、llava、qwen2-vl 等），或移除图片后发送纯文本消息。`);
                    return;
                }

                // 禁用发送按钮
                sendButton.disabled = true;
                sendButton.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';

                try {
                    // 添加用户消息到聊天框
                    if (currentImageData) {
                        appendMessage('user', `
                            <div class="mb-2">
                                <img src="${currentImageData}" alt="上传的图片" class="rounded-lg max-w-full max-h-64 object-contain">
                            </div>
                            <p>${message || '请分析这张图片'}</p>
                        `);
                    } else {
                        appendMessage('user', message);
                    }

                    // 添加到对话历史
                    if (currentImageData) {
                        if (apiConfig.config.apiType === 'ollama') {
                            conversationHistory.push({
                                role: 'user',
                                content: message || '请分析这张图片',
                                images: [currentImageData.split(',')[1]]
                            });
                        } else {
                            conversationHistory.push({
                                role: 'user',
                                content: [
                                    { type: 'text', text: message || '请分析这张图片' },
                                    { type: 'image_url', image_url: { url: currentImageData } }
                                ]
                            });
                        }
                    } else {
                        conversationHistory.push({ role: 'user', content: message });
                    }

                    // 清空输入框和图片数据
                    userInput.value = '';
                    currentImageData = null;

                    // 显示机器人正在输入的状态
                    const typingIndicator = showTypingIndicator();

                    // 准备消息历史（包含系统提示）
                    const messages = [
                        { role: 'system', content: apiConfig.config.systemPrompt },
                        ...conversationHistory
                    ];

                    let botResponse = '';
                    let responseElement = null;

                    // 添加重试机制
                    let retryCount = 0;
                    const maxRetries = 2;

                    while (retryCount <= maxRetries) {
                        try {
                            if (apiConfig.config.streamOutput) {
                                // 流式响应
                                removeTypingIndicator();
                                responseElement = appendMessage('bot', '', true); // 创建空的响应元素

                                botResponse = await apiClient.sendMessage(messages, (chunk) => {
                                    updateStreamingMessage(responseElement, chunk);
                                });
                            } else {
                                // 非流式响应
                                performanceMonitor.recordFirstByte(); // 记录首字节时间
                                performanceMonitor.setConnectionStatus('connected', apiConfig.config.model);
                                botResponse = await apiClient.sendMessage(messages);
                                removeTypingIndicator();
                                appendMessage('bot', botResponse);

                                // 添加AI字符计数
                                performanceMonitor.updateStreamingChars(botResponse.length);
                            }
                            break; // 成功则跳出重试循环
                        } catch (retryError) {
                            retryCount++;
                            if (retryCount <= maxRetries) {
                                console.log(`第${retryCount}次重试...`);
                                if (responseElement) {
                                    updateStreamingMessage(responseElement, `\n\n[连接失败，正在重试 ${retryCount}/${maxRetries}...]`);
                                }
                                await new Promise(resolve => setTimeout(resolve, 1000 * retryCount)); // 递增延迟
                            } else {
                                throw retryError; // 重试次数用完，抛出错误
                            }
                        }
                    }

                    // 添加到对话历史
                    conversationHistory.push({ role: 'assistant', content: botResponse });

                    // 限制对话历史长度
                    if (conversationHistory.length > 20) {
                        conversationHistory = conversationHistory.slice(-20);
                    }

                    // 结束性能计时并增加对话计数
                    performanceMonitor.endTiming();
                    performanceMonitor.incrementConversation();

                } catch (error) {
                    removeTypingIndicator();
                    performanceMonitor.setConnectionStatus('failed', apiConfig.config.model);
                    console.error('发送消息失败:', error);

                    let errorMessage = '抱歉，发送消息时出现错误。';
                    let troubleshootingTips = '';

                    if (error.message.includes('Failed to fetch') || error.message.includes('fetch')) {
                        if (apiConfig.config.apiType === 'ollama') {
                            errorMessage = '无法连接到Ollama服务';
                            troubleshootingTips = `
<div class="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-sm">
    <div class="font-medium text-yellow-800 mb-2">故障排除步骤：</div>
    <ol class="list-decimal list-inside space-y-1 text-yellow-700">
        <li>确认Ollama服务正在运行：<code class="bg-yellow-100 px-1 rounded">ollama serve</code></li>
        <li>检查模型是否已下载：<code class="bg-yellow-100 px-1 rounded">ollama list</code></li>
        <li>如果没有模型，请下载：<code class="bg-yellow-100 px-1 rounded">ollama pull llama3.2</code></li>
        <li>访问 <a href="http://localhost:11434" target="_blank" class="text-blue-600 underline">http://localhost:11434</a> 确认服务运行</li>
    </ol>
</div>`;
                        } else {
                            errorMessage = '无法连接到API服务';
                            troubleshootingTips = `
<div class="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-sm">
    <div class="font-medium text-yellow-800 mb-2">请检查：</div>
    <ul class="list-disc list-inside space-y-1 text-yellow-700">
        <li>API服务是否正在运行</li>
        <li>API地址是否正确</li>
        <li>网络连接是否正常</li>
    </ul>
</div>`;
                        }
                    } else if (error.message.includes('404')) {
                        errorMessage = '模型未找到或API端点错误';
                        if (apiConfig.config.apiType === 'ollama') {
                            troubleshootingTips = `
<div class="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg text-sm">
    <div class="font-medium text-red-800 mb-2">模型问题：</div>
    <p class="text-red-700">请确认模型 <code class="bg-red-100 px-1 rounded">${apiConfig.config.model}</code> 已下载</p>
    <p class="text-red-700 mt-1">运行：<code class="bg-red-100 px-1 rounded">ollama pull ${apiConfig.config.model}</code></p>
</div>`;
                        }
                    } else if (error.message.includes('500')) {
                        errorMessage = '服务器内部错误';
                        troubleshootingTips = `
<div class="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg text-sm">
    <div class="text-red-700">服务器可能遇到问题，请稍后重试或检查服务器日志</div>
</div>`;
                    } else {
                        errorMessage = `请求失败: ${error.message}`;
                    }

                    appendMessage('bot', `
<div class="text-red-600">
    <i class="fa fa-exclamation-triangle mr-2"></i>${errorMessage}
    ${troubleshootingTips}
    <div class="mt-3 text-xs text-gray-500">
        <button onclick="location.reload()" class="text-blue-600 hover:underline">刷新页面重试</button> |
        <button onclick="document.getElementById('settings-button').click()" class="text-blue-600 hover:underline">检查设置</button>
    </div>
</div>`);
                } finally {
                    // 重新启用发送按钮
                    sendButton.disabled = false;
                    sendButton.innerHTML = '<i class="fa fa-paper-plane"></i>';
                }
            }
            
            // 添加消息到聊天框
            function appendMessage(sender, message, isStreaming = false) {
                const messageDiv = document.createElement('div');
                messageDiv.className = sender === 'user'
                    ? 'flex items-start justify-end'
                    : 'flex items-start';

                if (sender === 'user') {
                    messageDiv.innerHTML = `
                        <div class="chat-message chat-message-user card-hover">
                            <div>${message}</div>
                        </div>
                        <div class="w-8 h-8 rounded-full bg-user flex items-center justify-center ml-3 flex-shrink-0">
                            <i class="fa fa-user text-white"></i>
                        </div>
                    `;
                } else {
                    const messageContent = isStreaming ? '<span class="streaming-content"></span><span class="cursor">|</span>' : `<div>${message}</div>`;
                    messageDiv.innerHTML = `
                        <div class="w-8 h-8 rounded-full bg-chatbot flex items-center justify-center mr-3 flex-shrink-0">
                            <i class="fa fa-robot text-white"></i>
                        </div>
                        <div class="chat-message chat-message-bot card-hover">
                            ${messageContent}
                        </div>
                    `;
                }

                chatContainer.appendChild(messageDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;

                // 添加消息发送音效
                if (!isStreaming) {
                    playNotificationSound();
                }

                return isStreaming ? messageDiv.querySelector('.streaming-content') : null;
            }

            // 更新流式消息
            function updateStreamingMessage(element, chunk) {
                if (element) {
                    // 记录首字节响应时间
                    if (element.textContent === '') {
                        performanceMonitor.recordFirstByte();
                        performanceMonitor.setConnectionStatus('connected', apiConfig.config.model);
                    }

                    element.textContent += chunk;

                    // 更新流式字符计数
                    performanceMonitor.updateStreamingChars(chunk.length);

                    chatContainer.scrollTop = chatContainer.scrollHeight;
                }
            }

            // 完成流式消息
            function finishStreamingMessage(messageDiv) {
                const cursor = messageDiv.querySelector('.cursor');
                if (cursor) {
                    cursor.remove();
                }
                playNotificationSound();
            }
            
            // 播放通知音效
            function playNotificationSound() {
                // 在实际应用中，可以添加真实的音效
                // 这里仅作为交互反馈的示例
                console.log("Message notification sound played");
            }
            
            // 显示打字指示器
            function showTypingIndicator() {
                const typingDiv = document.createElement('div');
                typingDiv.className = 'flex items-start';
                typingDiv.id = 'typing-indicator';
                typingDiv.innerHTML = `
                    <div class="w-8 h-8 rounded-full bg-chatbot flex items-center justify-center mr-3 flex-shrink-0">
                        <i class="fa fa-robot text-white"></i>
                    </div>
                    <div class="chat-message chat-message-bot">
                        <div class="typing-indicator">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                `;
                
                chatContainer.appendChild(typingDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }
            
            // 移除打字指示器
            function removeTypingIndicator() {
                const typingIndicator = document.getElementById('typing-indicator');
                if (typingIndicator) {
                    typingIndicator.remove();
                }
            }
            
            // 发送按钮点击事件
            sendButton.addEventListener('click', sendMessage);
            
            // 输入框回车事件
            userInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    if (!sendButton.disabled) {
                        sendMessage();
                    }
                }
            });

            // 自动调整输入框高度
            userInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });
            
            // 图片上传事件
            imageUpload.addEventListener('change', function(e) {
                if (e.target.files && e.target.files[0]) {
                    const file = e.target.files[0];

                    // 检查当前模型是否支持图像理解（使用智能匹配）
                    const currentModel = apiConfig.config.model;
                    const capabilities = getModelCapabilities(currentModel);

                    if (!capabilities.image.supported) {
                        alert(`当前模型 "${currentModel}" 不支持图像理解功能。\n请切换到支持图像的模型（如 qwen2.5vl、erwan2/DeepSeek-Janus-Pro-7B 等）。`);
                        e.target.value = '';
                        return;
                    }

                    // 检查文件大小 (限制为10MB)
                    if (file.size > 10 * 1024 * 1024) {
                        alert('图片文件过大，请选择小于10MB的图片');
                        return;
                    }

                    // 检查文件类型
                    if (!file.type.startsWith('image/')) {
                        alert('请选择有效的图片文件');
                        return;
                    }

                    const reader = new FileReader();

                    reader.onload = function(e) {
                        currentImageData = e.target.result;

                        // 在输入框旁边显示预览
                        showImagePreview(currentImageData);

                        // 更新输入框提示
                        userInput.placeholder = '输入对图片的问题或直接发送...';
                        userInput.focus();
                    }

                    reader.onerror = function() {
                        alert('读取图片文件失败，请重试');
                    }

                    reader.readAsDataURL(file);
                }

                // 清空文件输入，允许重复选择同一文件
                e.target.value = '';
            });

            // 显示图片预览
            function showImagePreview(imageData) {
                // 移除现有预览
                const existingPreview = document.querySelector('.image-preview');
                if (existingPreview) {
                    existingPreview.remove();
                }

                // 创建预览元素
                const previewDiv = document.createElement('div');
                previewDiv.className = 'image-preview flex items-center bg-blue-50 border border-blue-200 rounded-lg p-2 mb-2';
                previewDiv.innerHTML = `
                    <img src="${imageData}" alt="预览图片" class="w-12 h-12 object-cover rounded">
                    <span class="ml-2 text-sm text-blue-700 flex-1">图片已选择，输入问题后发送</span>
                    <button class="ml-2 text-red-500 hover:text-red-700" onclick="clearImagePreview()" title="移除图片">
                        <i class="fa fa-times"></i>
                    </button>
                `;

                // 插入到输入区域上方
                const inputArea = document.querySelector('.p-4.border-t');
                inputArea.insertBefore(previewDiv, inputArea.firstChild);
            }

            // 清除图片预览
            window.clearImagePreview = function() {
                const preview = document.querySelector('.image-preview');
                if (preview) {
                    preview.remove();
                }
                currentImageData = null;
                userInput.placeholder = '输入问题或上传图片...';
            }
            
            // 设置面板相关功能
            function openSettings() {
                settingsModal.classList.add('active');
                modalBackdrop.classList.add('active');
                document.body.style.overflow = 'hidden';
            }
            
            function closeSettingsModal() {
                settingsModal.classList.remove('active');
                modalBackdrop.classList.remove('active');
                document.body.style.overflow = '';
            }
            
            settingsButton.addEventListener('click', async function() {
                openSettings();
                // 打开设置时检查连接状态和刷新模型列表
                await checkConnectionStatus();
                await refreshModelList();
            });
            closeSettings.addEventListener('click', closeSettingsModal);
            cancelSettings.addEventListener('click', closeSettingsModal);
            modalBackdrop.addEventListener('click', closeSettingsModal);

            // 刷新模型列表按钮
            refreshModels.addEventListener('click', refreshModelList);

            // API类型变化时的处理
            document.getElementById('api-type').addEventListener('change', async function() {
                const apiType = this.value;
                const apiUrlInput = document.getElementById('api-url');

                // 根据API类型设置默认URL
                if (apiType === 'ollama') {
                    apiUrlInput.value = 'http://localhost:11434';
                    apiConfig.config.baseURL = 'http://localhost:11434';
                } else if (apiType === 'lm-studio') {
                    apiUrlInput.value = 'http://localhost:1234/v1';
                    apiConfig.config.baseURL = 'http://localhost:1234/v1';
                } else if (apiType === 'openai-compatible') {
                    apiUrlInput.value = 'http://localhost:1234/v1';
                    apiConfig.config.baseURL = 'http://localhost:1234/v1';
                } else if (apiType === 'custom') {
                    apiUrlInput.value = '';
                }

                // 更新配置并检查连接
                apiConfig.config.apiType = apiType;
                await checkConnectionStatus();
                await refreshModelList();
            });

            // 清空对话
            clearChat.addEventListener('click', function() {
                if (confirm('确定要清空所有对话记录吗？')) {
                    // 完全清空聊天容器
                    chatContainer.innerHTML = '';

                    // 清空对话历史
                    conversationHistory = [];

                    // 重置性能监控
                    performanceMonitor.reset();

                    // 清空图片预览
                    if (typeof clearImagePreview === 'function') {
                        clearImagePreview();
                    }

                    // 重置当前图片数据
                    if (typeof currentImageData !== 'undefined') {
                        currentImageData = null;
                    }

                    // 重置输入框占位符
                    if (userInput) {
                        userInput.placeholder = '输入问题或上传图片...';
                        userInput.value = '';
                    }

                    // 重新添加系统消息
                    const systemMessageDiv = document.createElement('div');
                    systemMessageDiv.className = 'flex items-start';
                    systemMessageDiv.innerHTML = `
                        <div class="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center mr-3 flex-shrink-0">
                            <i class="fa fa-info text-primary"></i>
                        </div>
                        <div class="bg-layer3 rounded-lg p-3 max-w-[85%] card-hover">
                            <div class="font-semibold text-primary">系统提示</div>
                            <p class="text-sm text-gray-700">欢迎使用本地大模型助手！我支持文本对话和图像理解。您可以直接输入问题，或使用右侧按钮上传图片进行多模态交互。</p>
                        </div>
                    `;
                    chatContainer.appendChild(systemMessageDiv);

                    // 重新添加欢迎消息
                    appendMessage('bot', '对话已清空，我们可以重新开始！有什么我可以帮助您的吗？');
                }
            });

            // 模型选择变化监听
            modelSelect.addEventListener('change', function() {
                const selectedModel = this.value;
                updateModelCapabilities(selectedModel);

                // 更新配置
                apiConfig.config.model = selectedModel;
                apiConfig.save();

                // 显示模型切换提示
                const modelIndicator = document.createElement('div');
                modelIndicator.className = 'fixed top-1/4 left-1/2 transform -translate-x-1/2 bg-primary/90 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-fadeIn';
                modelIndicator.textContent = `已切换到模型: ${selectedModel}`;
                document.body.appendChild(modelIndicator);

                setTimeout(() => {
                    modelIndicator.style.opacity = '0';
                    modelIndicator.style.transition = 'opacity 0.5s ease-out';
                    setTimeout(() => {
                        if (modelIndicator.parentNode) {
                            modelIndicator.parentNode.removeChild(modelIndicator);
                        }
                    }, 500);
                }, 2000);
            });

            // 模式选择
            modeButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    modeButtons.forEach(b => {
                        b.classList.remove('bg-primary', 'text-white');
                        b.classList.add('bg-gray-200', 'text-gray-700');
                    });
                    this.classList.remove('bg-gray-200', 'text-gray-700');
                    this.classList.add('bg-primary', 'text-white');
                    currentMode = this.dataset.mode;
                    currentModeElement.textContent = currentMode;
                    
                    // 添加模式切换的视觉反馈
                    const modeIndicator = document.createElement('div');
                    modeIndicator.className = 'fixed top-1/4 left-1/2 transform -translate-x-1/2 bg-primary/90 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-fadeIn';
                    modeIndicator.textContent = `已切换到${currentMode}模式`;
                    document.body.appendChild(modeIndicator);
                    
                    setTimeout(() => {
                        modeIndicator.style.opacity = '0';
                        modeIndicator.style.transition = 'opacity 0.5s ease-out';
                        setTimeout(() => {
                            modeIndicator.remove();
                        }, 500);
                    }, 1500);
                });
            });
            
            // 功能卡片点击切换模式
            window.selectMode = function(mode) {
                // 检查当前模型是否支持所选功能（使用智能匹配）
                const currentModel = apiConfig.config.model;
                const capabilities = getModelCapabilities(currentModel);

                // 检查模型是否适合对话
                if (!modelChecker.isConversationalModel(currentModel)) {
                    alert('当前选择的模型不适合对话任务，请先在设置中选择合适的对话模型。');
                    return;
                }

                let supported = true;
                let featureName = '';

                switch(mode) {
                    case '文本':
                        supported = capabilities.text.supported;
                        featureName = '文本推理';
                        break;
                    case '图像':
                        supported = capabilities.image.supported;
                        featureName = '图像理解';
                        break;
                    case '语音':
                        supported = capabilities.audio.supported;
                        featureName = '语音交互';
                        break;
                }

                if (!supported) {
                    alert(`当前模型 "${currentModel}" 不支持${featureName}功能。\n请在设置中切换到支持该功能的模型。`);
                    // 打开设置面板让用户切换模型
                    openSettings();
                    return;
                }

                // 更新性能监控的当前模式
                let monitorMode = 'text';
                if (mode === '图像' && capabilities.image.supported) {
                    monitorMode = 'image';
                } else if (mode === '语音' && capabilities.audio.supported) {
                    monitorMode = 'multimodal';
                } else if (capabilities.image.supported || capabilities.audio.supported) {
                    monitorMode = 'multimodal';
                }
                performanceMonitor.setCurrentMode(monitorMode);

                // 找到对应的模式按钮并触发点击事件
                const modeBtn = Array.from(modeButtons).find(btn => btn.dataset.mode === mode);
                if (modeBtn) {
                    modeBtn.click();
                    // 打开设置面板
                    openSettings();
                }
            };
            
            // 测试连接
            testConnection.addEventListener('click', async function() {
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fa fa-spinner fa-spin mr-1"></i> 测试中...';
                this.disabled = true;

                try {
                    // 临时更新配置进行测试
                    const tempConfig = new APIConfig();
                    tempConfig.config.apiType = document.getElementById('api-type').value;
                    tempConfig.config.baseURL = document.getElementById('api-url').value;
                    tempConfig.config.apiKey = document.getElementById('api-key').value;
                    tempConfig.config.model = document.getElementById('model-name').value;

                    // 首先检查基础连接
                    if (tempConfig.config.apiType === 'ollama') {
                        const connectionResult = await tempConfig.checkOllamaConnection();
                        if (!connectionResult.connected) {
                            throw new Error(`Ollama服务未运行: ${connectionResult.error}`);
                        }
                    }

                    // 然后测试模型调用
                    const testClient = new LLMAPIClient(tempConfig);
                    const testMessages = [{ role: 'user', content: 'Hi' }];

                    await testClient.sendMessage(testMessages);

                    this.innerHTML = '<i class="fa fa-check mr-1"></i> 连接成功';
                    this.className = this.className.replace('border-primary text-primary', 'border-green-500 text-green-500');

                    // 更新连接状态
                    updateConnectionStatus(true, '', tempConfig.config.apiType === 'ollama' ? 'Ollama' : 'API');

                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.className = this.className.replace('border-green-500 text-green-500', 'border-primary text-primary');
                        this.disabled = false;
                    }, 2000);

                } catch (error) {
                    console.error('连接测试失败:', error);
                    this.innerHTML = '<i class="fa fa-times mr-1"></i> 连接失败';
                    this.className = this.className.replace('border-primary text-primary', 'border-red-500 text-red-500');

                    // 更新连接状态
                    updateConnectionStatus(false, error.message);

                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.className = this.className.replace('border-red-500 text-red-500', 'border-primary text-primary');
                        this.disabled = false;
                    }, 3000);

                    // 提供详细的故障排除指导
                    let troubleshootingMessage = `连接测试失败: ${error.message}\n\n故障排除建议:\n`;

                    if (tempConfig.config.apiType === 'ollama') {
                        troubleshootingMessage += `
1. 确保Ollama服务正在运行:
   - 在终端运行: ollama serve
   - 或检查是否已在后台运行

2. 检查模型是否已下载:
   - 运行: ollama list
   - 如果没有模型，下载一个: ollama pull llama3.2

3. 确认端口11434未被占用:
   - 访问: http://localhost:11434
   - 应该看到"Ollama is running"

4. 检查防火墙设置是否阻止了连接`;
                    } else {
                        troubleshootingMessage += `
1. 确认API服务正在运行
2. 检查API地址是否正确
3. 验证API密钥（如果需要）
4. 确认模型名称正确`;
                    }

                    alert(troubleshootingMessage);
                }
            });

            // 保存设置
            saveSettings.addEventListener('click', function() {
                // 添加保存动画
                this.innerHTML = '<i class="fa fa-spinner fa-spin mr-1"></i> 保存中...';
                this.disabled = true;

                try {
                    // 更新配置
                    apiConfig.updateFromUI();

                    // 保存到本地存储
                    apiConfig.saveConfig();

                    setTimeout(() => {
                        const apiTypeNames = {
                            'ollama': 'Ollama',
                            'lm-studio': 'LM Studio',
                            'openai-compatible': 'OpenAI兼容',
                            'custom': '自定义API'
                        };

                        appendMessage('bot', `设置已保存！当前配置：
- API类型: ${apiTypeNames[apiConfig.config.apiType] || apiConfig.config.apiType}
- API地址: ${apiConfig.config.baseURL}
- 模型: ${apiConfig.config.model}
- 温度: ${apiConfig.config.temperature}
- 最大令牌: ${apiConfig.config.maxTokens}
- 流式输出: ${apiConfig.config.streamOutput ? '启用' : '禁用'}
- GPU加速: ${apiConfig.config.useGPU ? '启用' : '禁用'}`);
                        closeSettingsModal();

                        // 重置按钮状态
                        this.innerHTML = '保存设置';
                        this.disabled = false;
                    }, 800);
                } catch (error) {
                    console.error('保存设置失败:', error);
                    alert('保存设置失败，请检查输入');
                    this.innerHTML = '保存设置';
                    this.disabled = false;
                }
            });
            
            // 滑块值更新
            temperatureSlider.addEventListener('input', function() {
                tempValue.textContent = this.value;
            });
            
            maxLengthSlider.addEventListener('input', function() {
                lengthValue.textContent = this.value;
            });
            
            // 输入框获得焦点时的动画
            userInput.addEventListener('focus', function() {
                this.parentElement.classList.add('scale-[1.01]');
                this.parentElement.style.transition = 'transform 0.2s ease';
            });
            
            userInput.addEventListener('blur', function() {
                this.parentElement.classList.remove('scale-[1.01]');
            });
            
            // 页面加载完成后的欢迎动画和初始化检查
            setTimeout(async () => {
                const welcomeMsg = document.querySelector('.chat-message-bot:first-of-type');
                if (welcomeMsg) {
                    welcomeMsg.classList.add('animate-pulse');
                    setTimeout(() => {
                        welcomeMsg.classList.remove('animate-pulse');
                    }, 2000);
                }

                // 自动检查连接状态
                await checkConnectionStatus();

                // 初始化模型能力显示
                updateModelCapabilities(apiConfig.config.model);

                // 如果是Ollama且连接成功，自动刷新模型列表
                if (apiConfig.config.apiType === 'ollama') {
                    const connectionResult = await apiConfig.checkOllamaConnection();
                    if (connectionResult.connected) {
                        try {
                            const models = await apiConfig.getOllamaModels();
                            if (models.length === 0) {
                                // 如果没有模型，显示提示
                                appendMessage('bot', `
<div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
    <div class="flex items-center">
        <i class="fa fa-exclamation-triangle text-yellow-600 mr-2"></i>
        <div>
            <div class="font-medium text-yellow-800">未找到已安装的模型</div>
            <div class="text-yellow-700 text-sm mt-1">
                请先下载一个模型，例如：<br>
                <code class="bg-yellow-100 px-2 py-1 rounded mt-1 inline-block">ollama pull llama3.2</code>
            </div>
        </div>
    </div>
</div>`);
                            } else {
                                // 显示可用模型信息
                                appendMessage('bot', `
<div class="bg-green-50 border border-green-200 rounded-lg p-4">
    <div class="flex items-center">
        <i class="fa fa-check-circle text-green-600 mr-2"></i>
        <div>
            <div class="font-medium text-green-800">Ollama连接成功</div>
            <div class="text-green-700 text-sm mt-1">
                找到 ${models.length} 个可用模型，当前使用：${apiConfig.config.model}
            </div>
        </div>
    </div>
</div>`);
                            }
                        } catch (error) {
                            console.error('获取模型列表失败:', error);
                        }
                    } else {
                        // 连接失败，显示故障排除指导
                        appendMessage('bot', `
<div class="bg-red-50 border border-red-200 rounded-lg p-4">
    <div class="flex items-center">
        <i class="fa fa-exclamation-circle text-red-600 mr-2"></i>
        <div>
            <div class="font-medium text-red-800">无法连接到Ollama服务</div>
            <div class="text-red-700 text-sm mt-1">
                请确保Ollama正在运行：<br>
                <code class="bg-red-100 px-2 py-1 rounded mt-1 inline-block">ollama serve</code>
            </div>
        </div>
    </div>
</div>`);
                    }
                }
            }, 1000);

            // 详细统计弹窗事件处理
            const statsDetailBtn = document.getElementById('stats-detail-btn');
            const statsDetailModal = document.getElementById('stats-detail-modal');
            const closeStatsModal = document.getElementById('close-stats-modal');

            if (statsDetailBtn && statsDetailModal && closeStatsModal) {
                statsDetailBtn.addEventListener('click', function() {
                    // 更新详细统计数据
                    updateDetailedStats();
                    statsDetailModal.classList.remove('hidden');
                });

                closeStatsModal.addEventListener('click', function() {
                    statsDetailModal.classList.add('hidden');
                });

                // 点击背景关闭弹窗
                statsDetailModal.addEventListener('click', function(e) {
                    if (e.target === statsDetailModal) {
                        statsDetailModal.classList.add('hidden');
                    }
                });
            }

            // 更新详细统计数据
            function updateDetailedStats() {
                // 字符比例
                const charRatioDisplay = document.getElementById('char-ratio-display');
                if (charRatioDisplay) {
                    if (performanceMonitor.userCharCount > 0) {
                        const ratio = (performanceMonitor.aiCharCount / performanceMonitor.userCharCount).toFixed(1);
                        charRatioDisplay.textContent = `${ratio}:1`;
                    } else {
                        charRatioDisplay.textContent = '--';
                    }
                }

                // 会话效率（字符/分钟）
                const efficiencyDisplay = document.getElementById('session-efficiency-display');
                if (efficiencyDisplay) {
                    const startTime = performanceMonitor.firstMessageTime || performanceMonitor.sessionStartTime;
                    const durationMinutes = (Date.now() - startTime) / (1000 * 60);
                    if (durationMinutes > 0) {
                        const totalChars = performanceMonitor.userCharCount + performanceMonitor.aiCharCount;
                        const efficiency = Math.round(totalChars / durationMinutes);
                        efficiencyDisplay.textContent = efficiency.toLocaleString();
                    } else {
                        efficiencyDisplay.textContent = '--';
                    }
                }
            }

            // 初始化性能监控显示
            performanceMonitor.updateAllDisplays();

            // 定期更新详细统计（如果弹窗打开）
            setInterval(() => {
                if (statsDetailModal && !statsDetailModal.classList.contains('hidden')) {
                    updateDetailedStats();
                }
            }, 1000);
        });
    </script>
</body>
</html>    