<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地大模型对话界面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#10B981',
                        tertiary: '#F59E0B',
                        layer1: '#E0F2FE',
                        layer2: '#DBEAFE',
                        layer3: '#E0E7FF',
                        layer4: '#EEF2FF',
                        component: 'rgba(255, 255, 255, 0.7)',
                        chatbot: '#4F46E5',
                        user: '#10B981',
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .module-border {
                @apply border border-gray-200 rounded-lg;
            }
            .layer-padding {
                @apply p-4;
            }
            .component-padding {
                @apply p-3;
            }
            .text-shadow-sm {
                text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            }
            .chat-message {
                @apply rounded-lg p-4 max-w-[85%] my-2 animate-fadeIn;
            }
            .chat-message-bot {
                @apply bg-chatbot text-white rounded-tl-none;
            }
            .chat-message-user {
                @apply bg-user text-white rounded-tr-none ml-auto;
            }
            .animate-fadeIn {
                animation: fadeIn 0.3s ease-in-out;
            }
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }
            .typing-indicator {
                @apply inline-block space-x-1;
            }
            .typing-indicator span {
                @apply inline-block w-2 h-2 bg-white rounded-full animate-bounce;
            }
            .typing-indicator span:nth-child(2) {
                animation-delay: 0.2s;
            }
            .typing-indicator span:nth-child(3) {
                animation-delay: 0.4s;
            }
            .modal-backdrop {
                @apply fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300;
            }
            .modal {
                @apply fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full max-w-lg bg-white rounded-xl shadow-xl z-50 transition-all duration-300 scale-95 opacity-0;
            }
            .modal.active {
                @apply scale-100 opacity-100;
            }
            .modal-backdrop.active {
                @apply opacity-100;
            }
            .btn-hover {
                @apply transition-all duration-200 hover:shadow-md hover:-translate-y-0.5;
            }
            .card-hover {
                @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
            }
            .gradient-text {
                @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-tertiary;
            }
        }
    </style>
</head>
<body class="bg-gray-50 font-sans text-gray-800 min-h-screen flex flex-col">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm sticky top-0 z-10 transition-all duration-300">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center">
                <i class="fa fa-brain text-primary text-2xl mr-2"></i>
                <h1 class="text-xl font-bold text-primary">本地大模型助手</h1>
            </div>
            <div class="flex items-center space-x-4">
                <button class="text-gray-500 hover:text-primary transition-colors btn-hover" title="历史记录">
                    <i class="fa fa-history"></i>
                </button>
                <button id="settings-button" class="text-gray-500 hover:text-primary transition-colors btn-hover" title="设置">
                    <i class="fa fa-cog"></i>
                </button>
                <button class="text-gray-500 hover:text-primary transition-colors btn-hover" title="帮助">
                    <i class="fa fa-question-circle"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- 主要内容区 -->
    <main class="flex-1 container mx-auto p-4 md:p-6 flex flex-col">
        <!-- 模型状态卡片 -->
        <div class="bg-white rounded-xl p-4 shadow-sm mb-6 flex flex-col md:flex-row items-center justify-between card-hover">
            <div class="flex items-center mb-4 md:mb-0">
                <div class="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center mr-3">
                    <i class="fa fa-server text-primary"></i>
                </div>
                <div>
                    <h2 class="font-bold text-gray-800">模型状态</h2>
                    <div class="flex items-center mt-1">
                        <span class="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                        <span class="text-sm text-gray-600">推理模型 & 多模态模型 已加载</span>
                    </div>
                </div>
            </div>
            <div class="grid grid-cols-2 sm:grid-cols-4 gap-3 w-full md:w-auto">
                <div class="bg-layer4 rounded-lg p-3 text-center card-hover">
                    <div class="text-xs text-gray-500 mb-1">响应时间</div>
                    <div class="font-semibold">1.2s</div>
                </div>
                <div class="bg-layer4 rounded-lg p-3 text-center card-hover">
                    <div class="text-xs text-gray-500 mb-1">内存占用</div>
                    <div class="font-semibold">4.7GB</div>
                </div>
                <div class="bg-layer4 rounded-lg p-3 text-center card-hover">
                    <div class="text-xs text-gray-500 mb-1">对话轮数</div>
                    <div class="font-semibold">12</div>
                </div>
                <div class="bg-layer4 rounded-lg p-3 text-center card-hover">
                    <div class="text-xs text-gray-500 mb-1">使用模式</div>
                    <div class="font-semibold" id="current-mode">多模态</div>
                </div>
            </div>
        </div>

        <!-- 对话区域 -->
        <div class="flex-1 bg-white rounded-xl shadow-sm overflow-hidden flex flex-col mb-6 transition-all duration-300 hover:shadow-md">
            <div class="bg-primary text-white p-4 border-b border-gray-100 flex items-center">
                <i class="fa fa-comments-o mr-2"></i>
                <h2 class="font-bold">智能对话</h2>
            </div>
            
            <div id="chat-container" class="flex-1 overflow-y-auto p-4 space-y-4">
                <!-- 系统消息 -->
                <div class="flex items-start">
                    <div class="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center mr-3 flex-shrink-0">
                        <i class="fa fa-info text-primary"></i>
                    </div>
                    <div class="bg-layer3 rounded-lg p-3 max-w-[85%] card-hover">
                        <div class="font-semibold text-primary">系统提示</div>
                        <p class="text-sm text-gray-700">欢迎使用本地大模型助手！我支持文本对话和图像理解。您可以直接输入问题，或使用右侧按钮上传图片进行多模态交互。</p>
                    </div>
                </div>
                
                <!-- 聊天机器人消息 -->
                <div class="flex items-start">
                    <div class="w-8 h-8 rounded-full bg-chatbot flex items-center justify-center mr-3 flex-shrink-0">
                        <i class="fa fa-robot text-white"></i>
                    </div>
                    <div class="chat-message chat-message-bot card-hover">
                        <p>您好！我是您的本地大模型助手。请问有什么可以帮助您的吗？</p>
                    </div>
                </div>
                
                <!-- 用户消息 -->
                <div class="flex items-start justify-end">
                    <div class="chat-message chat-message-user card-hover">
                        <p>你能识别图片中的内容吗？</p>
                    </div>
                    <div class="w-8 h-8 rounded-full bg-user flex items-center justify-center ml-3 flex-shrink-0">
                        <i class="fa fa-user text-white"></i>
                    </div>
                </div>
                
                <!-- 聊天机器人消息 -->
                <div class="flex items-start">
                    <div class="w-8 h-8 rounded-full bg-chatbot flex items-center justify-center mr-3 flex-shrink-0">
                        <i class="fa fa-robot text-white"></i>
                    </div>
                    <div class="chat-message chat-message-bot card-hover">
                        <p>当然可以！我集成了多模态理解能力，请上传一张图片，我会为您识别并描述其中的内容。</p>
                    </div>
                </div>
            </div>
            
            <!-- 输入区域 -->
            <div class="p-4 border-t border-gray-100 bg-gray-50">
                <div class="relative">
                    <textarea id="user-input" class="w-full border border-gray-200 rounded-lg p-3 pr-12 focus:outline-none focus:ring-2 focus:ring-primary/50 resize-none transition-all duration-200 hover:border-primary/30" rows="2" placeholder="输入问题或上传图片..."></textarea>
                    <div class="absolute right-3 bottom-3 flex items-center space-x-2">
                        <label for="image-upload" class="text-gray-500 hover:text-primary cursor-pointer transition-colors btn-hover" title="上传图片">
                            <i class="fa fa-image text-lg"></i>
                        </label>
                        <input id="image-upload" type="file" accept="image/*" class="hidden">
                        <button id="send-button" class="bg-primary hover:bg-primary/90 text-white rounded-full w-9 h-9 flex items-center justify-center transition-all duration-200 hover:shadow-md hover:scale-105 active:scale-95" title="发送">
                            <i class="fa fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
                <div class="flex items-center justify-between mt-3 text-xs text-gray-500">
                    <div>支持文本、图片多模态交互</div>
                    <div>按 Enter 发送，Shift+Enter 换行</div>
                </div>
            </div>
        </div>
        
        <!-- 功能卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="bg-white rounded-xl p-4 shadow-sm border-l-4 border-primary hover:shadow-md transition-shadow card-hover cursor-pointer" onclick="selectMode('文本')">
                <div class="flex items-center mb-3">
                    <i class="fa fa-file-text-o text-primary text-xl mr-3"></i>
                    <h3 class="font-bold text-gray-800">文本推理</h3>
                </div>
                <p class="text-sm text-gray-600">基于7B参数的大型语言模型，提供专业的文本理解和生成能力。</p>
            </div>
            
            <div class="bg-white rounded-xl p-4 shadow-sm border-l-4 border-tertiary hover:shadow-md transition-shadow card-hover cursor-pointer" onclick="selectMode('图像')">
                <div class="flex items-center mb-3">
                    <i class="fa fa-image text-tertiary text-xl mr-3"></i>
                    <h3 class="font-bold text-gray-800">图像理解</h3>
                </div>
                <p class="text-sm text-gray-600">基于CLIP架构的视觉模型，支持图片识别、场景分析和内容描述。</p>
            </div>
            
            <div class="bg-white rounded-xl p-4 shadow-sm border-l-4 border-secondary hover:shadow-md transition-shadow card-hover cursor-pointer" onclick="selectMode('语音')">
                <div class="flex items-center mb-3">
                    <i class="fa fa-microphone text-secondary text-xl mr-3"></i>
                    <h3 class="font-bold text-gray-800">语音交互</h3>
                </div>
                <p class="text-sm text-gray-600">集成Wav2Vec2模型，支持语音输入和音频内容理解，提供更自然的交互体验。</p>
            </div>
        </div>
    </main>

    <!-- 设置模态框 -->
    <div id="settings-modal" class="modal">
        <div class="p-5">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800">模型对接设置</h3>
                <button id="close-settings" class="text-gray-400 hover:text-gray-600 btn-hover" title="关闭">
                    <i class="fa fa-times"></i>
                </button>
            </div>
            
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">默认模式</label>
                    <div class="grid grid-cols-3 gap-2">
                        <button class="mode-btn bg-primary text-white py-2 px-3 rounded-lg text-sm btn-hover" data-mode="多模态">多模态</button>
                        <button class="mode-btn bg-gray-200 text-gray-700 py-2 px-3 rounded-lg text-sm btn-hover" data-mode="文本">文本</button>
                        <button class="mode-btn bg-gray-200 text-gray-700 py-2 px-3 rounded-lg text-sm btn-hover" data-mode="图像">图像</button>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">推理模型路径</label>
                    <input type="text" id="model-path" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-200 hover:border-primary/30" value="/path/to/llm-model">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">多模态模型路径</label>
                    <input type="text" id="mm-model-path" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-200 hover:border-primary/30" value="/path/to/mm-model">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">推理参数</label>
                    <div class="grid grid-cols-2 gap-3">
                        <div>
                            <label class="text-xs text-gray-500">温度 (Temperature)</label>
                            <input type="range" id="temperature" min="0" max="2" step="0.1" value="0.7" class="w-full accent-primary">
                            <div class="flex justify-between text-xs text-gray-500">
                                <span>0</span>
                                <span id="temp-value">0.7</span>
                                <span>2</span>
                            </div>
                        </div>
                        <div>
                            <label class="text-xs text-gray-500">最大生成长度</label>
                            <input type="range" id="max-length" min="100" max="2000" step="100" value="1000" class="w-full accent-primary">
                            <div class="flex justify-between text-xs text-gray-500">
                                <span>100</span>
                                <span id="length-value">1000</span>
                                <span>2000</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div>
                    <label class="flex items-center cursor-pointer">
                        <input type="checkbox" id="use-gpu" class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary cursor-pointer">
                        <span class="ml-2 text-sm text-gray-700">使用GPU加速</span>
                    </label>
                </div>
                
                <div>
                    <label class="flex items-center cursor-pointer">
                        <input type="checkbox" id="stream-output" class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary cursor-pointer">
                        <span class="ml-2 text-sm text-gray-700">流式输出</span>
                    </label>
                </div>
            </div>
            
            <div class="mt-6 flex justify-end space-x-3">
                <button id="cancel-settings" class="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-all duration-200 btn-hover">取消</button>
                <button id="save-settings" class="px-4 py-2 bg-primary border border-transparent rounded-lg text-sm font-medium text-white hover:bg-primary/90 transition-all duration-200 btn-hover">保存设置</button>
            </div>
        </div>
    </div>
    
    <!-- 模态框背景 -->
    <div id="modal-backdrop" class="modal-backdrop"></div>

    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-200 py-4 text-center text-sm text-gray-500">
        <p>本地大模型助手 © 2025 | 基于双引擎架构</p>
    </footer>

    <script>
        // 聊天功能实现
        document.addEventListener('DOMContentLoaded', function() {
            const chatContainer = document.getElementById('chat-container');
            const userInput = document.getElementById('user-input');
            const sendButton = document.getElementById('send-button');
            const imageUpload = document.getElementById('image-upload');
            const settingsButton = document.getElementById('settings-button');
            const closeSettings = document.getElementById('close-settings');
            const cancelSettings = document.getElementById('cancel-settings');
            const saveSettings = document.getElementById('save-settings');
            const settingsModal = document.getElementById('settings-modal');
            const modalBackdrop = document.getElementById('modal-backdrop');
            const modeButtons = document.querySelectorAll('.mode-btn');
            const currentModeElement = document.getElementById('current-mode');
            const temperatureSlider = document.getElementById('temperature');
            const tempValue = document.getElementById('temp-value');
            const maxLengthSlider = document.getElementById('max-length');
            const lengthValue = document.getElementById('length-value');
            const header = document.querySelector('header');
            
            let currentMode = '多模态';
            
            // 监听滚动事件，改变导航栏样式
            window.addEventListener('scroll', function() {
                if (window.scrollY > 10) {
                    header.classList.add('shadow-md', 'py-2');
                    header.classList.remove('py-3');
                } else {
                    header.classList.remove('shadow-md', 'py-2');
                    header.classList.add('py-3');
                }
            });
            
            // 发送消息
            function sendMessage() {
                const message = userInput.value.trim();
                if (!message) return;
                
                // 添加用户消息到聊天框
                appendMessage('user', message);
                
                // 清空输入框
                userInput.value = '';
                
                // 显示机器人正在输入的状态
                showTypingIndicator();
                
                // 模拟机器人回复
                setTimeout(() => {
                    // 移除打字指示器
                    removeTypingIndicator();
                    
                    // 根据消息内容生成不同回复
                    let botResponse = '';
                    if (message.toLowerCase().includes('图片') || message.toLowerCase().includes('图像')) {
                        botResponse = '我正在分析您发送的图片内容。这看起来是一张风景照片，照片中有一片宁静的湖泊，湖边有几棵树木，远处是连绵的山脉，天空中漂浮着几朵白云。';
                    } else if (message.toLowerCase().includes('模型') || message.toLowerCase().includes('架构')) {
                        botResponse = '我基于双引擎架构运行，包括一个7B参数的文本推理模型和一个基于CLIP的多模态理解模型。这两个模型协同工作，使我能够处理文本和图像等多种输入方式。';
                    } else if (message.toLowerCase().includes('设置') || message.toLowerCase().includes('参数')) {
                        botResponse = `当前设置：
- 工作模式：${currentMode}
- 温度参数：${temperatureSlider.value}
- 最大生成长度：${maxLengthSlider.value}
- GPU加速：${document.getElementById('use-gpu').checked ? '启用' : '禁用'}
- 流式输出：${document.getElementById('stream-output').checked ? '启用' : '禁用'}`;
                    } else {
                        botResponse = '感谢您的提问！我是一个基于本地部署的大型语言模型，能够回答各种问题并处理多模态内容。请问您还有什么需要帮助的吗？';
                    }
                    
                    // 添加机器人回复
                    appendMessage('bot', botResponse);
                }, 1500);
            }
            
            // 添加消息到聊天框
            function appendMessage(sender, message) {
                const messageDiv = document.createElement('div');
                messageDiv.className = sender === 'user' 
                    ? 'flex items-start justify-end' 
                    : 'flex items-start';
                
                if (sender === 'user') {
                    messageDiv.innerHTML = `
                        <div class="chat-message chat-message-user card-hover">
                            <p>${message}</p>
                        </div>
                        <div class="w-8 h-8 rounded-full bg-user flex items-center justify-center ml-3 flex-shrink-0">
                            <i class="fa fa-user text-white"></i>
                        </div>
                    `;
                } else {
                    messageDiv.innerHTML = `
                        <div class="w-8 h-8 rounded-full bg-chatbot flex items-center justify-center mr-3 flex-shrink-0">
                            <i class="fa fa-robot text-white"></i>
                        </div>
                        <div class="chat-message chat-message-bot card-hover">
                            <p>${message}</p>
                        </div>
                    `;
                }
                
                chatContainer.appendChild(messageDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;
                
                // 添加消息发送音效
                playNotificationSound();
            }
            
            // 播放通知音效
            function playNotificationSound() {
                // 在实际应用中，可以添加真实的音效
                // 这里仅作为交互反馈的示例
                console.log("Message notification sound played");
            }
            
            // 显示打字指示器
            function showTypingIndicator() {
                const typingDiv = document.createElement('div');
                typingDiv.className = 'flex items-start';
                typingDiv.id = 'typing-indicator';
                typingDiv.innerHTML = `
                    <div class="w-8 h-8 rounded-full bg-chatbot flex items-center justify-center mr-3 flex-shrink-0">
                        <i class="fa fa-robot text-white"></i>
                    </div>
                    <div class="chat-message chat-message-bot">
                        <div class="typing-indicator">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                `;
                
                chatContainer.appendChild(typingDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }
            
            // 移除打字指示器
            function removeTypingIndicator() {
                const typingIndicator = document.getElementById('typing-indicator');
                if (typingIndicator) {
                    typingIndicator.remove();
                }
            }
            
            // 发送按钮点击事件
            sendButton.addEventListener('click', sendMessage);
            
            // 输入框回车事件
            userInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
            
            // 图片上传事件
            imageUpload.addEventListener('change', function(e) {
                if (e.target.files && e.target.files[0]) {
                    const reader = new FileReader();
                    
                    reader.onload = function(e) {
                        // 添加用户上传的图片到聊天框
                        appendMessage('user', `
                            <div class="mb-2">
                                <img src="${e.target.result}" alt="上传的图片" class="rounded-lg max-w-full max-h-64 object-contain">
                            </div>
                            <p>请分析这张图片</p>
                        `);
                        
                        // 显示机器人正在输入的状态
                        showTypingIndicator();
                        
                        // 模拟机器人回复
                        setTimeout(() => {
                            // 移除打字指示器
                            removeTypingIndicator();
                            
                            // 添加机器人回复
                            appendMessage('bot', `
                                <p>我分析了您上传的图片，这是一张城市风景照片。</p>
                                <ul class="list-disc pl-5 mt-2 space-y-1">
                                    <li>图片中心是一座现代化的摩天大楼</li>
                                    <li>大楼前有一个小公园，绿树成荫</li>
                                    <li>街道上有行人和车辆，显示出城市的活力</li>
                                    <li>天空晴朗，阳光明媚，给照片增添了明亮的氛围</li>
                                </ul>
                            `);
                        }, 2000);
                    }
                    
                    reader.readAsDataURL(e.target.files[0]);
                }
            });
            
            // 设置面板相关功能
            function openSettings() {
                settingsModal.classList.add('active');
                modalBackdrop.classList.add('active');
                document.body.style.overflow = 'hidden';
            }
            
            function closeSettingsModal() {
                settingsModal.classList.remove('active');
                modalBackdrop.classList.remove('active');
                document.body.style.overflow = '';
            }
            
            settingsButton.addEventListener('click', openSettings);
            closeSettings.addEventListener('click', closeSettingsModal);
            cancelSettings.addEventListener('click', closeSettingsModal);
            modalBackdrop.addEventListener('click', closeSettingsModal);
            
            // 模式选择
            modeButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    modeButtons.forEach(b => {
                        b.classList.remove('bg-primary', 'text-white');
                        b.classList.add('bg-gray-200', 'text-gray-700');
                    });
                    this.classList.remove('bg-gray-200', 'text-gray-700');
                    this.classList.add('bg-primary', 'text-white');
                    currentMode = this.dataset.mode;
                    currentModeElement.textContent = currentMode;
                    
                    // 添加模式切换的视觉反馈
                    const modeIndicator = document.createElement('div');
                    modeIndicator.className = 'fixed top-1/4 left-1/2 transform -translate-x-1/2 bg-primary/90 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-fadeIn';
                    modeIndicator.textContent = `已切换到${currentMode}模式`;
                    document.body.appendChild(modeIndicator);
                    
                    setTimeout(() => {
                        modeIndicator.style.opacity = '0';
                        modeIndicator.style.transition = 'opacity 0.5s ease-out';
                        setTimeout(() => {
                            modeIndicator.remove();
                        }, 500);
                    }, 1500);
                });
            });
            
            // 功能卡片点击切换模式
            window.selectMode = function(mode) {
                // 找到对应的模式按钮并触发点击事件
                const modeBtn = Array.from(modeButtons).find(btn => btn.dataset.mode === mode);
                if (modeBtn) {
                    modeBtn.click();
                    // 打开设置面板
                    openSettings();
                }
            };
            
            // 保存设置
            saveSettings.addEventListener('click', function() {
                // 添加保存动画
                this.innerHTML = '<i class="fa fa-spinner fa-spin mr-1"></i> 保存中...';
                this.disabled = true;
                
                setTimeout(() => {
                    // 在实际应用中，这里会保存设置到本地存储或发送到后端
                    appendMessage('bot', '设置已保存！新设置将在下一次对话中生效。');
                    closeSettingsModal();
                    
                    // 重置按钮状态
                    this.innerHTML = '保存设置';
                    this.disabled = false;
                }, 800);
            });
            
            // 滑块值更新
            temperatureSlider.addEventListener('input', function() {
                tempValue.textContent = this.value;
            });
            
            maxLengthSlider.addEventListener('input', function() {
                lengthValue.textContent = this.value;
            });
            
            // 输入框获得焦点时的动画
            userInput.addEventListener('focus', function() {
                this.parentElement.classList.add('scale-[1.01]');
                this.parentElement.style.transition = 'transform 0.2s ease';
            });
            
            userInput.addEventListener('blur', function() {
                this.parentElement.classList.remove('scale-[1.01]');
            });
            
            // 页面加载完成后的欢迎动画
            setTimeout(() => {
                const welcomeMsg = document.querySelector('.chat-message-bot:first-of-type');
                if (welcomeMsg) {
                    welcomeMsg.classList.add('animate-pulse');
                    setTimeout(() => {
                        welcomeMsg.classList.remove('animate-pulse');
                    }, 2000);
                }
            }, 1000);
        });
    </script>
</body>
</html>    