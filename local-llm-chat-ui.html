<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地AI服务管理平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        // 企业级配色方案 - 深灰蓝色系
                        primary: '#1E293B',      // 深灰蓝色 (主色调)
                        secondary: '#475569',    // 中灰色 (次要色)
                        tertiary: '#64748B',     // 浅灰色 (辅助色)
                        accent: '#0F172A',       // 深色强调色
                        success: '#059669',      // 成功色 (深绿)
                        warning: '#D97706',      // 警告色 (深橙)
                        error: '#DC2626',        // 错误色 (深红)
                        layer1: '#F8FAFC',       // 背景层1 (最浅)
                        layer2: '#F1F5F9',       // 背景层2
                        layer3: '#E2E8F0',       // 背景层3
                        layer4: '#CBD5E1',       // 背景层4 (最深)
                        component: 'rgba(255, 255, 255, 0.95)',
                        chatbot: '#1E293B',      // 机器人消息色
                        user: '#475569',         // 用户消息色
                    },
                    fontFamily: {
                        sans: ['Inter', '-apple-system', 'BlinkMacSystemFont', 'system-ui', 'sans-serif'],
                    },
                    boxShadow: {
                        'enterprise': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                        'enterprise-lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
                        'enterprise-xl': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                    }
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        :root {
            --chatbot-color: #1E293B;
            --user-color: #475569;
            --primary-color: #1E293B;
            --secondary-color: #475569;
            --accent-color: #0F172A;
            --success-color: #059669;
            --warning-color: #D97706;
            --error-color: #DC2626;
        }
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .module-border {
                border: 1px solid #e5e7eb;
                border-radius: 0.5rem;
            }
            .layer-padding {
                padding: 1rem;
            }
            .component-padding {
                padding: 0.75rem;
            }

            /* 企业级样式增强 */
            .enterprise-card {
                background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.95) 100%);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(226, 232, 240, 0.8);
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            }

            .enterprise-button {
                background: linear-gradient(135deg, #1E293B 0%, #475569 100%);
                border: 1px solid #334155;
                font-weight: 600;
                letter-spacing: 0.025em;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .enterprise-button:hover {
                background: linear-gradient(135deg, #0F172A 0%, #1E293B 100%);
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
                transform: translateY(-1px);
            }

            .enterprise-input {
                border: 2px solid #E2E8F0;
                background: rgba(248, 250, 252, 0.5);
                transition: all 0.3s ease;
            }

            .enterprise-input:focus {
                border-color: #1E293B;
                background: rgba(255, 255, 255, 0.9);
                box-shadow: 0 0 0 3px rgba(30, 41, 59, 0.1);
            }

            /* iOS灵动岛样式 */
            .dynamic-island {
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                z-index: 1000;
                transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                backdrop-filter: blur(20px);
                -webkit-backdrop-filter: blur(20px);
                background: rgba(0, 0, 0, 0.85);
                border-radius: 25px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                cursor: pointer;
                user-select: none;
            }

            .dynamic-island.compact {
                width: 200px;
                height: 40px;
                border-radius: 20px;
            }

            .dynamic-island.expanded {
                width: 350px;
                height: auto;
                min-height: 80px;
                border-radius: 25px;
            }

            .dynamic-island-content {
                padding: 12px 16px;
                color: white;
                font-size: 14px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                transition: all 0.3s ease;
            }

            .dynamic-island.compact .dynamic-island-content {
                padding: 8px 12px;
            }

            .dynamic-island-status {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .dynamic-island-indicator {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background: #10B981;
                animation: pulse 2s infinite;
            }

            .dynamic-island-indicator.offline {
                background: #EF4444;
                animation: none;
            }

            .dynamic-island-indicator.loading {
                background: #F59E0B;
                animation: spin 1s linear infinite;
            }

            .dynamic-island-details {
                opacity: 0;
                max-height: 0;
                overflow: hidden;
                transition: all 0.3s ease;
                margin-top: 12px;
                padding-top: 12px;
                border-top: 1px solid rgba(255, 255, 255, 0.1);
            }

            .dynamic-island.expanded .dynamic-island-details {
                opacity: 1;
                max-height: 200px;
            }

            .dynamic-island-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
                font-size: 12px;
            }

            .dynamic-island-row:last-child {
                margin-bottom: 0;
            }

            .dynamic-island-label {
                color: rgba(255, 255, 255, 0.7);
            }

            .dynamic-island-value {
                color: white;
                font-weight: 500;
            }

            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }

            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                .dynamic-island.compact {
                    width: 180px;
                    height: 36px;
                }

                .dynamic-island.expanded {
                    width: 320px;
                    left: 10px;
                    right: 10px;
                    transform: none;
                }
            }
            .text-shadow-sm {
                text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            }
            .chat-message {
                border-radius: 0.5rem;
                padding: 1rem;
                max-width: 85%;
                margin: 0.5rem 0;
                animation: fadeIn 0.3s ease-in-out;
            }
            .chat-message-bot {
                background-color: var(--chatbot-color);
                color: white;
                border-top-left-radius: 0;
            }
            .chat-message-user {
                background-color: var(--user-color);
                color: white;
                border-top-right-radius: 0;
                margin-left: auto;
            }
            .animate-fadeIn {
                animation: fadeIn 0.3s ease-in-out;
            }
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }
            .typing-indicator {
                display: inline-block;
            }
            .typing-indicator span {
                display: inline-block;
                width: 0.5rem;
                height: 0.5rem;
                background-color: white;
                border-radius: 50%;
                animation: bounce 1s infinite;
                margin-right: 0.25rem;
            }
            .typing-indicator span:nth-child(2) {
                animation-delay: 0.2s;
            }
            .typing-indicator span:nth-child(3) {
                animation-delay: 0.4s;
            }
            .modal-backdrop {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 40;
                transition: opacity 0.3s ease;
                opacity: 0;
                pointer-events: none;
            }
            .modal {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%) scale(0.95);
                width: 100%;
                max-width: 32rem;
                background-color: white;
                border-radius: 0.75rem;
                box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
                z-index: 50;
                transition: all 0.3s ease;
                opacity: 0;
            }
            .modal.active {
                transform: translate(-50%, -50%) scale(1);
                opacity: 1;
            }
            .modal-backdrop.active {
                opacity: 1;
                pointer-events: auto;
            }
            .btn-hover {
                transition: all 0.2s ease;
            }
            .btn-hover:hover {
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                transform: translateY(-2px);
            }
            .card-hover {
                transition: all 0.3s ease;
            }
            .card-hover:hover {
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
                transform: translateY(-4px);
            }
            .gradient-text {
                background: linear-gradient(to right, #4F46E5, #F59E0B);
                background-clip: text;
                -webkit-background-clip: text;
                color: transparent;
            }
            .cursor {
                animation: blink 1s infinite;
            }
            @keyframes blink {
                0%, 50% { opacity: 1; }
                51%, 100% { opacity: 0; }
            }
            .streaming-content {
                white-space: pre-wrap;
            }

            /* 监控面板样式优化 */
            .monitoring-card {
                background-color: #EEF2FF;
                border-radius: 0.5rem;
                text-align: center;
                transition: all 0.3s ease;
                min-height: 70px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                min-width: 120px; /* 确保最小宽度 */
                max-width: 200px; /* 限制最大宽度 */
            }
            .monitoring-card:hover {
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
                transform: translateY(-4px);
            }

            .monitoring-grid {
                display: grid;
                gap: 0.5rem;
                width: 100%;
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            }

            @media (min-width: 640px) {
                .monitoring-grid {
                    grid-template-columns: repeat(3, 1fr);
                }
            }

            @media (min-width: 768px) {
                .monitoring-grid {
                    grid-template-columns: repeat(4, 1fr);
                }
            }

            @media (min-width: 1024px) {
                .monitoring-grid {
                    grid-template-columns: repeat(6, 1fr);
                }
            }

            /* 思考过程样式 */
            .thinking-section {
                background-color: #f9fafb;
                border-left: 4px solid #d1d5db;
                border-radius: 0.5rem;
                padding: 0.75rem;
                margin: 0.5rem 0;
                font-size: 0.875rem;
                font-style: italic;
                color: #6b7280;
            }

            .thinking-toggle {
                font-size: 0.75rem;
                color: #2563eb;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 0.25rem;
                margin-bottom: 0.5rem;
                transition: color 0.2s ease;
            }

            .thinking-toggle:hover {
                color: #1e40af;
            }

            .thinking-content {
                color: #4b5563;
                white-space: pre-wrap;
                line-height: 1.4;
            }

            .thinking-collapsed {
                display: none;
            }

            /* 消息操作按钮 */
            .message-actions {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                margin-top: 0.5rem;
                opacity: 0;
                transition: opacity 0.2s ease;
            }

            .chat-message:hover .message-actions {
                opacity: 1;
            }

            .action-btn {
                font-size: 0.75rem;
                color: #6b7280;
                cursor: pointer;
                padding: 0.25rem;
                border-radius: 0.25rem;
                transition: all 0.2s ease;
            }

            .action-btn:hover {
                color: #374151;
                background-color: #f3f4f6;
            }

            /* 代码块样式 */
            .code-block {
                background-color: #111827;
                color: #f3f4f6;
                border-radius: 0.5rem;
                padding: 1rem;
                margin: 0.5rem 0;
                position: relative;
                overflow-x: auto;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 0.875rem;
                line-height: 1.5;
            }

            .code-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 0.5rem;
                color: #9ca3af;
                font-size: 0.75rem;
            }

            .copy-code-btn {
                background-color: #374151;
                color: #d1d5db;
                padding: 0.25rem 0.5rem;
                border-radius: 0.25rem;
                font-size: 0.75rem;
                cursor: pointer;
                transition: background-color 0.2s ease;
            }

            .copy-code-btn:hover {
                background-color: #4b5563;
            }

            /* 数学公式样式 */
            .math-inline {
                background-color: #eff6ff;
                padding: 0 0.25rem;
                border-radius: 0.25rem;
                color: #1e40af;
            }

            .math-block {
                background-color: #eff6ff;
                padding: 0.75rem;
                border-radius: 0.5rem;
                margin: 0.5rem 0;
                text-align: center;
                overflow-x: auto;
            }

            /* 消息时间戳 */
            .message-timestamp {
                font-size: 0.75rem;
                color: #9ca3af;
                margin-top: 0.25rem;
            }

            /* 消息评价按钮 */
            .rating-buttons {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                margin-top: 0.5rem;
            }

            .rating-btn {
                color: #9ca3af;
                cursor: pointer;
                transition: color 0.2s ease;
            }

            .rating-btn:hover {
                color: #4b5563;
            }

            .rating-btn.active-like {
                color: #059669;
            }

            .rating-btn.active-dislike {
                color: #dc2626;
            }

            /* 欢迎界面动画 */
            .welcome-fade-out {
                animation: fadeOut 0.5s ease-out forwards;
            }

            @keyframes fadeOut {
                from { opacity: 1; transform: translateY(0); }
                to { opacity: 0; transform: translateY(-20px); }
            }
        }
    </style>
</head>
<body class="bg-gray-50 font-sans text-gray-800 min-h-screen flex flex-col">
    <!-- iOS灵动岛样式状态栏 -->
    <div id="dynamic-island" class="dynamic-island compact">
        <div class="dynamic-island-content">
            <div class="dynamic-island-status">
                <div id="island-indicator" class="dynamic-island-indicator"></div>
                <span id="island-model-name">未连接</span>
            </div>
            <div id="island-toggle" class="text-xs opacity-70">
                <i class="fa fa-chevron-down"></i>
            </div>
        </div>
        <div class="dynamic-island-details">
            <div class="dynamic-island-row">
                <span class="dynamic-island-label">服务类型</span>
                <span id="island-service-type" class="dynamic-island-value">-</span>
            </div>
            <div class="dynamic-island-row">
                <span class="dynamic-island-label">服务地址</span>
                <span id="island-service-url" class="dynamic-island-value">-</span>
            </div>
            <div class="dynamic-island-row">
                <span class="dynamic-island-label">响应时间</span>
                <span id="island-response-time" class="dynamic-island-value">-</span>
            </div>
            <div class="dynamic-island-row">
                <span class="dynamic-island-label">模型类型</span>
                <span id="island-model-type" class="dynamic-island-value">-</span>
            </div>
        </div>
    </div>

    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm sticky top-0 z-10 transition-all duration-300">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center">
                <i class="fa fa-server text-primary text-2xl mr-3"></i>
                <div>
                    <h1 class="text-xl font-bold text-primary">AI服务集成平台</h1>
                    <p class="text-xs text-gray-500 -mt-1">统一管理 · 便捷调用· 恒泰技术</p>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <button id="clear-chat" class="text-gray-500 hover:text-primary transition-colors btn-hover" title="清空对话">
                    <i class="fa fa-trash"></i>
                </button>
                <button id="settings-button" class="text-gray-500 hover:text-primary transition-colors btn-hover" title="设置">
                    <i class="fa fa-cog"></i>
                </button>
                <button id="help-button" class="text-gray-500 hover:text-primary transition-colors btn-hover" title="帮助">
                    <i class="fa fa-question-circle"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- 主要内容区 -->
    <main class="flex-1 container mx-auto p-4 md:p-6 flex flex-col">
        <!-- 模型状态卡片 - 已被动态岛替代，隐藏 -->
        <div id="model-status-bar" class="hidden bg-white rounded-xl p-4 shadow-enterprise-lg mb-6 flex flex-col md:flex-row items-center justify-between card-hover sticky top-20 z-30 border border-gray-200 backdrop-blur-sm bg-white/95">
            <div class="flex items-center mb-4 md:mb-0">
                <div class="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center mr-3">
                    <i class="fa fa-server text-primary"></i>
                </div>
                <div>
                    <h2 class="font-bold text-gray-800">模型状态</h2>
                    <div class="flex items-center mt-1">
                        <span id="model-status-indicator" class="w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                        <span id="model-status-text" class="text-sm text-gray-600">检测中...</span>
                    </div>
                </div>
            </div>
            <!-- 增强的监控面板 -->
            <div class="monitoring-grid">
                <!-- 第一行：基础统计 -->
                <div class="monitoring-card p-2 border-l-4 border-blue-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-comments mr-1"></i>对话轮数
                    </div>
                    <div id="conversation-count-display" class="font-semibold text-blue-600 text-sm">0</div>
                </div>

                <div class="monitoring-card p-2 border-l-4 border-green-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-clock mr-1"></i>响应时间
                    </div>
                    <div id="response-time-display" class="font-semibold text-green-600 text-sm">--</div>
                </div>

                <div class="monitoring-card p-2 border-l-4 border-purple-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-font mr-1"></i>总字符数
                    </div>
                    <div id="total-char-count-display" class="font-semibold text-purple-600 text-sm">0</div>
                </div>

                <div class="monitoring-card p-2 border-l-4 border-indigo-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-hourglass-half mr-1"></i>会话时长
                    </div>
                    <div id="session-duration-display" class="font-semibold text-indigo-600 text-sm">00:00:00</div>
                </div>

                <div class="monitoring-card p-2 border-l-4 border-orange-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-calculator mr-1"></i>平均字符
                    </div>
                    <div id="average-chars-display" class="font-semibold text-orange-600 text-sm">--</div>
                </div>

                <div class="monitoring-card p-2 border-l-4 border-teal-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-cog mr-1"></i>当前模式
                    </div>
                    <div id="current-mode-display" class="font-semibold text-teal-600 text-sm">文本</div>
                </div>

                <div class="monitoring-card p-2 border-l-4 border-red-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-bolt mr-1"></i>首字节延迟
                    </div>
                    <div id="first-byte-time-display" class="font-semibold text-red-600 text-sm">--</div>
                </div>

                <div class="monitoring-card p-2 border-l-4 border-yellow-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-tachometer-alt mr-1"></i>输出速度
                    </div>
                    <div id="streaming-speed-display" class="font-semibold text-yellow-600 text-sm">--</div>
                </div>

                <div class="monitoring-card p-2 border-l-4 border-green-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-link mr-1"></i>连接状态
                    </div>
                    <div id="connection-status-display" class="font-semibold text-green-600 text-sm">未连接</div>
                </div>

                <div class="monitoring-card p-2 border-l-4 border-blue-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-cube mr-1"></i>模型状态
                    </div>
                    <div id="model-load-status-display" class="font-semibold text-blue-600 text-sm">未知</div>
                </div>

                <div class="monitoring-card p-2 border-l-4 border-pink-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-wifi mr-1"></i>网络延迟
                    </div>
                    <div id="network-latency-display" class="font-semibold text-pink-600 text-sm">--</div>
                </div>

                <div class="monitoring-card p-2 border-l-4 border-gray-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-chart-bar mr-1"></i>详细统计
                    </div>
                    <button id="stats-detail-btn" class="font-semibold text-gray-600 hover:text-gray-800 transition-colors text-sm">查看</button>
                </div>

                <!-- 系统信息卡片 -->
                <div class="monitoring-card p-2 border-l-4 border-indigo-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-microchip mr-1"></i>CPU核心
                    </div>
                    <div id="cpu-cores-display" class="font-semibold text-indigo-600 text-sm">--</div>
                </div>

                <div class="monitoring-card p-2 border-l-4 border-cyan-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-memory mr-1"></i>内存使用
                    </div>
                    <div id="memory-usage-display" class="font-semibold text-cyan-600 text-sm">--</div>
                </div>

                <div class="monitoring-card p-2 border-l-4 border-emerald-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-globe mr-1"></i>网络类型
                    </div>
                    <div id="network-type-display" class="font-semibold text-emerald-600 text-sm">--</div>
                </div>

                <!-- AI服务状态卡片 -->
                <div class="monitoring-card p-2 border-l-4 border-amber-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-server mr-1"></i>Ollama
                    </div>
                    <div id="ollama-status-display" class="font-semibold text-amber-600 text-sm">检测中</div>
                </div>

                <div class="monitoring-card p-2 border-l-4 border-lime-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-laptop mr-1"></i>LM Studio
                    </div>
                    <div id="lm-studio-status-display" class="font-semibold text-lime-600 text-sm">检测中</div>
                </div>

                <div class="monitoring-card p-2 border-l-4 border-orange-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-cogs mr-1"></i>WebUI
                    </div>
                    <div id="webui-status-display" class="font-semibold text-orange-600 text-sm">检测中</div>
                </div>

                <div class="monitoring-card p-2 border-l-4 border-rose-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-cloud mr-1"></i>LocalAI
                    </div>
                    <div id="localai-status-display" class="font-semibold text-rose-600 text-sm">检测中</div>
                </div>
            </div>

            <!-- 详细统计弹窗（隐藏） -->
            <div id="stats-detail-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
                <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-800">详细统计信息</h3>
                        <button id="close-stats-modal" class="text-gray-500 hover:text-gray-700">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                    <div class="grid grid-cols-3 gap-4">
                        <div class="space-y-3">
                            <h4 class="text-sm font-semibold text-gray-700 border-b pb-1">对话统计</h4>
                            <div class="bg-blue-50 p-3 rounded-lg">
                                <div class="text-sm text-blue-600 font-medium">用户输入字符</div>
                                <div id="user-char-count-display" class="text-xl font-bold text-blue-700">0</div>
                            </div>
                            <div class="bg-green-50 p-3 rounded-lg">
                                <div class="text-sm text-green-600 font-medium">AI输出字符</div>
                                <div id="ai-char-count-display" class="text-xl font-bold text-green-700">0</div>
                            </div>
                            <div class="bg-purple-50 p-3 rounded-lg">
                                <div class="text-sm text-purple-600 font-medium">字符比例 (AI:用户)</div>
                                <div id="char-ratio-display" class="text-xl font-bold text-purple-700">--</div>
                            </div>
                            <div class="bg-orange-50 p-3 rounded-lg">
                                <div class="text-sm text-orange-600 font-medium">会话效率 (字符/分钟)</div>
                                <div id="session-efficiency-display" class="text-xl font-bold text-orange-700">--</div>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <h4 class="text-sm font-semibold text-gray-700 border-b pb-1">系统信息</h4>
                            <div class="bg-indigo-50 p-3 rounded-lg">
                                <div class="text-sm text-indigo-600 font-medium">操作系统</div>
                                <div id="system-platform-display" class="text-lg font-bold text-indigo-700">--</div>
                            </div>
                            <div class="bg-cyan-50 p-3 rounded-lg">
                                <div class="text-sm text-cyan-600 font-medium">浏览器</div>
                                <div id="browser-info-display" class="text-lg font-bold text-cyan-700">--</div>
                            </div>
                            <div class="bg-teal-50 p-3 rounded-lg">
                                <div class="text-sm text-teal-600 font-medium">屏幕分辨率</div>
                                <div id="screen-info-display" class="text-lg font-bold text-teal-700">--</div>
                            </div>
                            <div class="bg-emerald-50 p-3 rounded-lg">
                                <div class="text-sm text-emerald-600 font-medium">时区</div>
                                <div id="timezone-display" class="text-lg font-bold text-emerald-700">--</div>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <h4 class="text-sm font-semibold text-gray-700 border-b pb-1">性能指标</h4>
                            <div class="bg-rose-50 p-3 rounded-lg">
                                <div class="text-sm text-rose-600 font-medium">页面加载时间</div>
                                <div id="page-load-time-display" class="text-lg font-bold text-rose-700">--</div>
                            </div>
                            <div class="bg-amber-50 p-3 rounded-lg">
                                <div class="text-sm text-amber-600 font-medium">首次绘制</div>
                                <div id="first-paint-display" class="text-lg font-bold text-amber-700">--</div>
                            </div>
                            <div class="bg-lime-50 p-3 rounded-lg">
                                <div class="text-sm text-lime-600 font-medium">WebGL支持</div>
                                <div id="webgl-support-display" class="text-lg font-bold text-lime-700">--</div>
                            </div>
                            <div class="bg-violet-50 p-3 rounded-lg">
                                <div class="text-sm text-violet-600 font-medium">存储配额</div>
                                <div id="storage-quota-display" class="text-lg font-bold text-violet-700">--</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 对话区域 -->
        <div class="flex-1 bg-white rounded-xl shadow-enterprise overflow-hidden flex flex-col mb-6 transition-all duration-300 hover:shadow-enterprise-lg border border-gray-200">
            <div class="bg-gradient-to-r from-primary to-secondary text-white p-5 border-b border-gray-100 flex items-center">
                <i class="fa fa-comments mr-3 text-lg"></i>
                <h2 class="font-semibold text-lg">智能对话</h2>
                <div class="ml-auto flex items-center text-sm opacity-90">
                    <i class="fa fa-shield mr-2"></i>
                    <span>企业级安全</span>
                </div>
            </div>
            
            <div id="chat-container" class="flex-1 overflow-y-auto p-4 space-y-4">
                <!-- 欢迎界面 -->
                <div id="welcome-screen" class="flex flex-col items-center justify-center h-full text-center py-12">
                    <div class="w-20 h-20 rounded-xl bg-gradient-to-br from-primary to-secondary flex items-center justify-center mb-8 shadow-enterprise-lg">
                        <i class="fa fa-server text-white text-3xl"></i>
                    </div>
                    <h2 class="text-3xl font-bold text-gray-800 mb-2">本地大模型管理界面</h2>
                    <p class="text-lg text-primary font-medium mb-6">统一管理 · 便捷调用 · 高效集成</p>
                    <p class="text-gray-600 mb-8 max-w-lg leading-relaxed">
                        统一管理本地AI服务，支持Ollama、LM Studio等多种平台。<br>
                        <span class="text-sm text-gray-500">开源工具，本地部署，保护数据隐私</span>
                    </p>

                    <!-- 功能特性卡片 -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl w-full">
                        <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                            <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mb-3 mx-auto">
                                <i class="fa fa-comments text-blue-600"></i>
                            </div>
                            <h3 class="font-semibold text-gray-800 mb-2">智能对话</h3>
                            <p class="text-sm text-gray-600">支持多轮对话，理解上下文，提供准确回答</p>
                        </div>

                        <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                            <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mb-3 mx-auto">
                                <i class="fa fa-image text-green-600"></i>
                            </div>
                            <h3 class="font-semibold text-gray-800 mb-2">图像理解</h3>
                            <p class="text-sm text-gray-600">上传图片进行分析，支持OCR和场景描述</p>
                        </div>

                        <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                            <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mb-3 mx-auto">
                                <i class="fa fa-cog text-purple-600"></i>
                            </div>
                            <h3 class="font-semibold text-gray-800 mb-2">本地部署</h3>
                            <p class="text-sm text-gray-600">数据安全，响应快速，支持多种模型</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 输入区域 -->
            <div class="p-4 border-t border-gray-100 bg-gray-50">
                <div class="relative">
                    <textarea id="user-input" class="w-full border border-gray-200 rounded-lg p-3 pr-20 focus:outline-none focus:ring-2 focus:ring-primary/50 resize-none transition-all duration-200 hover:border-primary/30" rows="2" placeholder="输入问题或上传图片..."></textarea>
                    <div class="absolute right-3 bottom-3 flex items-center space-x-2 pointer-events-none">
                        <label for="image-upload" class="text-gray-500 hover:text-primary cursor-pointer transition-colors btn-hover pointer-events-auto" title="上传图片">
                            <i class="fa fa-image text-lg"></i>
                        </label>
                        <input id="image-upload" type="file" accept="image/*" class="hidden">
                        <button id="send-button" class="bg-primary hover:bg-primary/90 text-white rounded-full w-9 h-9 flex items-center justify-center transition-all duration-200 hover:shadow-md hover:scale-105 active:scale-95 pointer-events-auto" title="发送">
                            <i class="fa fa-paper-plane"></i>
                        </button>
                        </div>
                    </div>
                </div>
                <div class="flex items-center justify-between mt-3 text-xs text-gray-500">
                    <div>支持文本、图片多模态交互</div>
                    <div>按 Enter 发送，Shift+Enter 换行</div>
                </div>
            </div>
        </div>
        
        <!-- 功能卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 shadow-enterprise border border-gray-200 hover:shadow-enterprise-lg transition-all duration-300 card-hover cursor-pointer group" onclick="selectMode('文本')">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mr-4 group-hover:bg-primary/20 transition-colors">
                        <i class="fa fa-file-text text-primary text-xl"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 text-lg">文本分析</h3>
                </div>
                <p class="text-sm text-gray-600 leading-relaxed">企业级文本理解与生成，支持专业文档分析、内容创作和智能问答。</p>
                <div class="mt-4 flex items-center text-xs text-primary font-medium">
                    <i class="fa fa-check-circle mr-2"></i>
                    <span>专业可靠</span>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 shadow-enterprise border border-gray-200 hover:shadow-enterprise-lg transition-all duration-300 card-hover cursor-pointer group" onclick="selectMode('图像理解')">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 rounded-lg bg-warning/10 flex items-center justify-center mr-4 group-hover:bg-warning/20 transition-colors">
                        <i class="fa fa-eye text-warning text-xl"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 text-lg">图像理解</h3>
                </div>
                <p class="text-sm text-gray-600 leading-relaxed">先进的视觉AI技术，支持图片识别、场景分析、OCR文字提取和智能内容描述。</p>
                <div class="mt-4 flex items-center text-xs text-warning font-medium">
                    <i class="fa fa-check-circle mr-2"></i>
                    <span>精准识别</span>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-enterprise border border-gray-200 hover:shadow-enterprise-lg transition-all duration-300 card-hover cursor-pointer group" onclick="selectMode('图像生成')">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 rounded-lg bg-purple-100 flex items-center justify-center mr-4 group-hover:bg-purple-200 transition-colors">
                        <i class="fa fa-magic text-purple-600 text-xl"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 text-lg">图像生成</h3>
                </div>
                <p class="text-sm text-gray-600 leading-relaxed">基于Stable Diffusion等先进模型，根据文本描述生成高质量专业图片。</p>
                <div class="mt-4 flex items-center text-xs text-purple-600 font-medium">
                    <i class="fa fa-check-circle mr-2"></i>
                    <span>创意无限</span>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 shadow-enterprise border border-gray-200 hover:shadow-enterprise-lg transition-all duration-300 card-hover cursor-pointer group" onclick="selectMode('语音')">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 rounded-lg bg-success/10 flex items-center justify-center mr-4 group-hover:bg-success/20 transition-colors">
                        <i class="fa fa-microphone text-success text-xl"></i>
                    </div>
                    <h3 class="font-semibold text-gray-800 text-lg">语音交互</h3>
                </div>
                <p class="text-sm text-gray-600 leading-relaxed">集成先进语音识别技术，支持语音输入和音频内容理解，提供自然交互体验。</p>
                <div class="mt-4 flex items-center text-xs text-success font-medium">
                    <i class="fa fa-check-circle mr-2"></i>
                    <span>自然流畅</span>
                </div>
            </div>
        </div>
    </main>

    <!-- 设置模态框 -->
    <div id="settings-modal" class="modal">
        <div class="p-5">
            <div class="flex justify-between items-center mb-4">
                <div>
                    <h3 class="text-lg font-bold text-gray-800">模型对接设置</h3>
                    <div id="connection-status" class="flex items-center space-x-2 mt-1">
                        <div id="status-indicator" class="w-2 h-2 rounded-full bg-gray-400"></div>
                        <span id="status-text" class="text-xs text-gray-600">检查连接中...</span>
                    </div>
                </div>
                <button id="close-settings" class="text-gray-400 hover:text-gray-600 btn-hover" title="关闭">
                    <i class="fa fa-times"></i>
                </button>
            </div>
            
            <!-- 标签页导航 -->
            <div class="flex border-b border-gray-200 mb-4 overflow-x-auto">
                <button class="settings-tab-btn px-4 py-2 text-sm font-medium border-b-2 border-primary text-primary whitespace-nowrap" data-tab="models">
                    <i class="fa fa-server mr-2"></i>模型管理
                </button>
                <button class="settings-tab-btn px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 whitespace-nowrap" data-tab="llm">
                    <i class="fa fa-robot mr-2"></i>LLM配置
                </button>
                <button class="settings-tab-btn px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 whitespace-nowrap" data-tab="image">
                    <i class="fa fa-image mr-2"></i>图像生成
                </button>
                <button class="settings-tab-btn px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 whitespace-nowrap" data-tab="general">
                    <i class="fa fa-cog mr-2"></i>通用设置
                </button>
            </div>

            <!-- 模型管理标签页 -->
            <div id="models-tab" class="settings-tab-content space-y-4">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <div class="flex items-start">
                        <i class="fa fa-info-circle text-blue-500 mt-1 mr-3"></i>
                        <div>
                            <h4 class="text-sm font-medium text-blue-800 mb-1">模型发现</h4>
                            <p class="text-xs text-blue-700">自动扫描本地AI服务，发现所有可用模型。支持Ollama、LM Studio、OpenAI兼容服务等。</p>
                        </div>
                    </div>
                </div>

                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">发现的AI服务</h3>
                    <button id="refresh-services" class="px-3 py-2 bg-primary text-white rounded-lg text-sm font-medium hover:bg-primary/90 transition-all duration-200 btn-hover">
                        <i class="fa fa-refresh mr-2"></i>刷新服务
                    </button>
                </div>

                <div id="services-container" class="space-y-4">
                    <!-- 服务列表将动态生成 -->
                    <div class="text-center py-8 text-gray-500">
                        <i class="fa fa-spinner fa-spin text-2xl mb-2"></i>
                        <p>正在扫描本地AI服务...</p>
                    </div>
                </div>

                <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                    <h4 class="text-sm font-medium text-gray-700 mb-2">快速操作</h4>
                    <div class="grid grid-cols-2 gap-3">
                        <button id="test-all-services" class="px-3 py-2 border border-gray-300 rounded-lg text-sm text-gray-700 hover:bg-gray-100 transition-all duration-200">
                            <i class="fa fa-check-circle mr-2"></i>测试所有服务
                        </button>
                        <button id="export-models" class="px-3 py-2 border border-gray-300 rounded-lg text-sm text-gray-700 hover:bg-gray-100 transition-all duration-200">
                            <i class="fa fa-download mr-2"></i>导出模型列表
                        </button>
                    </div>
                </div>
            </div>

            <!-- LLM配置标签页 -->
            <div id="llm-tab" class="settings-tab-content space-y-4 hidden">
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">API类型</label>
                    <select id="api-type" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-200 hover:border-primary/30">
                        <option value="ollama">Ollama (推荐)</option>
                        <option value="lm-studio">LM Studio</option>
                        <option value="openai-compatible">OpenAI兼容 (LocalAI等)</option>
                        <option value="custom">自定义API</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">API地址</label>
                    <input type="text" id="api-url" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-200 hover:border-primary/30" value="http://localhost:1234/v1" placeholder="http://localhost:1234/v1">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">API密钥 (可选)</label>
                    <input type="password" id="api-key" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-200 hover:border-primary/30" placeholder="留空表示无需密钥">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">模型名称</label>
                    <div class="flex space-x-2">
                        <select id="model-name" class="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-200 hover:border-primary/30">
                            <option value="llama3.2">llama3.2</option>
                        </select>
                        <button id="refresh-models" class="px-3 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 transition-all duration-200" title="刷新模型列表">
                            <i class="fa fa-refresh"></i>
                        </button>
                    </div>
                    <div id="model-status" class="mt-1 text-xs text-gray-500"></div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">系统提示词</label>
                    <textarea id="system-prompt" rows="3" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-200 hover:border-primary/30" placeholder="你是一个有用的AI助手。">你是一个有用的AI助手。</textarea>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">响应质量设置</label>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm text-gray-600 mb-2">回答风格</label>
                            <select id="response-style" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-200 hover:border-primary/30">
                                <option value="balanced">平衡模式 (推荐)</option>
                                <option value="precise">精确模式 (更准确)</option>
                                <option value="creative">创意模式 (更灵活)</option>
                            </select>
                            <p class="text-xs text-gray-500 mt-1">平衡模式适合大多数企业应用场景</p>
                        </div>

                        <div>
                            <label class="block text-sm text-gray-600 mb-2">回答长度</label>
                            <select id="response-length" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-200 hover:border-primary/30">
                                <option value="short">简洁回答 (500字以内)</option>
                                <option value="medium">标准回答 (1000字以内)</option>
                                <option value="long">详细回答 (2000字以内)</option>
                            </select>
                        </div>

                        <!-- 高级参数折叠区域 -->
                        <div class="border-t border-gray-200 pt-4">
                            <button type="button" id="toggle-advanced" class="flex items-center text-sm text-gray-600 hover:text-primary transition-colors">
                                <i class="fa fa-cog mr-2"></i>
                                <span>高级参数设置</span>
                                <i class="fa fa-chevron-down ml-2 transition-transform" id="advanced-chevron"></i>
                            </button>

                            <div id="advanced-params" class="hidden mt-4 space-y-3 bg-gray-50 p-4 rounded-lg">
                                <div>
                                    <label class="text-xs text-gray-500">温度 (Temperature)</label>
                                    <input type="range" id="temperature" min="0" max="2" step="0.1" value="0.7" class="w-full accent-primary">
                                    <div class="flex justify-between text-xs text-gray-500">
                                        <span>0 (精确)</span>
                                        <span id="temp-value">0.7</span>
                                        <span>2 (创意)</span>
                                    </div>
                                </div>
                                <div>
                                    <label class="text-xs text-gray-500">最大令牌数</label>
                                    <input type="range" id="max-length" min="100" max="2000" step="100" value="1000" class="w-full accent-primary">
                                    <div class="flex justify-between text-xs text-gray-500">
                                        <span>100</span>
                                        <span id="length-value">1000</span>
                                        <span>2000</span>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 italic">⚠️ 高级参数仅供技术人员调整，错误设置可能影响回答质量</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div>
                    <label class="flex items-center cursor-pointer">
                        <input type="checkbox" id="use-gpu" class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary cursor-pointer">
                        <span class="ml-2 text-sm text-gray-700">使用GPU加速</span>
                    </label>
                </div>
                
                <div>
                    <label class="flex items-center cursor-pointer">
                        <input type="checkbox" id="stream-output" class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary cursor-pointer">
                        <span class="ml-2 text-sm text-gray-700">流式输出</span>
                    </label>
                </div>

                <div class="mt-4 pt-4 border-t border-gray-200">
                    <button id="test-connection" class="w-full px-4 py-2 border border-primary text-primary rounded-lg text-sm font-medium hover:bg-primary/10 transition-all duration-200 btn-hover">
                        <i class="fa fa-plug mr-2"></i>测试LLM连接
                    </button>
                </div>
            </div>

            <!-- 图像生成标签页 -->
            <div id="image-tab" class="settings-tab-content space-y-4 hidden">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <div class="flex items-start">
                        <i class="fa fa-info-circle text-blue-500 mt-1 mr-3"></i>
                        <div>
                            <h4 class="text-sm font-medium text-blue-800 mb-1">图像生成说明</h4>
                            <p class="text-xs text-blue-700">图像生成功能需要单独的AI绘画服务，如Stable Diffusion WebUI。VL模型只能理解图片，不能生成图片。</p>
                        </div>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">图像生成服务</label>
                    <select id="image-api-type" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-200 hover:border-primary/30">
                        <option value="automatic1111">Automatic1111 WebUI (推荐)</option>
                        <option value="stable-diffusion-webui">Stable Diffusion WebUI</option>
                        <option value="comfyui">ComfyUI (高级用户)</option>
                        <option value="openai-dalle">OpenAI DALL-E</option>
                        <option value="custom-image">自定义图片API</option>
                    </select>
                    <p class="text-xs text-gray-500 mt-1">推荐使用Automatic1111，配置简单，功能强大</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">服务地址</label>
                    <input type="text" id="image-api-url" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-200 hover:border-primary/30" value="http://localhost:7860" placeholder="http://localhost:7860">
                    <p class="text-xs text-gray-500 mt-1">Automatic1111默认端口：7860，ComfyUI默认端口：8188</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">API密钥 (可选)</label>
                    <input type="password" id="image-api-key" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-200 hover:border-primary/30" placeholder="大多数本地服务无需密钥">
                </div>

                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex items-start">
                        <i class="fa fa-lightbulb text-yellow-600 mt-1 mr-3"></i>
                        <div>
                            <h4 class="text-sm font-medium text-yellow-800 mb-2">快速设置指南</h4>
                            <ol class="text-xs text-yellow-700 space-y-1">
                                <li>1. 下载并安装 Automatic1111 WebUI</li>
                                <li>2. 启动时添加参数：--api --cors-allow-origins=*</li>
                                <li>3. 确保服务运行在 http://localhost:7860</li>
                                <li>4. 点击下方"测试连接"验证配置</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <div class="mt-4 pt-4 border-t border-gray-200">
                    <button id="test-image-connection" class="w-full px-4 py-2 border border-purple-500 text-purple-600 rounded-lg text-sm font-medium hover:bg-purple-50 transition-all duration-200 btn-hover">
                        <i class="fa fa-image mr-2"></i>测试图像生成连接
                    </button>
                </div>
            </div>

            <!-- 通用设置标签页 -->
            <div id="general-tab" class="settings-tab-content space-y-4 hidden">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">可用模式</label>
                    <div class="grid grid-cols-2 gap-3">
                        <button class="mode-btn bg-primary text-white py-3 px-4 rounded-lg text-sm btn-hover flex items-center justify-center" data-mode="多模态" title="支持文本+图片理解">
                            <i class="fa fa-comments mr-2"></i>多模态
                        </button>
                        <button class="mode-btn bg-gray-200 text-gray-700 py-3 px-4 rounded-lg text-sm btn-hover flex items-center justify-center" data-mode="文本" title="纯文本对话">
                            <i class="fa fa-font mr-2"></i>文本
                        </button>
                    </div>
                    <div class="grid grid-cols-2 gap-3 mt-3">
                        <button class="mode-btn bg-gray-200 text-gray-700 py-3 px-4 rounded-lg text-sm btn-hover flex items-center justify-center" data-mode="图像理解" title="图片分析和理解">
                            <i class="fa fa-eye mr-2"></i>图像理解
                        </button>
                        <button class="mode-btn bg-gray-200 text-gray-700 py-3 px-4 rounded-lg text-sm btn-hover flex items-center justify-center" data-mode="图像生成" title="文本生成图片">
                            <i class="fa fa-magic mr-2"></i>图像生成
                        </button>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">界面主题</label>
                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                        <option value="light">浅色主题</option>
                        <option value="dark">深色主题 (开发中)</option>
                        <option value="auto">跟随系统</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">默认启动模式</label>
                    <select id="default-mode" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary/50">
                        <option value="多模态">多模态模式</option>
                        <option value="文本">文本模式</option>
                        <option value="图像理解">图像理解模式</option>
                        <option value="图像生成">图像生成模式</option>
                    </select>
                </div>

                <div>
                    <label class="flex items-center cursor-pointer">
                        <input type="checkbox" id="auto-save-chat" class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary cursor-pointer" checked>
                        <span class="ml-2 text-sm text-gray-700">自动保存对话历史</span>
                    </label>
                </div>

                <div>
                    <label class="flex items-center cursor-pointer">
                        <input type="checkbox" id="show-performance" class="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary cursor-pointer" checked>
                        <span class="ml-2 text-sm text-gray-700">显示性能监控面板</span>
                    </label>
                </div>
            </div>

            <div class="mt-6 flex justify-between border-t border-gray-200 pt-4">
                <button id="reset-settings" class="px-4 py-2 border border-red-300 text-red-600 rounded-lg text-sm font-medium hover:bg-red-50 transition-all duration-200 btn-hover">
                    <i class="fa fa-refresh mr-2"></i>重置设置
                </button>
                <div class="flex space-x-3">
                    <button id="cancel-settings" class="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-all duration-200 btn-hover">取消</button>
                    <button id="save-settings" class="px-4 py-2 bg-primary border border-transparent rounded-lg text-sm font-medium text-white hover:bg-primary/90 transition-all duration-200 btn-hover">保存设置</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 模态框背景 -->
    <div id="modal-backdrop" class="modal-backdrop"></div>

    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-200 py-4 text-center text-sm text-gray-500">
        <p>本地AI服务调用工具 © 2025 | 恒泰技术</p>
    </footer>

    <script>
        // 聊天功能实现
        document.addEventListener('DOMContentLoaded', function() {
            const chatContainer = document.getElementById('chat-container');
            const userInput = document.getElementById('user-input');
            const sendButton = document.getElementById('send-button');
            const imageUpload = document.getElementById('image-upload');
            const settingsButton = document.getElementById('settings-button');
            const closeSettings = document.getElementById('close-settings');
            const cancelSettings = document.getElementById('cancel-settings');
            const saveSettings = document.getElementById('save-settings');
            const testConnection = document.getElementById('test-connection');
            const clearChatButton = document.getElementById('clear-chat');
            const helpButton = document.getElementById('help-button');
            const settingsModal = document.getElementById('settings-modal');
            const modalBackdrop = document.getElementById('modal-backdrop');
            const modeButtons = document.querySelectorAll('.mode-btn');
            const currentModeElement = document.getElementById('current-mode');
            const temperatureSlider = document.getElementById('temperature');
            const tempValue = document.getElementById('temp-value');
            const maxLengthSlider = document.getElementById('max-length');
            const lengthValue = document.getElementById('length-value');
            const header = document.querySelector('header');
            const refreshModels = document.getElementById('refresh-models');
            const modelSelect = document.getElementById('model-name');
            const modelStatus = document.getElementById('model-status');
            const statusIndicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('status-text');

            let currentMode = '多模态';
            let conversationHistory = [];
            let currentImageData = null;

            // 服务发现管理
            class ServiceDiscovery {
                constructor() {
                    this.services = [
                        {
                            name: 'Ollama',
                            url: 'http://localhost:11434',
                            type: 'ollama',
                            icon: 'fa-server',
                            description: '本地Ollama服务',
                            endpoints: {
                                models: '/api/tags',
                                health: '/api/version'
                            }
                        },
                        {
                            name: 'LM Studio',
                            url: 'http://localhost:1234',
                            type: 'lm-studio',
                            icon: 'fa-desktop',
                            description: 'LM Studio本地服务',
                            endpoints: {
                                models: '/v1/models',
                                health: '/v1/models'
                            }
                        },
                        {
                            name: 'OpenAI兼容',
                            url: 'http://localhost:8000',
                            type: 'openai-compatible',
                            icon: 'fa-cloud',
                            description: 'OpenAI兼容API服务',
                            endpoints: {
                                models: '/v1/models',
                                health: '/v1/models'
                            }
                        },
                        {
                            name: 'LocalAI',
                            url: 'http://localhost:8080',
                            type: 'localai',
                            icon: 'fa-microchip',
                            description: 'LocalAI服务',
                            endpoints: {
                                models: '/v1/models',
                                health: '/v1/models'
                            }
                        }
                    ];
                    this.discoveredServices = [];
                }

                async discoverServices() {
                    this.discoveredServices = [];
                    const promises = this.services.map(service => this.checkService(service));
                    await Promise.allSettled(promises);
                    return this.discoveredServices;
                }

                async checkService(service) {
                    try {
                        const controller = new AbortController();
                        const timeoutId = setTimeout(() => controller.abort(), 5000);

                        const response = await fetch(`${service.url}${service.endpoints.health}`, {
                            method: 'GET',
                            signal: controller.signal,
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });

                        clearTimeout(timeoutId);

                        if (response.ok) {
                            const models = await this.getModels(service);
                            const serviceInfo = {
                                ...service,
                                status: 'online',
                                models: models,
                                responseTime: Date.now() - performance.now(),
                                lastChecked: new Date().toISOString()
                            };
                            this.discoveredServices.push(serviceInfo);
                            return serviceInfo;
                        }
                    } catch (error) {
                        const serviceInfo = {
                            ...service,
                            status: 'offline',
                            models: [],
                            error: error.message,
                            lastChecked: new Date().toISOString()
                        };
                        this.discoveredServices.push(serviceInfo);
                        return serviceInfo;
                    }
                }

                async getModels(service) {
                    try {
                        const response = await fetch(`${service.url}${service.endpoints.models}`);
                        if (!response.ok) return [];

                        const data = await response.json();

                        if (service.type === 'ollama') {
                            return data.models?.map(model => ({
                                name: model.name,
                                size: model.size ? this.formatBytes(model.size) : 'Unknown',
                                modified: model.modified_at ? new Date(model.modified_at).toLocaleDateString() : 'Unknown',
                                type: this.detectModelType(model.name),
                                family: model.details?.family || 'Unknown'
                            })) || [];
                        } else {
                            // OpenAI兼容格式
                            return data.data?.map(model => ({
                                name: model.id,
                                size: 'Unknown',
                                modified: model.created ? new Date(model.created * 1000).toLocaleDateString() : 'Unknown',
                                type: this.detectModelType(model.id),
                                family: model.object || 'model'
                            })) || [];
                        }
                    } catch (error) {
                        console.error(`获取${service.name}模型列表失败:`, error);
                        return [];
                    }
                }

                detectModelType(modelName) {
                    const name = modelName.toLowerCase();
                    if (name.includes('vision') || name.includes('vl') || name.includes('multimodal')) {
                        return '多模态';
                    } else if (name.includes('embed') || name.includes('embedding')) {
                        return '嵌入模型';
                    } else if (name.includes('code') || name.includes('coding')) {
                        return '代码模型';
                    } else {
                        return '文本模型';
                    }
                }

                formatBytes(bytes) {
                    if (bytes === 0) return '0 Bytes';
                    const k = 1024;
                    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                }

                renderServices(container) {
                    if (this.discoveredServices.length === 0) {
                        container.innerHTML = `
                            <div class="text-center py-8 text-gray-500">
                                <i class="fa fa-exclamation-triangle text-2xl mb-2"></i>
                                <p>未发现可用的AI服务</p>
                                <p class="text-sm mt-2">请确保Ollama、LM Studio等服务正在运行</p>
                            </div>
                        `;
                        return;
                    }

                    container.innerHTML = this.discoveredServices.map(service => `
                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <i class="fa ${service.icon} text-lg mr-3 text-gray-600"></i>
                                    <div>
                                        <h4 class="font-semibold text-gray-800">${service.name}</h4>
                                        <p class="text-sm text-gray-500">${service.url}</p>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <span class="w-3 h-3 rounded-full mr-2 ${service.status === 'online' ? 'bg-green-500' : 'bg-red-500'}"></span>
                                    <span class="text-sm font-medium ${service.status === 'online' ? 'text-green-600' : 'text-red-600'}">
                                        ${service.status === 'online' ? '在线' : '离线'}
                                    </span>
                                </div>
                            </div>

                            ${service.status === 'online' ? `
                                <div class="mb-3">
                                    <p class="text-sm text-gray-600 mb-2">发现 ${service.models.length} 个模型:</p>
                                    <div class="max-h-32 overflow-y-auto space-y-1">
                                        ${service.models.map(model => `
                                            <div class="flex items-center justify-between text-xs bg-gray-50 p-2 rounded">
                                                <div class="flex-1">
                                                    <span class="font-medium">${model.name}</span>
                                                    <span class="text-gray-500 ml-2">(${model.type})</span>
                                                </div>
                                                <div class="text-gray-500">
                                                    ${model.size}
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="test-service-btn px-3 py-1 text-xs border border-gray-300 rounded text-gray-600 hover:bg-gray-50" data-service="${service.type}" data-url="${service.url}">
                                        <i class="fa fa-check mr-1"></i>测试连接
                                    </button>
                                    <button class="select-service-btn px-3 py-1 text-xs bg-primary text-white rounded hover:bg-primary/90" data-service="${service.type}" data-url="${service.url}">
                                        <i class="fa fa-arrow-right mr-1"></i>选择服务
                                    </button>
                                </div>
                            ` : `
                                <div class="text-sm text-red-600 mb-2">
                                    <i class="fa fa-exclamation-circle mr-1"></i>
                                    ${service.error || '服务不可用'}
                                </div>
                                <button class="retry-service-btn px-3 py-1 text-xs border border-gray-300 rounded text-gray-600 hover:bg-gray-50" data-service="${service.type}">
                                    <i class="fa fa-refresh mr-1"></i>重试连接
                                </button>
                            `}
                        </div>
                    `).join('');

                    // 绑定事件
                    this.bindServiceEvents(container);
                }

                bindServiceEvents(container) {
                    // 测试服务连接
                    container.querySelectorAll('.test-service-btn').forEach(btn => {
                        btn.addEventListener('click', async (e) => {
                            const serviceType = e.target.dataset.service;
                            const serviceUrl = e.target.dataset.url;
                            await this.testServiceConnection(serviceType, serviceUrl, btn);
                        });
                    });

                    // 选择服务
                    container.querySelectorAll('.select-service-btn').forEach(btn => {
                        btn.addEventListener('click', (e) => {
                            const serviceType = e.target.dataset.service;
                            const serviceUrl = e.target.dataset.url;
                            this.selectService(serviceType, serviceUrl);
                        });
                    });

                    // 重试连接
                    container.querySelectorAll('.retry-service-btn').forEach(btn => {
                        btn.addEventListener('click', async (e) => {
                            const serviceType = e.target.dataset.service;
                            const service = this.services.find(s => s.type === serviceType);
                            if (service) {
                                await this.checkService(service);
                                this.renderServices(container);
                            }
                        });
                    });
                }

                async testServiceConnection(serviceType, serviceUrl, button) {
                    const originalText = button.innerHTML;
                    button.innerHTML = '<i class="fa fa-spinner fa-spin mr-1"></i>测试中...';
                    button.disabled = true;

                    try {
                        const response = await fetch(`${serviceUrl}/api/version`);
                        if (response.ok) {
                            button.innerHTML = '<i class="fa fa-check mr-1"></i>连接成功';
                            button.className = button.className.replace('border-gray-300 text-gray-600', 'border-green-500 text-green-600');
                        } else {
                            throw new Error('连接失败');
                        }
                    } catch (error) {
                        button.innerHTML = '<i class="fa fa-times mr-1"></i>连接失败';
                        button.className = button.className.replace('border-gray-300 text-gray-600', 'border-red-500 text-red-600');
                    }

                    setTimeout(() => {
                        button.innerHTML = originalText;
                        button.className = button.className.replace(/border-(green|red)-500 text-(green|red)-600/, 'border-gray-300 text-gray-600');
                        button.disabled = false;
                    }, 2000);
                }

                selectService(serviceType, serviceUrl) {
                    // 更新API配置
                    const apiTypeMap = {
                        'ollama': 'ollama',
                        'lm-studio': 'lm-studio',
                        'openai-compatible': 'openai-compatible',
                        'localai': 'openai-compatible'
                    };

                    const apiType = apiTypeMap[serviceType] || 'custom';

                    // 更新配置
                    apiConfig.config.apiType = apiType;
                    apiConfig.config.baseURL = serviceUrl;
                    apiConfig.save();
                    apiConfig.updateUI();

                    // 切换到LLM配置标签页
                    document.querySelector('[data-tab="llm"]').click();

                    showToast(`已选择 ${serviceType} 服务: ${serviceUrl}`, 'success');
                }
            }

            // API配置管理
            class APIConfig {
                constructor() {
                    this.loadConfig();
                }

                loadConfig() {
                    const saved = localStorage.getItem('llm-chat-config');
                    const defaults = {
                        apiType: 'ollama',
                        baseURL: 'http://localhost:11434',
                        apiKey: '',
                        model: 'llama3.2',
                        temperature: 0.7,
                        maxTokens: 1000,
                        useGPU: true,
                        streamOutput: true,
                        systemPrompt: '你是一个有用的AI助手，请用中文回答问题。'
                    };

                    this.config = saved ? { ...defaults, ...JSON.parse(saved) } : defaults;
                    this.updateUI();
                }

                saveConfig() {
                    localStorage.setItem('llm-chat-config', JSON.stringify(this.config));
                }

                updateUI() {
                    document.getElementById('temperature').value = this.config.temperature;
                    document.getElementById('temp-value').textContent = this.config.temperature;
                    document.getElementById('max-length').value = this.config.maxTokens;
                    document.getElementById('length-value').textContent = this.config.maxTokens;
                    document.getElementById('use-gpu').checked = this.config.useGPU;
                    document.getElementById('stream-output').checked = this.config.streamOutput;
                    document.getElementById('api-type').value = this.config.apiType;
                    document.getElementById('api-url').value = this.config.baseURL;
                    document.getElementById('api-key').value = this.config.apiKey;
                    document.getElementById('model-name').value = this.config.model;
                    document.getElementById('system-prompt').value = this.config.systemPrompt;
                }

                updateFromUI() {
                    this.config.temperature = parseFloat(document.getElementById('temperature').value);
                    this.config.maxTokens = parseInt(document.getElementById('max-length').value);
                    this.config.useGPU = document.getElementById('use-gpu').checked;
                    this.config.streamOutput = document.getElementById('stream-output').checked;
                    this.config.apiType = document.getElementById('api-type').value;
                    this.config.baseURL = document.getElementById('api-url').value;
                    this.config.apiKey = document.getElementById('api-key').value;
                    this.config.model = document.getElementById('model-name').value;
                    this.config.systemPrompt = document.getElementById('system-prompt').value;
                }

                async getOllamaModels() {
                    try {
                        const response = await fetch(`${this.config.baseURL}/api/tags`);
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        const data = await response.json();
                        return data.models || [];
                    } catch (error) {
                        console.error('获取Ollama模型列表失败:', error);
                        throw error;
                    }
                }

                async checkOllamaConnection() {
                    try {
                        const response = await fetch(`${this.config.baseURL}/api/version`);
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        const data = await response.json();
                        return { connected: true, version: data.version };
                    } catch (error) {
                        return { connected: false, error: error.message };
                    }
                }
            }

            const apiConfig = new APIConfig();
            const serviceDiscovery = new ServiceDiscovery();

            // 动态岛管理类
            class DynamicIsland {
                constructor() {
                    this.island = document.getElementById('dynamic-island');
                    this.indicator = document.getElementById('island-indicator');
                    this.modelName = document.getElementById('island-model-name');
                    this.toggle = document.getElementById('island-toggle');
                    this.serviceType = document.getElementById('island-service-type');
                    this.serviceUrl = document.getElementById('island-service-url');
                    this.responseTime = document.getElementById('island-response-time');
                    this.modelType = document.getElementById('island-model-type');

                    this.isExpanded = false;
                    this.isVisible = true;
                    this.lastScrollY = 0;

                    this.init();
                }

                init() {
                    // 点击切换展开/收缩
                    this.island.addEventListener('click', () => {
                        this.toggle();
                    });

                    // 滚动时的行为
                    let scrollTimeout;
                    window.addEventListener('scroll', () => {
                        clearTimeout(scrollTimeout);

                        const currentScrollY = window.scrollY;

                        // 滚动时收缩
                        if (currentScrollY > 100 && !this.island.classList.contains('compact')) {
                            this.collapse();
                        }

                        // 顶部时展开
                        if (currentScrollY < 50 && this.island.classList.contains('compact')) {
                            this.expand();
                        }

                        this.lastScrollY = currentScrollY;

                        // 滚动停止后的处理
                        scrollTimeout = setTimeout(() => {
                            if (currentScrollY > 200) {
                                this.hide();
                            } else {
                                this.show();
                            }
                        }, 150);
                    });

                    // 初始状态更新
                    this.updateStatus('offline', '未连接', {});
                }

                toggle() {
                    if (this.isExpanded) {
                        this.collapse();
                    } else {
                        this.expand();
                    }
                }

                expand() {
                    this.island.classList.remove('compact');
                    this.island.classList.add('expanded');
                    this.toggle.querySelector('i').className = 'fa fa-chevron-up';
                    this.isExpanded = true;
                }

                collapse() {
                    this.island.classList.remove('expanded');
                    this.island.classList.add('compact');
                    this.toggle.querySelector('i').className = 'fa fa-chevron-down';
                    this.isExpanded = false;
                }

                show() {
                    if (!this.isVisible) {
                        this.island.style.transform = 'translateX(-50%) translateY(0)';
                        this.island.style.opacity = '1';
                        this.isVisible = true;
                    }
                }

                hide() {
                    if (this.isVisible) {
                        this.island.style.transform = 'translateX(-50%) translateY(-60px)';
                        this.island.style.opacity = '0';
                        this.isVisible = false;
                    }
                }

                updateStatus(status, modelName, details = {}) {
                    // 更新指示器
                    this.indicator.className = `dynamic-island-indicator ${status}`;

                    // 更新模型名称
                    this.modelName.textContent = modelName || '未连接';

                    // 更新详细信息
                    this.serviceType.textContent = details.serviceType || '-';
                    this.serviceUrl.textContent = details.serviceUrl || '-';
                    this.responseTime.textContent = details.responseTime || '-';
                    this.modelType.textContent = details.modelType || '-';

                    // 根据状态自动展开/收缩
                    if (status === 'loading') {
                        this.expand();
                    }
                }

                // 更新连接状态
                updateConnection(connected, modelName, config) {
                    if (connected) {
                        const details = {
                            serviceType: this.getServiceTypeName(config.apiType),
                            serviceUrl: config.baseURL,
                            responseTime: '< 100ms',
                            modelType: this.getModelType(modelName)
                        };
                        this.updateStatus('online', modelName, details);
                    } else {
                        this.updateStatus('offline', '连接失败', {});
                    }
                }

                // 更新加载状态
                updateLoading(modelName) {
                    this.updateStatus('loading', `加载中: ${modelName}`, {
                        serviceType: '正在连接...',
                        serviceUrl: '检测中...',
                        responseTime: '测试中...',
                        modelType: '识别中...'
                    });
                }

                getServiceTypeName(apiType) {
                    const typeMap = {
                        'ollama': 'Ollama',
                        'lm-studio': 'LM Studio',
                        'openai-compatible': 'OpenAI兼容',
                        'custom': '自定义API'
                    };
                    return typeMap[apiType] || apiType;
                }

                getModelType(modelName) {
                    if (!modelName) return '-';
                    const name = modelName.toLowerCase();
                    if (name.includes('vision') || name.includes('vl')) return '多模态';
                    if (name.includes('embed')) return '嵌入模型';
                    if (name.includes('code')) return '代码模型';
                    return '文本模型';
                }
            }

            const dynamicIsland = new DynamicIsland();

            // 增强的性能监控类
            class PerformanceMonitor {
                constructor() {
                    // 基础统计
                    this.conversationCount = 0;
                    this.lastResponseTime = 0;
                    this.startTime = 0;
                    this.isConnected = false;
                    this.currentModel = '';
                    this.currentMode = 'text';

                    // 新增：对话统计信息
                    this.userCharCount = 0;        // 用户输入字符数
                    this.aiCharCount = 0;          // AI输出字符数
                    this.sessionStartTime = Date.now(); // 会话开始时间
                    this.firstMessageTime = null;   // 首次对话时间

                    // 新增：性能指标
                    this.firstByteTime = 0;        // 首字节响应时间
                    this.streamingSpeed = 0;       // 流式输出速度
                    this.currentStreamStart = 0;   // 当前流式开始时间
                    this.currentStreamChars = 0;   // 当前流式字符数
                    this.networkLatency = 0;       // 网络延迟

                    // 新增：技术状态
                    this.connectionStatus = 'disconnected'; // disconnected/connecting/connected/reconnecting/failed
                    this.modelLoadStatus = 'unknown';       // unknown/loading/loaded/failed
                    this.serverMemory = 0;                  // 服务器内存使用

                    // 定时器
                    this.sessionTimer = null;
                    this.streamingTimer = null;
                    this.latencyTimer = null;

                    this.initializeTimers();
                }

                initializeTimers() {
                    // 会话时间更新定时器
                    this.sessionTimer = setInterval(() => {
                        this.updateSessionDurationDisplay();
                    }, 1000);

                    // 网络延迟检测定时器
                    this.latencyTimer = setInterval(() => {
                        this.checkNetworkLatency();
                    }, 10000); // 每10秒检测一次
                }

                // 开始计时（发送消息时调用）
                startTiming() {
                    this.startTime = Date.now();
                    this.firstByteTime = 0;
                    this.currentStreamStart = 0;
                    this.currentStreamChars = 0;
                    this.streamingSpeed = 0;

                    if (!this.firstMessageTime) {
                        this.firstMessageTime = Date.now();
                    }
                }

                // 记录首字节响应时间
                recordFirstByte() {
                    if (this.startTime > 0 && this.firstByteTime === 0) {
                        this.firstByteTime = Date.now() - this.startTime;
                        this.updateFirstByteTimeDisplay();

                        // 开始流式输出监控
                        this.currentStreamStart = Date.now();
                        this.currentStreamChars = 0;
                        this.startStreamingMonitor();
                    }
                }

                // 开始流式输出监控
                startStreamingMonitor() {
                    if (this.streamingTimer) {
                        clearInterval(this.streamingTimer);
                    }

                    this.streamingTimer = setInterval(() => {
                        this.updateStreamingSpeed();
                    }, 500); // 每500ms更新一次流式速度
                }

                // 停止流式输出监控
                stopStreamingMonitor() {
                    if (this.streamingTimer) {
                        clearInterval(this.streamingTimer);
                        this.streamingTimer = null;
                    }
                }

                // 更新流式输出字符数
                updateStreamingChars(newChars) {
                    this.currentStreamChars += newChars;
                    this.aiCharCount += newChars;
                    this.updateCharCountDisplays();
                }

                // 计算并更新流式输出速度
                updateStreamingSpeed() {
                    if (this.currentStreamStart > 0 && this.currentStreamChars > 0) {
                        const elapsed = (Date.now() - this.currentStreamStart) / 1000;
                        this.streamingSpeed = Math.round(this.currentStreamChars / elapsed);
                        this.updateStreamingSpeedDisplay();
                    }
                }

                // 结束计时（响应完成时调用）
                endTiming() {
                    if (this.startTime > 0) {
                        this.lastResponseTime = Date.now() - this.startTime;
                        this.updateResponseTimeDisplay();
                        this.startTime = 0;
                        this.stopStreamingMonitor();
                    }
                }

                // 增加对话轮数
                incrementConversation() {
                    this.conversationCount++;
                    this.updateConversationCountDisplay();
                    this.updateAverageCharsDisplay();
                }

                // 添加用户输入字符数
                addUserChars(count) {
                    this.userCharCount += count;
                    this.updateCharCountDisplays();
                    this.updateAverageCharsDisplay();
                }

                // 设置连接状态
                setConnectionStatus(status, modelName = '') {
                    this.connectionStatus = status;
                    this.currentModel = modelName;
                    this.isConnected = (status === 'connected');
                    this.updateConnectionStatusDisplay();
                }

                // 设置模型加载状态
                setModelLoadStatus(status) {
                    this.modelLoadStatus = status;
                    this.updateModelLoadStatusDisplay();
                }

                // 设置当前模式
                setCurrentMode(mode) {
                    this.currentMode = mode;
                    this.updateCurrentModeDisplay();
                }

                // 网络延迟检测
                async checkNetworkLatency() {
                    if (!this.isConnected) return;

                    try {
                        const startTime = Date.now();
                        const response = await fetch(apiConfig.config.baseURL + '/models', {
                            method: 'GET',
                            headers: { 'Content-Type': 'application/json' }
                        });

                        if (response.ok) {
                            this.networkLatency = Date.now() - startTime;
                            this.updateNetworkLatencyDisplay();
                        }
                    } catch (error) {
                        this.networkLatency = -1; // 表示检测失败
                        this.updateNetworkLatencyDisplay();
                    }
                }

                // 重置所有统计数据
                reset() {
                    this.conversationCount = 0;
                    this.lastResponseTime = 0;
                    this.userCharCount = 0;
                    this.aiCharCount = 0;
                    this.sessionStartTime = Date.now();
                    this.firstMessageTime = null;
                    this.firstByteTime = 0;
                    this.streamingSpeed = 0;
                    this.currentStreamStart = 0;
                    this.currentStreamChars = 0;

                    this.updateAllDisplays();
                }

                // 更新所有显示
                updateAllDisplays() {
                    this.updateResponseTimeDisplay();
                    this.updateConversationCountDisplay();
                    this.updateCharCountDisplays();
                    this.updateSessionDurationDisplay();
                    this.updateAverageCharsDisplay();
                    this.updateFirstByteTimeDisplay();
                    this.updateStreamingSpeedDisplay();
                    this.updateConnectionStatusDisplay();
                    this.updateModelLoadStatusDisplay();
                    this.updateCurrentModeDisplay();
                    this.updateNetworkLatencyDisplay();
                }

                // === 显示更新方法 ===

                updateResponseTimeDisplay() {
                    const display = document.getElementById('response-time-display');
                    if (display) {
                        if (this.lastResponseTime > 0) {
                            display.textContent = `${(this.lastResponseTime / 1000).toFixed(1)}s`;
                            display.className = 'font-semibold text-green-600';
                        } else {
                            display.textContent = '--';
                            display.className = 'font-semibold text-gray-500';
                        }
                    }
                }

                updateConversationCountDisplay() {
                    const display = document.getElementById('conversation-count-display');
                    if (display) {
                        display.textContent = this.conversationCount.toString();
                        display.className = 'font-semibold text-blue-600';
                    }
                }

                updateCharCountDisplays() {
                    // 用户字符数
                    const userDisplay = document.getElementById('user-char-count-display');
                    if (userDisplay) {
                        userDisplay.textContent = this.userCharCount.toLocaleString();
                        userDisplay.className = 'font-semibold text-blue-600';
                    }

                    // AI字符数
                    const aiDisplay = document.getElementById('ai-char-count-display');
                    if (aiDisplay) {
                        aiDisplay.textContent = this.aiCharCount.toLocaleString();
                        aiDisplay.className = 'font-semibold text-green-600';
                    }

                    // 总字符数
                    const totalDisplay = document.getElementById('total-char-count-display');
                    if (totalDisplay) {
                        const total = this.userCharCount + this.aiCharCount;
                        totalDisplay.textContent = total.toLocaleString();
                        totalDisplay.className = 'font-semibold text-purple-600';
                    }
                }

                updateSessionDurationDisplay() {
                    const display = document.getElementById('session-duration-display');
                    if (display) {
                        const startTime = this.firstMessageTime || this.sessionStartTime;
                        const duration = Math.floor((Date.now() - startTime) / 1000);

                        const hours = Math.floor(duration / 3600);
                        const minutes = Math.floor((duration % 3600) / 60);
                        const seconds = duration % 60;

                        const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                        display.textContent = timeStr;
                        display.className = 'font-semibold text-indigo-600';
                    }
                }

                updateAverageCharsDisplay() {
                    const display = document.getElementById('average-chars-display');
                    if (display) {
                        if (this.conversationCount > 0) {
                            const total = this.userCharCount + this.aiCharCount;
                            const average = Math.round(total / this.conversationCount);
                            display.textContent = average.toLocaleString();
                            display.className = 'font-semibold text-orange-600';
                        } else {
                            display.textContent = '--';
                            display.className = 'font-semibold text-gray-500';
                        }
                    }
                }

                updateFirstByteTimeDisplay() {
                    const display = document.getElementById('first-byte-time-display');
                    if (display) {
                        if (this.firstByteTime > 0) {
                            display.textContent = `${this.firstByteTime}ms`;

                            // 根据延迟设置颜色
                            if (this.firstByteTime < 500) {
                                display.className = 'font-semibold text-green-600';
                            } else if (this.firstByteTime < 1000) {
                                display.className = 'font-semibold text-yellow-600';
                            } else {
                                display.className = 'font-semibold text-red-600';
                            }
                        } else {
                            display.textContent = '--';
                            display.className = 'font-semibold text-gray-500';
                        }
                    }
                }

                updateStreamingSpeedDisplay() {
                    const display = document.getElementById('streaming-speed-display');
                    if (display) {
                        if (this.streamingSpeed > 0) {
                            display.textContent = `${this.streamingSpeed} 字符/秒`;

                            // 根据速度设置颜色
                            if (this.streamingSpeed > 50) {
                                display.className = 'font-semibold text-green-600';
                            } else if (this.streamingSpeed > 20) {
                                display.className = 'font-semibold text-yellow-600';
                            } else {
                                display.className = 'font-semibold text-red-600';
                            }
                        } else {
                            display.textContent = '--';
                            display.className = 'font-semibold text-gray-500';
                        }
                    }
                }

                updateConnectionStatusDisplay() {
                    const display = document.getElementById('connection-status-display');
                    if (display) {
                        const statusMap = {
                            'connected': { text: '已连接', class: 'font-semibold text-green-600' },
                            'connecting': { text: '连接中', class: 'font-semibold text-yellow-600' },
                            'reconnecting': { text: '重连中', class: 'font-semibold text-orange-600' },
                            'failed': { text: '连接失败', class: 'font-semibold text-red-600' },
                            'disconnected': { text: '未连接', class: 'font-semibold text-gray-600' }
                        };

                        const status = statusMap[this.connectionStatus] || statusMap['disconnected'];
                        display.textContent = status.text;
                        display.className = status.class;
                    }
                }

                updateModelLoadStatusDisplay() {
                    const display = document.getElementById('model-load-status-display');
                    if (display) {
                        const statusMap = {
                            'loaded': { text: '已加载', class: 'font-semibold text-green-600' },
                            'loading': { text: '加载中', class: 'font-semibold text-yellow-600' },
                            'failed': { text: '加载失败', class: 'font-semibold text-red-600' },
                            'unknown': { text: '未知', class: 'font-semibold text-gray-600' }
                        };

                        const status = statusMap[this.modelLoadStatus] || statusMap['unknown'];
                        display.textContent = status.text;
                        display.className = status.class;
                    }
                }

                updateCurrentModeDisplay() {
                    const display = document.getElementById('current-mode-display');
                    if (display) {
                        const modeNames = {
                            'text': '文本',
                            'image': '图像',
                            'multimodal': '多模态'
                        };
                        display.textContent = modeNames[this.currentMode] || '文本';

                        // 根据模式设置颜色
                        const colorClasses = {
                            'text': 'font-semibold text-blue-600',
                            'image': 'font-semibold text-purple-600',
                            'multimodal': 'font-semibold text-indigo-600'
                        };
                        display.className = colorClasses[this.currentMode] || 'font-semibold text-gray-600';
                    }
                }

                updateNetworkLatencyDisplay() {
                    const display = document.getElementById('network-latency-display');
                    if (display) {
                        if (this.networkLatency > 0) {
                            display.textContent = `${this.networkLatency}ms`;

                            // 根据延迟设置颜色
                            if (this.networkLatency < 100) {
                                display.className = 'font-semibold text-green-600';
                            } else if (this.networkLatency < 300) {
                                display.className = 'font-semibold text-yellow-600';
                            } else {
                                display.className = 'font-semibold text-red-600';
                            }
                        } else if (this.networkLatency === -1) {
                            display.textContent = '检测失败';
                            display.className = 'font-semibold text-red-600';
                        } else {
                            display.textContent = '--';
                            display.className = 'font-semibold text-gray-500';
                        }
                    }
                }

                // 清理定时器
                destroy() {
                    if (this.sessionTimer) {
                        clearInterval(this.sessionTimer);
                    }
                    if (this.streamingTimer) {
                        clearInterval(this.streamingTimer);
                    }
                    if (this.latencyTimer) {
                        clearInterval(this.latencyTimer);
                    }
                }

                // 系统监控增强
                getSystemInfo() {
                    return {
                        platform: navigator.platform,
                        userAgent: navigator.userAgent,
                        language: navigator.language,
                        cookieEnabled: navigator.cookieEnabled,
                        onLine: navigator.onLine,
                        hardwareConcurrency: navigator.hardwareConcurrency || 'Unknown',
                        deviceMemory: navigator.deviceMemory || 'Unknown',
                        connection: navigator.connection ? {
                            effectiveType: navigator.connection.effectiveType,
                            downlink: navigator.connection.downlink,
                            rtt: navigator.connection.rtt
                        } : null,
                        screen: {
                            width: screen.width,
                            height: screen.height,
                            colorDepth: screen.colorDepth,
                            pixelDepth: screen.pixelDepth
                        },
                        viewport: {
                            width: window.innerWidth,
                            height: window.innerHeight
                        },
                        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                        webgl: this.getWebGLInfo(),
                        storage: this.getStorageInfo()
                    };
                }

                // 获取WebGL信息
                getWebGLInfo() {
                    try {
                        const canvas = document.createElement('canvas');
                        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                        if (!gl) return { supported: false };

                        const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
                        return {
                            supported: true,
                            vendor: debugInfo ? gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL) : 'Unknown',
                            renderer: debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : 'Unknown',
                            version: gl.getParameter(gl.VERSION),
                            shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION)
                        };
                    } catch (e) {
                        return { supported: false, error: e.message };
                    }
                }

                // 获取存储信息
                getStorageInfo() {
                    const storage = {};
                    try {
                        if ('storage' in navigator && 'estimate' in navigator.storage) {
                            navigator.storage.estimate().then(estimate => {
                                storage.quota = estimate.quota;
                                storage.usage = estimate.usage;
                                storage.available = estimate.quota - estimate.usage;
                            });
                        }
                        storage.localStorage = typeof(Storage) !== "undefined";
                        storage.sessionStorage = typeof(Storage) !== "undefined";
                        storage.indexedDB = 'indexedDB' in window;
                    } catch (e) {
                        storage.error = e.message;
                    }
                    return storage;
                }

                // 获取AI服务状态
                async getAIServicesStatus() {
                    const services = [
                        { name: 'Ollama', url: 'http://localhost:11434', endpoint: '/api/version' },
                        { name: 'LM Studio', url: 'http://localhost:1234', endpoint: '/v1/models' },
                        { name: 'Text Generation WebUI', url: 'http://localhost:7860', endpoint: '/api/v1/models' },
                        { name: 'LocalAI', url: 'http://localhost:8080', endpoint: '/v1/models' }
                    ];

                    const results = await Promise.allSettled(
                        services.map(async service => {
                            const startTime = Date.now();
                            try {
                                const controller = new AbortController();
                                const timeoutId = setTimeout(() => controller.abort(), 3000);

                                const response = await fetch(service.url + service.endpoint, {
                                    signal: controller.signal,
                                    method: 'GET'
                                });

                                clearTimeout(timeoutId);

                                return {
                                    name: service.name,
                                    url: service.url,
                                    status: response.ok ? 'online' : 'error',
                                    responseTime: Date.now() - startTime
                                };
                            } catch (error) {
                                return {
                                    name: service.name,
                                    url: service.url,
                                    status: 'offline',
                                    error: error.message
                                };
                            }
                        })
                    );

                    return results.map(result => result.status === 'fulfilled' ? result.value : result.reason);
                }

                // 获取性能指标
                getPerformanceMetrics() {
                    const perf = performance;
                    const navigation = perf.getEntriesByType('navigation')[0];

                    return {
                        loadTime: navigation ? Math.round(navigation.loadEventEnd - navigation.fetchStart) : 0,
                        domContentLoaded: navigation ? Math.round(navigation.domContentLoadedEventEnd - navigation.fetchStart) : 0,
                        firstPaint: this.getFirstPaint(),
                        memoryUsage: this.getMemoryUsage(),
                        timing: {
                            dns: navigation ? Math.round(navigation.domainLookupEnd - navigation.domainLookupStart) : 0,
                            tcp: navigation ? Math.round(navigation.connectEnd - navigation.connectStart) : 0,
                            request: navigation ? Math.round(navigation.responseStart - navigation.requestStart) : 0,
                            response: navigation ? Math.round(navigation.responseEnd - navigation.responseStart) : 0
                        }
                    };
                }

                // 获取首次绘制时间
                getFirstPaint() {
                    try {
                        const paintEntries = performance.getEntriesByType('paint');
                        const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
                        const firstContentfulPaint = paintEntries.find(entry => entry.name === 'first-contentful-paint');

                        return {
                            firstPaint: firstPaint ? Math.round(firstPaint.startTime) : 0,
                            firstContentfulPaint: firstContentfulPaint ? Math.round(firstContentfulPaint.startTime) : 0
                        };
                    } catch (e) {
                        return { error: e.message };
                    }
                }

                // 获取内存使用情况
                getMemoryUsage() {
                    try {
                        if ('memory' in performance) {
                            const memory = performance.memory;
                            return {
                                used: Math.round(memory.usedJSHeapSize / 1024 / 1024 * 100) / 100,
                                total: Math.round(memory.totalJSHeapSize / 1024 / 1024 * 100) / 100,
                                limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024 * 100) / 100
                            };
                        }
                        return { supported: false };
                    } catch (e) {
                        return { error: e.message };
                    }
                }

                // 更新系统信息显示
                updateSystemInfoDisplays() {
                    const systemInfo = this.getSystemInfo();

                    // CPU核心数
                    const cpuDisplay = document.getElementById('cpu-cores-display');
                    if (cpuDisplay) {
                        cpuDisplay.textContent = systemInfo.hardwareConcurrency + '核';
                    }

                    // 内存使用
                    const memoryDisplay = document.getElementById('memory-usage-display');
                    if (memoryDisplay) {
                        const memory = systemInfo.memory || this.getMemoryUsage();
                        if (memory.used) {
                            memoryDisplay.textContent = `${memory.used}MB`;
                        } else {
                            memoryDisplay.textContent = '不支持';
                        }
                    }

                    // 网络类型
                    const networkDisplay = document.getElementById('network-type-display');
                    if (networkDisplay) {
                        if (systemInfo.connection) {
                            networkDisplay.textContent = systemInfo.connection.effectiveType || '未知';
                        } else {
                            networkDisplay.textContent = '未知';
                        }
                    }
                }

                // 更新AI服务状态显示
                async updateAIServicesDisplays() {
                    const services = await this.getAIServicesStatus();

                    const displayMap = {
                        'Ollama': 'ollama-status-display',
                        'LM Studio': 'lm-studio-status-display',
                        'Text Generation WebUI': 'webui-status-display',
                        'LocalAI': 'localai-status-display'
                    };

                    services.forEach(service => {
                        const displayId = displayMap[service.name];
                        const display = document.getElementById(displayId);
                        if (display) {
                            const statusMap = {
                                'online': { text: '在线', class: 'font-semibold text-green-600' },
                                'offline': { text: '离线', class: 'font-semibold text-red-600' },
                                'error': { text: '错误', class: 'font-semibold text-orange-600' }
                            };

                            const status = statusMap[service.status] || statusMap['offline'];
                            display.textContent = status.text;
                            display.className = status.class + ' text-sm';
                        }
                    });
                }

                // 更新详细统计弹窗中的系统信息
                updateDetailedSystemInfo() {
                    const systemInfo = this.getSystemInfo();
                    const performanceMetrics = this.getPerformanceMetrics();

                    // 操作系统
                    const platformDisplay = document.getElementById('system-platform-display');
                    if (platformDisplay) {
                        platformDisplay.textContent = systemInfo.platform || '未知';
                    }

                    // 浏览器信息
                    const browserDisplay = document.getElementById('browser-info-display');
                    if (browserDisplay) {
                        const userAgent = systemInfo.userAgent;
                        let browserName = '未知';
                        if (userAgent.includes('Chrome')) browserName = 'Chrome';
                        else if (userAgent.includes('Firefox')) browserName = 'Firefox';
                        else if (userAgent.includes('Safari')) browserName = 'Safari';
                        else if (userAgent.includes('Edge')) browserName = 'Edge';
                        browserDisplay.textContent = browserName;
                    }

                    // 屏幕分辨率
                    const screenDisplay = document.getElementById('screen-info-display');
                    if (screenDisplay) {
                        screenDisplay.textContent = `${systemInfo.screen.width}×${systemInfo.screen.height}`;
                    }

                    // 时区
                    const timezoneDisplay = document.getElementById('timezone-display');
                    if (timezoneDisplay) {
                        timezoneDisplay.textContent = systemInfo.timezone || '未知';
                    }

                    // 页面加载时间
                    const loadTimeDisplay = document.getElementById('page-load-time-display');
                    if (loadTimeDisplay) {
                        loadTimeDisplay.textContent = performanceMetrics.loadTime ? `${performanceMetrics.loadTime}ms` : '--';
                    }

                    // 首次绘制
                    const firstPaintDisplay = document.getElementById('first-paint-display');
                    if (firstPaintDisplay) {
                        const fp = performanceMetrics.firstPaint;
                        if (fp && fp.firstContentfulPaint) {
                            firstPaintDisplay.textContent = `${fp.firstContentfulPaint}ms`;
                        } else {
                            firstPaintDisplay.textContent = '--';
                        }
                    }

                    // WebGL支持
                    const webglDisplay = document.getElementById('webgl-support-display');
                    if (webglDisplay) {
                        webglDisplay.textContent = systemInfo.webgl.supported ? '支持' : '不支持';
                    }

                    // 存储配额
                    const storageDisplay = document.getElementById('storage-quota-display');
                    if (storageDisplay) {
                        const storage = systemInfo.storage;
                        if (storage.quota) {
                            const quotaGB = Math.round(storage.quota / 1024 / 1024 / 1024 * 100) / 100;
                            storageDisplay.textContent = `${quotaGB}GB`;
                        } else {
                            storageDisplay.textContent = '未知';
                        }
                    }
                }

                // 初始化系统监控
                initSystemMonitoring() {
                    // 立即更新一次
                    this.updateSystemInfoDisplays();
                    this.updateAIServicesDisplays();
                    this.updateDetailedSystemInfo();

                    // 定期更新AI服务状态（每30秒）
                    setInterval(() => {
                        this.updateAIServicesDisplays();
                    }, 30000);

                    // 定期更新系统信息（每5秒）
                    setInterval(() => {
                        this.updateSystemInfoDisplays();
                        this.updateDetailedSystemInfo();
                    }, 5000);
                }
            }

            const performanceMonitor = new PerformanceMonitor();

            // 模型兼容性检查
            class ModelCompatibilityChecker {
                constructor() {
                    this.incompatiblePatterns = [
                        /embedding/i,
                        /embed/i,
                        /retrieval/i,
                        /rerank/i,
                        /classifier/i
                    ];

                    this.conversationalPatterns = [
                        /chat/i,
                        /instruct/i,
                        /conversation/i,
                        /llama/i,
                        /qwen/i,
                        /mistral/i,
                        /deepseek/i,
                        /yi/i,
                        /baichuan/i
                    ];
                }

                isConversationalModel(modelName) {
                    if (!modelName) return false;

                    // 检查是否为不兼容的模型类型
                    for (const pattern of this.incompatiblePatterns) {
                        if (pattern.test(modelName)) {
                            return false;
                        }
                    }

                    // 检查是否为已知的对话模型
                    for (const pattern of this.conversationalPatterns) {
                        if (pattern.test(modelName)) {
                            return true;
                        }
                    }

                    // 默认假设是对话模型（保守策略）
                    return true;
                }

                getModelWarning(modelName) {
                    if (!modelName) return null;

                    for (const pattern of this.incompatiblePatterns) {
                        if (pattern.test(modelName)) {
                            if (/embedding|embed/i.test(modelName)) {
                                return {
                                    type: 'error',
                                    message: '⚠️ 检测到嵌入模型：此模型用于生成文本向量，不支持对话功能。请选择对话模型（如llama、qwen、mistral等）。'
                                };
                            } else if (/retrieval/i.test(modelName)) {
                                return {
                                    type: 'error',
                                    message: '⚠️ 检测到检索模型：此模型用于信息检索，不支持对话功能。请选择对话模型。'
                                };
                            } else {
                                return {
                                    type: 'warning',
                                    message: '⚠️ 此模型可能不适合对话任务，建议选择专门的对话模型。'
                                };
                            }
                        }
                    }

                    return null;
                }

                showModelWarning(warning) {
                    if (!warning) return;

                    // 创建警告提示
                    const warningDiv = document.createElement('div');
                    warningDiv.className = `p-3 rounded-lg mb-4 ${warning.type === 'error' ? 'bg-red-50 border border-red-200' : 'bg-yellow-50 border border-yellow-200'}`;
                    warningDiv.innerHTML = `
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <i class="fa fa-exclamation-triangle ${warning.type === 'error' ? 'text-red-500' : 'text-yellow-500'}"></i>
                            </div>
                            <div class="ml-3">
                                <p class="${warning.type === 'error' ? 'text-red-700' : 'text-yellow-700'} text-sm">${warning.message}</p>
                            </div>
                            <button class="ml-auto flex-shrink-0 text-gray-400 hover:text-gray-600" onclick="this.parentElement.parentElement.remove()">
                                <i class="fa fa-times"></i>
                            </button>
                        </div>
                    `;

                    // 插入到聊天容器前
                    const chatContainer = document.getElementById('chat-container');
                    if (chatContainer && chatContainer.parentNode) {
                        chatContainer.parentNode.insertBefore(warningDiv, chatContainer);
                    }
                }

                clearWarnings() {
                    // 移除所有警告
                    const warnings = document.querySelectorAll('.bg-red-50, .bg-yellow-50');
                    warnings.forEach(warning => {
                        if (warning.querySelector('.fa-exclamation-triangle')) {
                            warning.remove();
                        }
                    });
                }
            }

            const modelChecker = new ModelCompatibilityChecker();

            // 消息增强处理类
            class MessageEnhancer {
                constructor() {
                    this.messageCounter = 0;
                    this.userSettings = {
                        showThinking: true,
                        showTimestamp: true,
                        enableSyntaxHighlight: true,
                        enableMathRendering: true
                    };
                }

                // 处理思考过程标签
                processThinkingTags(content) {
                    const thinkingRegex = /<think>([\s\S]*?)<\/think>/gi;
                    let processedContent = content;
                    const thinkingSections = [];

                    let match;
                    while ((match = thinkingRegex.exec(content)) !== null) {
                        const thinkingContent = match[1].trim();
                        const thinkingId = `thinking-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

                        const thinkingHtml = `
                            <div class="thinking-section" id="${thinkingId}">
                                <div class="thinking-toggle" onclick="toggleThinking('${thinkingId}')">
                                    <i class="fa fa-brain"></i>
                                    <span class="toggle-text">展开思考过程</span>
                                    <i class="fa fa-chevron-down toggle-icon"></i>
                                </div>
                                <div class="thinking-content thinking-collapsed">
                                    ${this.escapeHtml(thinkingContent)}
                                </div>
                            </div>
                        `;

                        thinkingSections.push({
                            original: match[0],
                            replacement: thinkingHtml
                        });
                    }

                    // 替换所有思考过程标签
                    thinkingSections.forEach(section => {
                        processedContent = processedContent.replace(section.original, section.replacement);
                    });

                    return processedContent;
                }

                // 处理代码块
                processCodeBlocks(content) {
                    // 处理代码块 ```language\ncode\n```
                    const codeBlockRegex = /```(\w+)?\n?([\s\S]*?)```/g;
                    return content.replace(codeBlockRegex, (match, language, code) => {
                        const lang = language || 'text';
                        const codeId = `code-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

                        return `
                            <div class="code-block">
                                <div class="code-header">
                                    <span>${lang}</span>
                                    <button class="copy-code-btn" onclick="copyCode('${codeId}')">
                                        <i class="fa fa-copy"></i> 复制
                                    </button>
                                </div>
                                <pre id="${codeId}"><code>${this.escapeHtml(code.trim())}</code></pre>
                            </div>
                        `;
                    });
                }

                // 处理行内代码
                processInlineCode(content) {
                    return content.replace(/`([^`]+)`/g, '<code class="bg-gray-100 px-1 rounded text-sm">$1</code>');
                }

                // 处理数学公式
                processMathFormulas(content) {
                    if (!this.userSettings.enableMathRendering) return content;

                    // 处理块级数学公式 $$...$$
                    content = content.replace(/\$\$([\s\S]*?)\$\$/g, '<div class="math-block">$1</div>');

                    // 处理行内数学公式 $...$
                    content = content.replace(/\$([^$]+)\$/g, '<span class="math-inline">$1</span>');

                    return content;
                }

                // 转义HTML
                escapeHtml(text) {
                    const div = document.createElement('div');
                    div.textContent = text;
                    return div.innerHTML;
                }

                // 增强消息内容
                enhanceMessage(content, sender = 'bot') {
                    if (sender === 'user') return content;

                    let enhanced = content;

                    // 处理思考过程
                    if (this.userSettings.showThinking) {
                        enhanced = this.processThinkingTags(enhanced);
                    }

                    // 处理代码块
                    if (this.userSettings.enableSyntaxHighlight) {
                        enhanced = this.processCodeBlocks(enhanced);
                        enhanced = this.processInlineCode(enhanced);
                    }

                    // 处理数学公式
                    enhanced = this.processMathFormulas(enhanced);

                    return enhanced;
                }

                // 生成消息操作按钮
                generateMessageActions(messageId, sender) {
                    const timestamp = new Date().toLocaleString('zh-CN', {
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });

                    let actions = '';

                    if (this.userSettings.showTimestamp) {
                        actions += `<span class="message-timestamp">${timestamp}</span>`;
                    }

                    actions += `
                        <div class="message-actions">
                            <button class="action-btn" onclick="copyMessage('${messageId}')" title="复制消息">
                                <i class="fa fa-copy"></i>
                            </button>
                    `;

                    if (sender === 'bot') {
                        actions += `
                            <button class="action-btn" onclick="regenerateMessage('${messageId}')" title="重新生成">
                                <i class="fa fa-redo"></i>
                            </button>
                            <div class="rating-buttons">
                                <button class="rating-btn" onclick="rateMessage('${messageId}', 'like')" title="有用">
                                    <i class="fa fa-thumbs-up"></i>
                                </button>
                                <button class="rating-btn" onclick="rateMessage('${messageId}', 'dislike')" title="无用">
                                    <i class="fa fa-thumbs-down"></i>
                                </button>
                            </div>
                        `;
                    }

                    actions += '</div>';
                    return actions;
                }
            }

            const messageEnhancer = new MessageEnhancer();

            // 全局消息操作函数
            window.toggleThinking = function(thinkingId) {
                const thinkingSection = document.getElementById(thinkingId);
                if (!thinkingSection) return;

                const content = thinkingSection.querySelector('.thinking-content');
                const toggleText = thinkingSection.querySelector('.toggle-text');
                const toggleIcon = thinkingSection.querySelector('.toggle-icon');

                if (content.classList.contains('thinking-collapsed')) {
                    content.classList.remove('thinking-collapsed');
                    toggleText.textContent = '收起思考过程';
                    toggleIcon.className = 'fa fa-chevron-up toggle-icon';
                } else {
                    content.classList.add('thinking-collapsed');
                    toggleText.textContent = '展开思考过程';
                    toggleIcon.className = 'fa fa-chevron-down toggle-icon';
                }
            };

            window.copyCode = function(codeId) {
                const codeElement = document.getElementById(codeId);
                if (!codeElement) return;

                const code = codeElement.textContent;
                navigator.clipboard.writeText(code).then(() => {
                    // 显示复制成功提示
                    const btn = codeElement.parentElement.querySelector('.copy-code-btn');
                    const originalText = btn.innerHTML;
                    btn.innerHTML = '<i class="fa fa-check"></i> 已复制';
                    btn.style.backgroundColor = '#10b981';

                    setTimeout(() => {
                        btn.innerHTML = originalText;
                        btn.style.backgroundColor = '';
                    }, 2000);
                }).catch(err => {
                    console.error('复制失败:', err);
                });
            };

            window.copyMessage = function(messageId) {
                const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
                if (!messageElement) return;

                const messageContent = messageElement.querySelector('.chat-message').textContent;
                navigator.clipboard.writeText(messageContent).then(() => {
                    // 显示复制成功提示
                    showToast('消息已复制到剪贴板');
                }).catch(err => {
                    console.error('复制失败:', err);
                    showToast('复制失败', 'error');
                });
            };

            window.regenerateMessage = function(messageId) {
                const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
                if (!messageElement) return;

                // 找到对应的用户消息，重新发送
                const messageIndex = Array.from(chatContainer.children).indexOf(messageElement);
                if (messageIndex > 0) {
                    const userMessageElement = chatContainer.children[messageIndex - 1];
                    const userMessage = userMessageElement.querySelector('.chat-message').textContent;

                    // 移除当前AI回复
                    messageElement.remove();

                    // 重新发送消息
                    userInput.value = userMessage;
                    sendMessage();
                }
            };

            window.rateMessage = function(messageId, rating) {
                const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
                if (!messageElement) return;

                const ratingButtons = messageElement.querySelectorAll('.rating-btn');
                ratingButtons.forEach(btn => {
                    btn.classList.remove('active-like', 'active-dislike');
                });

                const ratingBtn = messageElement.querySelector(`[onclick*="${rating}"]`);
                if (ratingBtn) {
                    ratingBtn.classList.add(rating === 'like' ? 'active-like' : 'active-dislike');
                }

                // 这里可以添加评价数据的存储逻辑
                console.log(`消息 ${messageId} 被评为: ${rating}`);
                showToast(rating === 'like' ? '感谢您的反馈！' : '我们会继续改进');
            };

            // 显示提示消息
            function showToast(message, type = 'success') {
                const toast = document.createElement('div');
                const bgColor = type === 'error' ? 'bg-red-500' :
                               type === 'info' ? 'bg-blue-500' : 'bg-green-500';
                toast.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white text-sm transition-all duration-300 ${bgColor}`;
                toast.textContent = message;

                document.body.appendChild(toast);

                setTimeout(() => {
                    toast.style.opacity = '0';
                    toast.style.transform = 'translateY(-20px)';
                    setTimeout(() => {
                        document.body.removeChild(toast);
                    }, 300);
                }, 2000);
            }

            // 导出对话功能
            function exportConversation() {
                const messages = Array.from(chatContainer.children);
                if (messages.length === 0) {
                    showToast('没有对话内容可导出', 'info');
                    return;
                }

                let exportContent = `# 本地LLM对话记录\n\n`;
                exportContent += `导出时间: ${new Date().toLocaleString('zh-CN')}\n`;
                exportContent += `模型: ${apiConfig.config.model}\n\n`;
                exportContent += `---\n\n`;

                messages.forEach((messageDiv, index) => {
                    const isUser = messageDiv.classList.contains('justify-end') ||
                                  messageDiv.querySelector('.bg-user');
                    const messageContent = messageDiv.querySelector('.chat-message');

                    if (messageContent) {
                        const sender = isUser ? '用户' : 'AI助手';
                        let content = messageContent.textContent.trim();

                        // 移除时间戳和操作按钮文本
                        content = content.replace(/\d{2}\/\d{2} \d{2}:\d{2}$/, '').trim();
                        content = content.replace(/复制重新生成$/, '').trim();

                        if (content) {
                            exportContent += `## ${sender}\n\n${content}\n\n`;
                        }
                    }
                });

                // 创建下载链接
                const blob = new Blob([exportContent], { type: 'text/markdown;charset=utf-8' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `LLM对话记录_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.md`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                showToast('对话记录已导出');
            }

            // 模型能力数据库 - 基于用户实际安装的模型
            const modelCapabilities = {
                // === 用户的Ollama模型 ===

                // Llama 3.3 (42GB) - 最新的大型文本生成模型
                'llama3.3:latest': {
                    text: { supported: true, description: 'Meta最新的Llama 3.3模型，70B参数规模，在推理、代码生成和复杂问答方面表现卓越，支持多语言对话。' },
                    image: { supported: false, description: '此模型专注于文本生成，不支持图像理解功能。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },
                'llama3.3': {
                    text: { supported: true, description: 'Meta最新的Llama 3.3模型，70B参数规模，在推理、代码生成和复杂问答方面表现卓越，支持多语言对话。' },
                    image: { supported: false, description: '此模型专注于文本生成，不支持图像理解功能。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },

                // Mistral Small 3.2 (15GB) - 高效文本模型
                'mistral-small3.2:latest': {
                    text: { supported: true, description: 'Mistral AI的Small 3.2模型，22B参数，在保持高质量输出的同时提供快速响应，擅长逻辑推理和代码任务。' },
                    image: { supported: false, description: '此模型专注于文本处理，不支持图像理解功能。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },
                'mistral-small3.2': {
                    text: { supported: true, description: 'Mistral AI的Small 3.2模型，22B参数，在保持高质量输出的同时提供快速响应，擅长逻辑推理和代码任务。' },
                    image: { supported: false, description: '此模型专注于文本处理，不支持图像理解功能。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },

                // Qwen2.5-7B-Instruct-1M (4.7GB) - 长上下文文本模型
                'yasserrmd/Qwen2.5-7B-Instruct-1M:latest': {
                    text: { supported: true, description: 'Qwen2.5-7B指令微调版本，支持100万token超长上下文，在中英文理解、长文档分析和复杂推理方面表现优秀。' },
                    image: { supported: false, description: '此模型专注于长文本处理，不支持图像理解功能。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },
                'yasserrmd/Qwen2.5-7B-Instruct-1M': {
                    text: { supported: true, description: 'Qwen2.5-7B指令微调版本，支持100万token超长上下文，在中英文理解、长文档分析和复杂推理方面表现优秀。' },
                    image: { supported: false, description: '此模型专注于长文本处理，不支持图像理解功能。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },

                // DeepSeek Janus Pro 7B (4.2GB) - 多模态模型
                'erwan2/DeepSeek-Janus-Pro-7B:latest': {
                    text: { supported: true, description: 'DeepSeek Janus Pro多模态模型，7B参数，专为视觉-语言任务设计，在图像理解和文本生成方面均表现出色。' },
                    image: { supported: true, description: '强大的视觉理解能力，支持图像描述、场景分析、OCR文字识别、图表解读和复杂视觉推理任务。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },
                'erwan2/DeepSeek-Janus-Pro-7B': {
                    text: { supported: true, description: 'DeepSeek Janus Pro多模态模型，7B参数，专为视觉-语言任务设计，在图像理解和文本生成方面均表现出色。' },
                    image: { supported: true, description: '强大的视觉理解能力，支持图像描述、场景分析、OCR文字识别、图表解读和复杂视觉推理任务。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },

                // Qwen2.5VL (6.0GB) - 多模态视觉语言模型
                'qwen2.5vl:latest': {
                    text: { supported: true, description: '阿里巴巴Qwen2.5-VL多模态模型，在中英文文本生成和理解方面表现优异，特别适合中文用户。' },
                    image: { supported: true, description: '先进的视觉语言模型，支持图像理解、中文OCR、图表分析、场景描述和多轮视觉对话，在中文视觉问答方面表现突出。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },
                'qwen2.5vl': {
                    text: { supported: true, description: '阿里巴巴Qwen2.5-VL多模态模型，在中英文文本生成和理解方面表现优异，特别适合中文用户。' },
                    image: { supported: true, description: '先进的视觉语言模型，支持图像理解、中文OCR、图表分析、场景描述和多轮视觉对话，在中文视觉问答方面表现突出。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },

                // LM Studio中的Qwen2.5-VL变体（用户实际使用的模型名称）
                'qwen/qwen2.5-vl-7b': {
                    text: { supported: true, description: '阿里巴巴Qwen2.5-VL多模态模型7B版本，专为LM Studio优化，在中英文文本生成和理解方面表现优异。' },
                    image: { supported: true, description: '强大的视觉语言模型，支持图像理解、中文OCR、图表分析、场景描述和多轮视觉对话，特别适合中文视觉问答任务。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },
                'qwen/qwen2.5-vl': {
                    text: { supported: true, description: '阿里巴巴Qwen2.5-VL多模态模型，专为LM Studio优化，在中英文文本生成和理解方面表现优异。' },
                    image: { supported: true, description: '强大的视觉语言模型，支持图像理解、中文OCR、图表分析、场景描述和多轮视觉对话，特别适合中文视觉问答任务。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },
                'qwen2.5-vl-7b': {
                    text: { supported: true, description: '阿里巴巴Qwen2.5-VL多模态模型7B版本，在中英文文本生成和理解方面表现优异。' },
                    image: { supported: true, description: '强大的视觉语言模型，支持图像理解、中文OCR、图表分析、场景描述和多轮视觉对话，特别适合中文视觉问答任务。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },
                'qwen2.5-vl': {
                    text: { supported: true, description: '阿里巴巴Qwen2.5-VL多模态模型，在中英文文本生成和理解方面表现优异。' },
                    image: { supported: true, description: '强大的视觉语言模型，支持图像理解、中文OCR、图表分析、场景描述和多轮视觉对话，特别适合中文视觉问答任务。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },



                // Qwen3 (5.2GB) - 新一代文本模型
                'qwen3:latest': {
                    text: { supported: true, description: 'Qwen3最新版本，在中英文理解、代码生成、数学推理和创意写作方面有显著提升，响应速度快。' },
                    image: { supported: false, description: '此模型专注于文本处理，不支持图像理解功能。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },
                'qwen3': {
                    text: { supported: true, description: 'Qwen3最新版本，在中英文理解、代码生成、数学推理和创意写作方面有显著提升，响应速度快。' },
                    image: { supported: false, description: '此模型专注于文本处理，不支持图像理解功能。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },

                // Nomic Embed Text (274MB) - 文本嵌入模型
                'nomic-embed-text:latest': {
                    text: { supported: false, description: '这是一个文本嵌入模型，用于生成文本向量表示，不适用于对话生成任务。请选择其他对话模型。' },
                    image: { supported: false, description: '嵌入模型不支持图像理解功能。' },
                    audio: { supported: false, description: '嵌入模型不支持语音交互功能。' }
                },
                'nomic-embed-text': {
                    text: { supported: false, description: '这是一个文本嵌入模型，用于生成文本向量表示，不适用于对话生成任务。请选择其他对话模型。' },
                    image: { supported: false, description: '嵌入模型不支持图像理解功能。' },
                    audio: { supported: false, description: '嵌入模型不支持语音交互功能。' }
                },

                // === 通用模型名称匹配（不带版本标签） ===

                // === LM Studio 兼容性支持 ===
                // LM Studio通常使用不同的模型命名格式，添加常见的LM Studio模型
                'microsoft/DialoGPT-medium': {
                    text: { supported: true, description: 'Microsoft DialoGPT对话模型，专为多轮对话设计，在对话连贯性方面表现良好。' },
                    image: { supported: false, description: '此模型专注于对话生成，不支持图像理解功能。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },

                // Phantom WAN 14B FusionX - 高性能对话模型
                'phantom_wan_14b_fusionx': {
                    text: { supported: true, description: 'Phantom WAN 14B FusionX是一个高性能的14B参数对话模型，在中英文对话、推理和创意写作方面表现优异，响应速度快。' },
                    image: { supported: false, description: '此模型专注于文本生成，不支持图像理解功能。如需图像功能请切换到多模态模型。' },
                    audio: { supported: false, description: '此模型不支持语音交互功能。' }
                },

                // === 默认配置（用于未知模型） ===
                'default': {
                    text: { supported: true, description: '未知模型类型，假设支持基础文本对话功能。建议查看模型文档确认具体能力。' },
                    image: { supported: false, description: '未知模型的图像理解能力，建议查看模型文档或尝试上传图片测试。' },
                    audio: { supported: false, description: '未知模型的语音交互能力，建议查看模型文档确认是否支持。' }
                }
            };

            // 智能模型匹配函数
            function getModelCapabilities(modelName) {
                // 直接匹配
                if (modelCapabilities[modelName]) {
                    return modelCapabilities[modelName];
                }

                // 增强的模糊匹配算法
                const normalizedName = modelName.toLowerCase();
                const modelKeys = Object.keys(modelCapabilities);

                // 第一轮：精确匹配（包括命名空间）
                for (const key of modelKeys) {
                    if (key === 'default') continue;
                    const keyLower = key.toLowerCase();
                    if (normalizedName === keyLower) {
                        return modelCapabilities[key];
                    }
                }

                // 第二轮：包含关系匹配
                for (const key of modelKeys) {
                    if (key === 'default') continue;
                    const keyLower = key.toLowerCase();
                    if (normalizedName.includes(keyLower) || keyLower.includes(normalizedName)) {
                        return modelCapabilities[key];
                    }
                }

                // 第三轮：智能模式匹配（处理连字符、版本号等）
                for (const key of modelKeys) {
                    if (key === 'default') continue;

                    // 标准化处理：移除连字符、点、版本号等
                    const normalizeForMatching = (name) => {
                        return name.toLowerCase()
                            .replace(/[-_.]/g, '')  // 移除连字符、下划线、点
                            .replace(/:\w+$/, '')   // 移除版本标签 (:latest, :v1等)
                            .replace(/\/.*?\//, '') // 移除命名空间前缀
                            .replace(/\d+b$/, '')   // 移除模型大小标识 (7b, 13b等)
                            .replace(/v\d+/, '')    // 移除版本号 (v1, v2等)
                            .trim();
                    };

                    const normalizedInput = normalizeForMatching(normalizedName);
                    const normalizedKey = normalizeForMatching(key);

                    if (normalizedInput === normalizedKey ||
                        normalizedInput.includes(normalizedKey) ||
                        normalizedKey.includes(normalizedInput)) {
                        return modelCapabilities[key];
                    }
                }

                // 第四轮：基础名称匹配（移除命名空间和版本）
                const baseModelName = normalizedName.split(':')[0].split('/').pop();
                for (const key of modelKeys) {
                    if (key === 'default') continue;
                    const baseKeyName = key.toLowerCase().split(':')[0].split('/').pop();
                    if (baseModelName === baseKeyName) {
                        return modelCapabilities[key];
                    }
                }

                // 如果没有找到匹配，返回默认配置
                return modelCapabilities['default'];
            }

            // 更新模型状态显示
            function updateModelStatusDisplay(modelName) {
                const statusIndicator = document.getElementById('model-status-indicator');
                const statusText = document.getElementById('model-status-text');

                if (statusIndicator && statusText) {
                    if (modelName && modelName !== 'default') {
                        // 获取模型能力信息
                        const capabilities = getModelCapabilities(modelName);
                        const isMultimodal = capabilities.vision;

                        // 更新状态指示器
                        statusIndicator.className = 'w-2 h-2 rounded-full bg-green-500 mr-2';

                        // 更新状态文本
                        if (isMultimodal) {
                            statusText.textContent = `推理模型: ${modelName} (支持多模态)`;
                        } else {
                            statusText.textContent = `推理模型: ${modelName} (文本模式)`;
                        }
                    } else {
                        // 未选择模型或默认状态
                        statusIndicator.className = 'w-2 h-2 rounded-full bg-yellow-500 mr-2';
                        statusText.textContent = '未选择模型';
                    }
                }
            }

            // 更新模型能力显示
            function updateModelCapabilities(modelName) {
                // 清除之前的警告
                modelChecker.clearWarnings();

                // 检查模型兼容性
                const warning = modelChecker.getModelWarning(modelName);
                if (warning) {
                    modelChecker.showModelWarning(warning);
                }

                // 更新性能监控显示
                const isConversational = modelChecker.isConversationalModel(modelName);
                performanceMonitor.setConnectionStatus(isConversational, modelName);

                // 更新模型状态显示
                updateModelStatusDisplay(modelName);

                // 获取模型能力信息（使用智能匹配）
                const capabilities = getModelCapabilities(modelName);

                // 在控制台显示匹配结果（用于调试）
                console.log(`模型 "${modelName}" 匹配到能力配置:`, capabilities);
                console.log(`模型兼容性检查:`, { isConversational, warning });

                // 详细的匹配调试信息
                console.log('=== 模型匹配调试信息 ===');
                console.log('原始模型名称:', modelName);
                console.log('标准化名称:', modelName.toLowerCase());
                console.log('基础名称:', modelName.split(':')[0].split('/').pop());
                console.log('可用的模型键:', Object.keys(modelCapabilities).filter(k => k !== 'default'));
                console.log('匹配结果:', capabilities === modelCapabilities['default'] ? '使用默认配置（未匹配）' : '成功匹配');
                console.log('========================');

                // 更新文本推理卡片
                const textCard = document.querySelector('[onclick="selectMode(\'文本\')"]');
                if (textCard) {
                    const title = textCard.querySelector('h3');
                    const description = textCard.querySelector('p');
                    const icon = textCard.querySelector('i');

                    if (capabilities.text.supported) {
                        textCard.className = 'bg-white rounded-xl p-4 shadow-sm border-l-4 border-primary hover:shadow-md transition-shadow card-hover cursor-pointer';
                        icon.className = 'fa fa-file-text-o text-primary text-xl mr-3';
                        title.textContent = '文本推理';
                        description.textContent = capabilities.text.description;
                    } else {
                        textCard.className = 'bg-gray-100 rounded-xl p-4 shadow-sm border-l-4 border-gray-400 cursor-not-allowed opacity-60';
                        icon.className = 'fa fa-file-text-o text-gray-400 text-xl mr-3';
                        title.textContent = '文本推理 (不支持)';
                        description.textContent = capabilities.text.description;
                    }
                }

                // 更新图像理解卡片
                const imageCard = document.querySelector('[onclick="selectMode(\'图像理解\')"]');
                if (imageCard) {
                    const title = imageCard.querySelector('h3');
                    const description = imageCard.querySelector('p');
                    const icon = imageCard.querySelector('i');

                    if (capabilities.image.supported) {
                        imageCard.className = 'bg-white rounded-xl p-4 shadow-sm border-l-4 border-tertiary hover:shadow-md transition-shadow card-hover cursor-pointer';
                        icon.className = 'fa fa-image text-tertiary text-xl mr-3';
                        title.textContent = '图像理解';
                        description.textContent = capabilities.image.description;
                    } else {
                        imageCard.className = 'bg-gray-100 rounded-xl p-4 shadow-sm border-l-4 border-gray-400 cursor-not-allowed opacity-60';
                        icon.className = 'fa fa-image text-gray-400 text-xl mr-3';
                        title.textContent = '图像理解 (不支持)';
                        description.textContent = capabilities.image.description;
                    }
                }

                // 更新语音交互卡片
                const audioCard = document.querySelector('[onclick="selectMode(\'语音\')"]');
                if (audioCard) {
                    const title = audioCard.querySelector('h3');
                    const description = audioCard.querySelector('p');
                    const icon = audioCard.querySelector('i');

                    if (capabilities.audio.supported) {
                        audioCard.className = 'bg-white rounded-xl p-4 shadow-sm border-l-4 border-secondary hover:shadow-md transition-shadow card-hover cursor-pointer';
                        icon.className = 'fa fa-microphone text-secondary text-xl mr-3';
                        title.textContent = '语音交互';
                        description.textContent = capabilities.audio.description;
                    } else {
                        audioCard.className = 'bg-gray-100 rounded-xl p-4 shadow-sm border-l-4 border-gray-400 cursor-not-allowed opacity-60';
                        icon.className = 'fa fa-microphone text-gray-400 text-xl mr-3';
                        title.textContent = '语音交互 (不支持)';
                        description.textContent = capabilities.audio.description;
                    }
                }

                // 更新系统消息中的能力描述
                const systemMessage = document.querySelector('.bg-layer3 p');
                if (systemMessage) {
                    let supportedFeatures = [];
                    if (capabilities.text.supported) supportedFeatures.push('文本对话');
                    if (capabilities.image.supported) supportedFeatures.push('图像理解');
                    if (capabilities.audio.supported) supportedFeatures.push('语音交互');

                    const featuresText = supportedFeatures.length > 0 ? supportedFeatures.join('和') : '基础功能';
                    systemMessage.textContent = `欢迎使用本地大模型助手！当前模型 ${modelName} 支持${featuresText}。您可以直接输入问题${capabilities.image.supported ? '，或使用右侧按钮上传图片进行多模态交互' : ''}。`;
                }
            }

            // 连接状态管理
            function updateConnectionStatus(connected, message = '', version = '') {
                if (connected) {
                    statusIndicator.className = 'w-2 h-2 rounded-full bg-green-500';
                    statusText.textContent = version ? `已连接 (${version})` : '已连接';
                    statusText.className = 'text-xs text-green-600';

                    // 更新动态岛状态
                    dynamicIsland.updateConnection(true, apiConfig.config.model, apiConfig.config);
                } else {
                    statusIndicator.className = 'w-2 h-2 rounded-full bg-red-500';
                    statusText.textContent = message || '连接失败';
                    statusText.className = 'text-xs text-red-600';

                    // 更新动态岛状态
                    dynamicIsland.updateConnection(false, message, apiConfig.config);
                }
            }

            // 检查连接状态
            async function checkConnectionStatus() {
                if (apiConfig.config.apiType === 'ollama') {
                    const result = await apiConfig.checkOllamaConnection();
                    updateConnectionStatus(result.connected, result.error, result.version);
                    return result.connected;
                } else if (apiConfig.config.apiType === 'lm-studio') {
                    try {
                        const response = await fetch(`${apiConfig.config.baseURL}/models`, {
                            method: 'GET',
                            headers: { 'Content-Type': 'application/json' }
                        });

                        if (response.ok) {
                            updateConnectionStatus(true, '', 'LM Studio');
                            return true;
                        } else {
                            updateConnectionStatus(false, `HTTP ${response.status}`, '');
                            return false;
                        }
                    } catch (error) {
                        updateConnectionStatus(false, error.message, '');
                        return false;
                    }
                } else {
                    // 对于其他API类型，暂时显示未知状态
                    statusIndicator.className = 'w-2 h-2 rounded-full bg-yellow-500';
                    statusText.textContent = '未检测';
                    statusText.className = 'text-xs text-yellow-600';
                    return true;
                }
            }

            // 刷新模型列表
            async function refreshModelList() {
                if (apiConfig.config.apiType === 'ollama') {
                    await refreshOllamaModels();
                } else if (apiConfig.config.apiType === 'lm-studio') {
                    await refreshLMStudioModels();
                } else {
                    modelStatus.textContent = '此API类型不支持自动检测模型，请手动输入模型名称';
                    return;
                }
            }

            // 刷新Ollama模型列表
            async function refreshOllamaModels() {

                refreshModels.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
                refreshModels.disabled = true;
                modelStatus.textContent = '正在获取Ollama模型列表...';

                try {
                    const models = await apiConfig.getOllamaModels();

                    // 清空现有选项
                    modelSelect.innerHTML = '';

                    if (models.length === 0) {
                        modelSelect.innerHTML = '<option value="">未找到模型</option>';
                        modelStatus.textContent = '未找到已安装的模型，请先使用 ollama pull 下载模型';
                        modelStatus.className = 'mt-1 text-xs text-red-500';
                    } else {
                        // 添加模型选项
                        models.forEach(model => {
                            const option = document.createElement('option');
                            option.value = model.name;
                            option.textContent = `${model.name} (${(model.size / 1024 / 1024 / 1024).toFixed(1)}GB)`;
                            modelSelect.appendChild(option);
                        });

                        // 设置当前选中的模型
                        if (apiConfig.config.model && models.find(m => m.name === apiConfig.config.model)) {
                            modelSelect.value = apiConfig.config.model;
                        } else if (models.length > 0) {
                            modelSelect.value = models[0].name;
                            apiConfig.config.model = models[0].name;
                        }

                        modelStatus.textContent = `找到 ${models.length} 个Ollama模型`;
                        modelStatus.className = 'mt-1 text-xs text-green-600';

                        // 更新模型能力显示
                        updateModelCapabilities(modelSelect.value);
                    }
                } catch (error) {
                    console.error('获取Ollama模型列表失败:', error);
                    modelStatus.textContent = `获取Ollama模型失败: ${error.message}`;
                    modelStatus.className = 'mt-1 text-xs text-red-500';

                    // 添加默认选项
                    modelSelect.innerHTML = '<option value="llama3.3:latest">llama3.3:latest (手动输入)</option>';
                } finally {
                    refreshModels.innerHTML = '<i class="fa fa-refresh"></i>';
                    refreshModels.disabled = false;
                }
            }

            // 刷新LM Studio模型列表
            async function refreshLMStudioModels() {
                refreshModels.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';
                refreshModels.disabled = true;
                modelStatus.textContent = '正在获取LM Studio模型列表...';

                try {
                    // LM Studio使用OpenAI兼容的API获取模型列表
                    const response = await fetch(`${apiConfig.config.baseURL}/models`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            ...(apiConfig.config.apiKey && { 'Authorization': `Bearer ${apiConfig.config.apiKey}` })
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    const models = data.data || [];

                    // 清空现有选项
                    modelSelect.innerHTML = '';

                    if (models.length === 0) {
                        modelSelect.innerHTML = '<option value="">未找到模型</option>';
                        modelStatus.textContent = '未找到已加载的模型，请在LM Studio中加载模型';
                        modelStatus.className = 'mt-1 text-xs text-red-500';
                    } else {
                        // 添加模型选项
                        models.forEach(model => {
                            const option = document.createElement('option');
                            option.value = model.id;
                            option.textContent = model.id;
                            modelSelect.appendChild(option);
                        });

                        // 设置当前选中的模型
                        if (apiConfig.config.model && models.find(m => m.id === apiConfig.config.model)) {
                            modelSelect.value = apiConfig.config.model;
                        } else if (models.length > 0) {
                            modelSelect.value = models[0].id;
                            apiConfig.config.model = models[0].id;
                        }

                        modelStatus.textContent = `找到 ${models.length} 个LM Studio模型`;
                        modelStatus.className = 'mt-1 text-xs text-green-600';

                        // 更新模型能力显示
                        updateModelCapabilities(modelSelect.value);
                    }
                } catch (error) {
                    console.error('获取LM Studio模型列表失败:', error);
                    modelStatus.textContent = `获取LM Studio模型失败: ${error.message}`;
                    modelStatus.className = 'mt-1 text-xs text-red-500';

                    // 添加默认选项
                    modelSelect.innerHTML = '<option value="local-model">local-model (手动输入)</option>';
                } finally {
                    refreshModels.innerHTML = '<i class="fa fa-refresh"></i>';
                    refreshModels.disabled = false;
                }
            }

            // API通信类
            class LLMAPIClient {
                constructor(config) {
                    this.config = config;
                }

                async sendMessage(messages, onChunk = null) {
                    try {
                        let endpoint, requestBody, headers;

                        if (this.config.config.apiType === 'ollama') {
                            // Ollama API格式
                            endpoint = `${this.config.config.baseURL}/api/chat`;
                            requestBody = {
                                model: this.config.config.model,
                                messages: messages,
                                stream: this.config.config.streamOutput && onChunk !== null,
                                options: {
                                    temperature: this.config.config.temperature,
                                    num_predict: this.config.config.maxTokens
                                }
                            };
                            headers = { 'Content-Type': 'application/json' };
                        } else {
                            // OpenAI兼容API格式
                            endpoint = `${this.config.config.baseURL}/chat/completions`;

                            // 确保消息格式正确
                            const formattedMessages = messages.map(msg => {
                                if (typeof msg.content === 'string') {
                                    return msg;
                                } else if (Array.isArray(msg.content)) {
                                    // 处理多模态消息格式
                                    return {
                                        role: msg.role,
                                        content: msg.content
                                    };
                                } else {
                                    return {
                                        role: msg.role,
                                        content: String(msg.content || '')
                                    };
                                }
                            });

                            requestBody = {
                                model: this.config.config.model,
                                messages: formattedMessages,
                                temperature: this.config.config.temperature,
                                max_tokens: this.config.config.maxTokens,
                                stream: this.config.config.streamOutput && onChunk !== null
                            };

                            headers = {
                                'Content-Type': 'application/json',
                                ...(this.config.config.apiKey && { 'Authorization': `Bearer ${this.config.config.apiKey}` })
                            };
                        }

                        console.log('API请求详情:', {
                            endpoint,
                            headers,
                            requestBody: JSON.stringify(requestBody, null, 2)
                        });

                        const response = await fetch(endpoint, {
                            method: 'POST',
                            headers: headers,
                            body: JSON.stringify(requestBody)
                        });

                        if (!response.ok) {
                            let errorDetails = '';
                            try {
                                const errorData = await response.text();
                                errorDetails = errorData;
                                console.error('API错误响应:', errorData);
                            } catch (e) {
                                console.error('无法读取错误响应:', e);
                            }

                            throw new Error(`API请求失败: ${response.status} ${response.statusText}${errorDetails ? '\n详情: ' + errorDetails : ''}`);
                        }

                        if (this.config.config.streamOutput && onChunk) {
                            return this.handleStreamResponse(response, onChunk);
                        } else {
                            const data = await response.json();
                            if (this.config.config.apiType === 'ollama') {
                                return data.message.content;
                            } else {
                                return data.choices[0].message.content;
                            }
                        }
                    } catch (error) {
                        console.error('API调用错误:', error);
                        throw error;
                    }
                }

                async handleStreamResponse(response, onChunk) {
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    let fullResponse = '';

                    try {
                        while (true) {
                            const { done, value } = await reader.read();
                            if (done) break;

                            const chunk = decoder.decode(value);
                            const lines = chunk.split('\n');

                            for (const line of lines) {
                                if (line.trim() === '') continue;

                                if (this.config.config.apiType === 'ollama') {
                                    // Ollama流式响应格式
                                    try {
                                        const parsed = JSON.parse(line);
                                        if (parsed.message?.content) {
                                            const content = parsed.message.content;
                                            fullResponse += content;
                                            onChunk(content);
                                        }
                                        if (parsed.done) return fullResponse;
                                    } catch (e) {
                                        // 忽略解析错误
                                    }
                                } else {
                                    // OpenAI兼容流式响应格式
                                    if (line.startsWith('data: ')) {
                                        const data = line.slice(6);
                                        if (data === '[DONE]') return fullResponse;

                                        try {
                                            const parsed = JSON.parse(data);
                                            const content = parsed.choices?.[0]?.delta?.content;
                                            if (content) {
                                                fullResponse += content;
                                                onChunk(content);
                                            }
                                        } catch (e) {
                                            // 忽略解析错误
                                        }
                                    }
                                }
                            }
                        }
                    } finally {
                        reader.releaseLock();
                    }

                    return fullResponse;
                }

                async sendImageMessage(imageData, prompt, onChunk = null) {
                    try {
                        let messages;

                        if (this.config.config.apiType === 'ollama') {
                            // Ollama支持图像的格式
                            messages = [
                                {
                                    role: 'user',
                                    content: prompt,
                                    images: [imageData.split(',')[1]] // 移除data:image/...;base64,前缀
                                }
                            ];
                        } else {
                            // OpenAI兼容格式
                            messages = [
                                {
                                    role: 'user',
                                    content: [
                                        { type: 'text', text: prompt },
                                        { type: 'image_url', image_url: { url: imageData } }
                                    ]
                                }
                            ];
                        }

                        return await this.sendMessage(messages, onChunk);
                    } catch (error) {
                        console.error('图像API调用错误:', error);
                        throw error;
                    }
                }
            }

            const apiClient = new LLMAPIClient(apiConfig);

            // 监听滚动事件，改变导航栏样式
            window.addEventListener('scroll', function() {
                if (window.scrollY > 10) {
                    header.classList.add('shadow-md', 'py-2');
                    header.classList.remove('py-3');
                } else {
                    header.classList.remove('shadow-md', 'py-2');
                    header.classList.add('py-3');
                }
            });

            // 更新输入框提示
            function updateInputPlaceholder(mode) {
                const placeholders = {
                    '文本': '输入问题或对话内容...',
                    '多模态': '输入问题或上传图片...',
                    '图像理解': '上传图片并描述您想了解的内容...',
                    '图像生成': '描述您想要生成的图片，例如：一只可爱的小猫在花园里玩耍...',
                    '语音': '输入文本或录制语音...'
                };

                const placeholder = placeholders[mode] || '输入问题或上传图片...';
                userInput.placeholder = placeholder;
            }

            // 图片生成功能
            async function generateImage(prompt) {
                if (!prompt.trim()) {
                    alert('请输入图片描述');
                    return;
                }

                // 添加用户消息到聊天
                addMessage(prompt, 'user');
                userInput.value = '';

                // 禁用发送按钮
                sendButton.disabled = true;
                sendButton.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';

                // 添加生成中的消息
                const generatingMsg = addMessage('正在生成图片，请稍候...', 'bot');

                try {
                    // 获取图片生成配置
                    const imageApiType = document.getElementById('image-api-type').value;
                    const imageApiUrl = document.getElementById('image-api-url').value;
                    const imageApiKey = document.getElementById('image-api-key').value;

                    let imageUrl = null;

                    // 根据不同的API类型调用相应的接口
                    switch (imageApiType) {
                        case 'automatic1111':
                            imageUrl = await generateWithAutomatic1111(prompt, imageApiUrl, imageApiKey);
                            break;
                        case 'comfyui':
                            imageUrl = await generateWithComfyUI(prompt, imageApiUrl, imageApiKey);
                            break;
                        case 'stable-diffusion-webui':
                            imageUrl = await generateWithStableDiffusionWebUI(prompt, imageApiUrl, imageApiKey);
                            break;
                        case 'openai-dalle':
                            imageUrl = await generateWithDALLE(prompt, imageApiUrl, imageApiKey);
                            break;
                        case 'custom-image':
                            imageUrl = await generateWithCustomAPI(prompt, imageApiUrl, imageApiKey);
                            break;
                        default:
                            throw new Error('不支持的图片生成API类型');
                    }

                    // 移除生成中的消息
                    generatingMsg.remove();

                    // 添加生成的图片
                    if (imageUrl) {
                        const imageMessage = addMessage('', 'bot');
                        const imageElement = document.createElement('img');
                        imageElement.src = imageUrl;
                        imageElement.className = 'max-w-full h-auto rounded-lg shadow-md cursor-pointer';
                        imageElement.alt = `生成的图片: ${prompt}`;
                        imageElement.onclick = () => window.open(imageUrl, '_blank');

                        const imageContainer = document.createElement('div');
                        imageContainer.className = 'mt-2';
                        imageContainer.appendChild(imageElement);

                        const caption = document.createElement('p');
                        caption.className = 'text-sm text-gray-600 mt-2';
                        caption.textContent = `根据描述"${prompt}"生成的图片`;
                        imageContainer.appendChild(caption);

                        imageMessage.appendChild(imageContainer);
                    } else {
                        throw new Error('图片生成失败，未返回有效的图片URL');
                    }

                } catch (error) {
                    console.error('图片生成错误:', error);

                    // 移除生成中的消息
                    generatingMsg.remove();

                    // 添加错误消息
                    addMessage(`图片生成失败: ${error.message}`, 'bot');
                    showToast('图片生成失败，请检查API配置和网络连接', 'error');
                } finally {
                    // 恢复发送按钮
                    sendButton.disabled = false;
                    sendButton.innerHTML = '<i class="fa fa-paper-plane"></i>';
                }
            }

            // Automatic1111 WebUI API
            async function generateWithAutomatic1111(prompt, apiUrl, apiKey) {
                const response = await fetch(`${apiUrl}/sdapi/v1/txt2img`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(apiKey && { 'Authorization': `Bearer ${apiKey}` })
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        negative_prompt: "low quality, blurry, distorted",
                        steps: 20,
                        width: 512,
                        height: 512,
                        cfg_scale: 7,
                        sampler_name: "DPM++ 2M Karras"
                    })
                });

                if (!response.ok) {
                    throw new Error(`Automatic1111 API错误: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                if (data.images && data.images.length > 0) {
                    return `data:image/png;base64,${data.images[0]}`;
                }
                throw new Error('未返回生成的图片');
            }

            // ComfyUI API (简化版本)
            async function generateWithComfyUI(prompt, apiUrl, apiKey) {
                // ComfyUI的API比较复杂，这里提供一个基础示例
                // 实际使用时需要根据具体的workflow进行调整
                throw new Error('ComfyUI集成需要配置具体的workflow，请使用Automatic1111或其他API');
            }

            // Stable Diffusion WebUI API (与Automatic1111类似)
            async function generateWithStableDiffusionWebUI(prompt, apiUrl, apiKey) {
                return await generateWithAutomatic1111(prompt, apiUrl, apiKey);
            }

            // OpenAI DALL-E API
            async function generateWithDALLE(prompt, apiUrl, apiKey) {
                if (!apiKey) {
                    throw new Error('OpenAI DALL-E需要API密钥');
                }

                const response = await fetch('https://api.openai.com/v1/images/generations', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        n: 1,
                        size: "512x512"
                    })
                });

                if (!response.ok) {
                    throw new Error(`OpenAI API错误: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                if (data.data && data.data.length > 0) {
                    return data.data[0].url;
                }
                throw new Error('未返回生成的图片');
            }

            // 自定义API (通用格式)
            async function generateWithCustomAPI(prompt, apiUrl, apiKey) {
                const response = await fetch(`${apiUrl}/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(apiKey && { 'Authorization': `Bearer ${apiKey}` })
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        width: 512,
                        height: 512
                    })
                });

                if (!response.ok) {
                    throw new Error(`自定义API错误: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                // 假设返回格式为 { image_url: "..." } 或 { image: "base64..." }
                if (data.image_url) {
                    return data.image_url;
                } else if (data.image) {
                    return `data:image/png;base64,${data.image}`;
                }
                throw new Error('未返回生成的图片');
            }

            // 发送消息
            async function sendMessage() {
                const message = userInput.value.trim();
                if (!message && !currentImageData) return;

                // 检查是否为图像生成模式
                if (currentMode === '图像生成') {
                    await generateImage(message);
                    return;
                }

                // 检查模型兼容性（使用智能匹配）
                const currentModel = apiConfig.config.model;

                // 检查模型是否适合对话
                if (!modelChecker.isConversationalModel(currentModel)) {
                    alert('当前选择的模型不适合对话任务，请在设置中选择合适的对话模型（如llama、qwen、mistral等）。');
                    openSettings();
                    return;
                }

                // 开始性能计时和监控
                performanceMonitor.startTiming();
                performanceMonitor.setConnectionStatus('connecting', currentModel);
                performanceMonitor.addUserChars(message.length);

                const capabilities = getModelCapabilities(currentModel);

                // 如果有图片但模型不支持图像理解
                if (currentImageData && !capabilities.image.supported) {
                    alert(`当前模型 "${currentModel}" 不支持图像理解功能。\n请先切换到支持图像的模型（如 llama3.2-vision、llava、qwen2-vl 等），或移除图片后发送纯文本消息。`);
                    return;
                }

                // 禁用发送按钮
                sendButton.disabled = true;
                sendButton.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';

                try {
                    // 添加用户消息到聊天框
                    if (currentImageData) {
                        appendMessage('user', `
                            <div class="mb-2">
                                <img src="${currentImageData}" alt="上传的图片" class="rounded-lg max-w-full max-h-64 object-contain">
                            </div>
                            <p>${message || '请分析这张图片'}</p>
                        `);
                    } else {
                        appendMessage('user', message);
                    }

                    // 添加到对话历史
                    if (currentImageData) {
                        if (apiConfig.config.apiType === 'ollama') {
                            conversationHistory.push({
                                role: 'user',
                                content: message || '请分析这张图片',
                                images: [currentImageData.split(',')[1]]
                            });
                        } else {
                            conversationHistory.push({
                                role: 'user',
                                content: [
                                    { type: 'text', text: message || '请分析这张图片' },
                                    { type: 'image_url', image_url: { url: currentImageData } }
                                ]
                            });
                        }
                    } else {
                        conversationHistory.push({ role: 'user', content: message });
                    }

                    // 清空输入框和图片数据
                    userInput.value = '';
                    currentImageData = null;

                    // 显示机器人正在输入的状态
                    const typingIndicator = showTypingIndicator();

                    // 准备消息历史（包含系统提示）
                    const messages = [
                        { role: 'system', content: apiConfig.config.systemPrompt },
                        ...conversationHistory
                    ];

                    let botResponse = '';
                    let responseElement = null;

                    // 添加重试机制
                    let retryCount = 0;
                    const maxRetries = 2;

                    while (retryCount <= maxRetries) {
                        try {
                            if (apiConfig.config.streamOutput) {
                                // 流式响应
                                removeTypingIndicator();
                                responseElement = appendMessage('bot', '', true); // 创建空的响应元素

                                botResponse = await apiClient.sendMessage(messages, (chunk) => {
                                    updateStreamingMessage(responseElement, chunk);
                                });

                                // 完成流式消息处理
                                finishStreamingMessage(responseElement.parentElement);
                            } else {
                                // 非流式响应
                                performanceMonitor.recordFirstByte(); // 记录首字节时间
                                performanceMonitor.setConnectionStatus('connected', apiConfig.config.model);
                                botResponse = await apiClient.sendMessage(messages);
                                removeTypingIndicator();
                                appendMessage('bot', botResponse);

                                // 添加AI字符计数
                                performanceMonitor.updateStreamingChars(botResponse.length);
                            }
                            break; // 成功则跳出重试循环
                        } catch (retryError) {
                            retryCount++;
                            if (retryCount <= maxRetries) {
                                console.log(`第${retryCount}次重试...`);
                                if (responseElement) {
                                    updateStreamingMessage(responseElement, `\n\n[连接失败，正在重试 ${retryCount}/${maxRetries}...]`);
                                }
                                await new Promise(resolve => setTimeout(resolve, 1000 * retryCount)); // 递增延迟
                            } else {
                                throw retryError; // 重试次数用完，抛出错误
                            }
                        }
                    }

                    // 添加到对话历史
                    conversationHistory.push({ role: 'assistant', content: botResponse });

                    // 限制对话历史长度
                    if (conversationHistory.length > 20) {
                        conversationHistory = conversationHistory.slice(-20);
                    }

                    // 结束性能计时并增加对话计数
                    performanceMonitor.endTiming();
                    performanceMonitor.incrementConversation();

                } catch (error) {
                    removeTypingIndicator();
                    performanceMonitor.setConnectionStatus('failed', apiConfig.config.model);
                    console.error('发送消息失败:', error);

                    let errorMessage = '抱歉，发送消息时出现错误。';
                    let troubleshootingTips = '';

                    if (error.message.includes('Failed to fetch') || error.message.includes('fetch')) {
                        if (apiConfig.config.apiType === 'ollama') {
                            errorMessage = '无法连接到Ollama服务';
                            troubleshootingTips = `
<div class="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-sm">
    <div class="font-medium text-yellow-800 mb-2">故障排除步骤：</div>
    <ol class="list-decimal list-inside space-y-1 text-yellow-700">
        <li>确认Ollama服务正在运行：<code class="bg-yellow-100 px-1 rounded">ollama serve</code></li>
        <li>检查模型是否已下载：<code class="bg-yellow-100 px-1 rounded">ollama list</code></li>
        <li>如果没有模型，请下载：<code class="bg-yellow-100 px-1 rounded">ollama pull llama3.2</code></li>
        <li>访问 <a href="http://localhost:11434" target="_blank" class="text-blue-600 underline">http://localhost:11434</a> 确认服务运行</li>
    </ol>
</div>`;
                        } else {
                            errorMessage = '无法连接到API服务';
                            troubleshootingTips = `
<div class="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-sm">
    <div class="font-medium text-yellow-800 mb-2">请检查：</div>
    <ul class="list-disc list-inside space-y-1 text-yellow-700">
        <li>API服务是否正在运行</li>
        <li>API地址是否正确</li>
        <li>网络连接是否正常</li>
    </ul>
</div>`;
                        }
                    } else if (error.message.includes('404')) {
                        errorMessage = '模型未找到或API端点错误';
                        if (apiConfig.config.apiType === 'ollama') {
                            troubleshootingTips = `
<div class="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg text-sm">
    <div class="font-medium text-red-800 mb-2">模型问题：</div>
    <p class="text-red-700">请确认模型 <code class="bg-red-100 px-1 rounded">${apiConfig.config.model}</code> 已下载</p>
    <p class="text-red-700 mt-1">运行：<code class="bg-red-100 px-1 rounded">ollama pull ${apiConfig.config.model}</code></p>
</div>`;
                        }
                    } else if (error.message.includes('500')) {
                        errorMessage = '服务器内部错误';
                        troubleshootingTips = `
<div class="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg text-sm">
    <div class="text-red-700">服务器可能遇到问题，请稍后重试或检查服务器日志</div>
</div>`;
                    } else {
                        errorMessage = `请求失败: ${error.message}`;
                    }

                    appendMessage('bot', `
<div class="text-red-600">
    <i class="fa fa-exclamation-triangle mr-2"></i>${errorMessage}
    ${troubleshootingTips}
    <div class="mt-3 text-xs text-gray-500">
        <button onclick="location.reload()" class="text-blue-600 hover:underline">刷新页面重试</button> |
        <button onclick="document.getElementById('settings-button').click()" class="text-blue-600 hover:underline">检查设置</button>
    </div>
</div>`);
                } finally {
                    // 重新启用发送按钮
                    sendButton.disabled = false;
                    sendButton.innerHTML = '<i class="fa fa-paper-plane"></i>';
                }
            }
            
            // 添加消息到聊天框
            function appendMessage(sender, message, isStreaming = false) {
                // 隐藏欢迎界面
                const welcomeScreen = document.getElementById('welcome-screen');
                if (welcomeScreen && !welcomeScreen.classList.contains('welcome-fade-out')) {
                    welcomeScreen.classList.add('welcome-fade-out');
                    setTimeout(() => {
                        welcomeScreen.style.display = 'none';
                    }, 500);
                }

                const messageDiv = document.createElement('div');
                const messageId = `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
                messageDiv.setAttribute('data-message-id', messageId);
                messageDiv.className = sender === 'user'
                    ? 'flex items-start justify-end'
                    : 'flex items-start';

                if (sender === 'user') {
                    const actions = messageEnhancer.generateMessageActions(messageId, sender);
                    messageDiv.innerHTML = `
                        <div class="chat-message chat-message-user card-hover">
                            <div>${message}</div>
                            ${actions}
                        </div>
                        <div class="w-8 h-8 rounded-full bg-user flex items-center justify-center ml-3 flex-shrink-0">
                            <i class="fa fa-user text-white"></i>
                        </div>
                    `;
                } else {
                    let messageContent;
                    if (isStreaming) {
                        messageContent = '<span class="streaming-content"></span><span class="cursor">|</span>';
                    } else {
                        // 应用消息增强处理
                        const enhancedMessage = messageEnhancer.enhanceMessage(message, sender);
                        const actions = messageEnhancer.generateMessageActions(messageId, sender);
                        messageContent = `<div>${enhancedMessage}</div>${actions}`;
                    }

                    messageDiv.innerHTML = `
                        <div class="w-8 h-8 rounded-full bg-chatbot flex items-center justify-center mr-3 flex-shrink-0">
                            <i class="fa fa-robot text-white"></i>
                        </div>
                        <div class="chat-message chat-message-bot card-hover">
                            ${messageContent}
                        </div>
                    `;
                }

                chatContainer.appendChild(messageDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;

                // 添加消息发送音效
                if (!isStreaming) {
                    playNotificationSound();
                }

                return isStreaming ? messageDiv.querySelector('.streaming-content') : messageDiv;
            }

            // 更新流式消息
            function updateStreamingMessage(element, chunk) {
                if (element) {
                    // 记录首字节响应时间
                    if (element.textContent === '') {
                        performanceMonitor.recordFirstByte();
                        performanceMonitor.setConnectionStatus('connected', apiConfig.config.model);
                    }

                    element.textContent += chunk;

                    // 更新流式字符计数
                    performanceMonitor.updateStreamingChars(chunk.length);

                    chatContainer.scrollTop = chatContainer.scrollHeight;
                }
            }

            // 完成流式消息
            function finishStreamingMessage(messageDiv) {
                const cursor = messageDiv.querySelector('.cursor');
                const streamingContent = messageDiv.querySelector('.streaming-content');

                if (cursor) {
                    cursor.remove();
                }

                if (streamingContent) {
                    // 获取流式内容
                    const rawContent = streamingContent.textContent;

                    // 应用消息增强处理
                    const enhancedContent = messageEnhancer.enhanceMessage(rawContent, 'bot');

                    // 生成消息ID和操作按钮
                    const messageId = messageDiv.getAttribute('data-message-id') ||
                        `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
                    messageDiv.setAttribute('data-message-id', messageId);

                    const actions = messageEnhancer.generateMessageActions(messageId, 'bot');

                    // 替换内容
                    const chatMessage = messageDiv.querySelector('.chat-message');
                    if (chatMessage) {
                        chatMessage.innerHTML = `<div>${enhancedContent}</div>${actions}`;
                    }
                }

                playNotificationSound();
            }
            
            // 播放通知音效
            function playNotificationSound() {
                // 在实际应用中，可以添加真实的音效
                // 这里仅作为交互反馈的示例
                console.log("Message notification sound played");
            }
            
            // 显示打字指示器
            function showTypingIndicator() {
                const typingDiv = document.createElement('div');
                typingDiv.className = 'flex items-start';
                typingDiv.id = 'typing-indicator';
                typingDiv.innerHTML = `
                    <div class="w-8 h-8 rounded-full bg-chatbot flex items-center justify-center mr-3 flex-shrink-0">
                        <i class="fa fa-robot text-white"></i>
                    </div>
                    <div class="chat-message chat-message-bot">
                        <div class="typing-indicator">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </div>
                `;
                
                chatContainer.appendChild(typingDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }
            
            // 移除打字指示器
            function removeTypingIndicator() {
                const typingIndicator = document.getElementById('typing-indicator');
                if (typingIndicator) {
                    typingIndicator.remove();
                }
            }
            
            // 发送按钮点击事件
            sendButton.addEventListener('click', sendMessage);
            
            // 输入框快捷键事件
            userInput.addEventListener('keydown', function(e) {
                // Enter发送消息（Shift+Enter换行）
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    if (!sendButton.disabled) {
                        sendMessage();
                    }
                }

                // Ctrl+Enter也可以发送消息
                if (e.key === 'Enter' && e.ctrlKey) {
                    e.preventDefault();
                    if (!sendButton.disabled) {
                        sendMessage();
                    }
                }

                // Escape清空输入框
                if (e.key === 'Escape') {
                    e.preventDefault();
                    userInput.value = '';
                    userInput.style.height = 'auto';
                }
            });

            // 全局快捷键支持
            document.addEventListener('keydown', function(e) {
                // Ctrl+/ 打开设置
                if (e.ctrlKey && e.key === '/') {
                    e.preventDefault();
                    openSettings();
                }

                // Ctrl+K 清空聊天记录
                if (e.ctrlKey && e.key === 'k') {
                    e.preventDefault();
                    clearChat();
                }

                // Ctrl+S 导出对话
                if (e.ctrlKey && e.key === 's') {
                    e.preventDefault();
                    exportConversation();
                }

                // Ctrl+F 搜索消息（如果实现了搜索功能）
                if (e.ctrlKey && e.key === 'f') {
                    e.preventDefault();
                    // 这里可以添加搜索功能
                    showToast('搜索功能开发中...', 'info');
                }
            });

            // 自动调整输入框高度
            userInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });
            
            // 图片上传事件
            imageUpload.addEventListener('change', function(e) {
                if (e.target.files && e.target.files[0]) {
                    const file = e.target.files[0];

                    // 检查当前模型是否支持图像理解（使用智能匹配）
                    const currentModel = apiConfig.config.model;
                    const capabilities = getModelCapabilities(currentModel);

                    if (!capabilities.image.supported) {
                        alert(`当前模型 "${currentModel}" 不支持图像理解功能。\n请切换到支持图像的模型（如 qwen2.5vl、erwan2/DeepSeek-Janus-Pro-7B 等）。`);
                        e.target.value = '';
                        return;
                    }

                    // 检查文件大小 (限制为10MB)
                    if (file.size > 10 * 1024 * 1024) {
                        alert('图片文件过大，请选择小于10MB的图片');
                        return;
                    }

                    // 检查文件类型
                    if (!file.type.startsWith('image/')) {
                        alert('请选择有效的图片文件');
                        return;
                    }

                    const reader = new FileReader();

                    reader.onload = function(e) {
                        currentImageData = e.target.result;

                        // 在输入框旁边显示预览
                        showImagePreview(currentImageData);

                        // 更新输入框提示
                        userInput.placeholder = '输入对图片的问题或直接发送...';
                        userInput.focus();
                    }

                    reader.onerror = function() {
                        alert('读取图片文件失败，请重试');
                    }

                    reader.readAsDataURL(file);
                }

                // 清空文件输入，允许重复选择同一文件
                e.target.value = '';
            });

            // 显示图片预览
            function showImagePreview(imageData) {
                // 移除现有预览
                const existingPreview = document.querySelector('.image-preview');
                if (existingPreview) {
                    existingPreview.remove();
                }

                // 创建预览元素
                const previewDiv = document.createElement('div');
                previewDiv.className = 'image-preview flex items-center bg-blue-50 border border-blue-200 rounded-lg p-2 mb-2';
                previewDiv.innerHTML = `
                    <img src="${imageData}" alt="预览图片" class="w-12 h-12 object-cover rounded">
                    <span class="ml-2 text-sm text-blue-700 flex-1">图片已选择，输入问题后发送</span>
                    <button class="ml-2 text-red-500 hover:text-red-700" onclick="clearImagePreview()" title="移除图片">
                        <i class="fa fa-times"></i>
                    </button>
                `;

                // 插入到输入区域上方
                const inputArea = document.querySelector('.p-4.border-t');
                inputArea.insertBefore(previewDiv, inputArea.firstChild);
            }

            // 清除图片预览
            window.clearImagePreview = function() {
                const preview = document.querySelector('.image-preview');
                if (preview) {
                    preview.remove();
                }
                currentImageData = null;
                userInput.placeholder = '输入问题或上传图片...';
            }
            
            // 设置面板相关功能
            function openSettings() {
                settingsModal.classList.add('active');
                modalBackdrop.classList.add('active');
                document.body.style.overflow = 'hidden';
            }
            
            function closeSettingsModal() {
                settingsModal.classList.remove('active');
                modalBackdrop.classList.remove('active');
                document.body.style.overflow = '';
            }
            
            settingsButton.addEventListener('click', async function() {
                openSettings();
                // 打开设置时检查连接状态和刷新模型列表
                await checkConnectionStatus();
                await refreshModelList();
            });
            closeSettings.addEventListener('click', closeSettingsModal);
            cancelSettings.addEventListener('click', closeSettingsModal);
            modalBackdrop.addEventListener('click', closeSettingsModal);

            // 刷新模型列表按钮
            refreshModels.addEventListener('click', refreshModelList);

            // API类型变化时的处理
            document.getElementById('api-type').addEventListener('change', async function() {
                const apiType = this.value;
                const apiUrlInput = document.getElementById('api-url');

                // 根据API类型设置默认URL
                if (apiType === 'ollama') {
                    apiUrlInput.value = 'http://localhost:11434';
                    apiConfig.config.baseURL = 'http://localhost:11434';
                } else if (apiType === 'lm-studio') {
                    apiUrlInput.value = 'http://localhost:1234/v1';
                    apiConfig.config.baseURL = 'http://localhost:1234/v1';
                } else if (apiType === 'openai-compatible') {
                    apiUrlInput.value = 'http://localhost:1234/v1';
                    apiConfig.config.baseURL = 'http://localhost:1234/v1';
                } else if (apiType === 'custom') {
                    apiUrlInput.value = '';
                }

                // 更新配置并检查连接
                apiConfig.config.apiType = apiType;
                await checkConnectionStatus();
                await refreshModelList();
            });

            // 清空对话函数
            function clearChat() {
                if (confirm('确定要清空所有对话记录吗？')) {
                    // 完全清空聊天容器
                    chatContainer.innerHTML = '';

                    // 清空对话历史
                    conversationHistory = [];

                    // 重置性能监控
                    performanceMonitor.reset();

                    // 清空图片预览
                    if (typeof clearImagePreview === 'function') {
                        clearImagePreview();
                    }

                    // 重置当前图片数据
                    if (typeof currentImageData !== 'undefined') {
                        currentImageData = null;
                    }

                    // 重置输入框
                    if (userInput) {
                        userInput.placeholder = '输入问题或上传图片...';
                        userInput.value = '';
                        userInput.style.height = 'auto';
                    }

                    // 重新显示欢迎界面
                    const welcomeScreen = document.createElement('div');
                    welcomeScreen.id = 'welcome-screen';
                    welcomeScreen.className = 'flex flex-col items-center justify-center h-full text-center py-12';
                    welcomeScreen.innerHTML = `
                        <div class="w-16 h-16 rounded-full bg-gradient-to-r from-primary to-tertiary flex items-center justify-center mb-6">
                            <i class="fa fa-robot text-white text-2xl"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-800 mb-4">本地大模型助手</h2>
                        <p class="text-gray-600 mb-8 max-w-md">
                            支持文本对话、图像理解和多模态交互。<br>
                            开始对话，体验AI的强大能力！
                        </p>

                        <!-- 功能特性卡片 -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl w-full">
                            <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                                <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mb-3 mx-auto">
                                    <i class="fa fa-comments text-blue-600"></i>
                                </div>
                                <h3 class="font-semibold text-gray-800 mb-2">智能对话</h3>
                                <p class="text-sm text-gray-600">支持多轮对话，理解上下文，提供准确回答</p>
                            </div>

                            <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                                <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mb-3 mx-auto">
                                    <i class="fa fa-image text-green-600"></i>
                                </div>
                                <h3 class="font-semibold text-gray-800 mb-2">图像理解</h3>
                                <p class="text-sm text-gray-600">上传图片进行分析，支持OCR和场景描述</p>
                            </div>

                            <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-100">
                                <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mb-3 mx-auto">
                                    <i class="fa fa-cog text-purple-600"></i>
                                </div>
                                <h3 class="font-semibold text-gray-800 mb-2">本地部署</h3>
                                <p class="text-sm text-gray-600">数据安全，响应快速，支持多种模型</p>
                            </div>
                        </div>
                    `;

                    chatContainer.appendChild(welcomeScreen);
                    showToast('对话记录已清空');
                }
            }

            // 清空对话按钮事件
            clearChatButton.addEventListener('click', clearChat);

            // 帮助按钮事件
            helpButton.addEventListener('click', function() {
                showToast('快捷键：Enter发送，Shift+Enter换行，Ctrl+K打开设置，Ctrl+S保存设置，Ctrl+L清空对话');
            });

            // 模型选择变化监听
            modelSelect.addEventListener('change', function() {
                const selectedModel = this.value;
                updateModelCapabilities(selectedModel);

                // 更新配置
                apiConfig.config.model = selectedModel;
                apiConfig.save();

                // 显示模型切换提示
                const modelIndicator = document.createElement('div');
                modelIndicator.className = 'fixed top-1/4 left-1/2 transform -translate-x-1/2 bg-primary/90 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-fadeIn';
                modelIndicator.textContent = `已切换到模型: ${selectedModel}`;
                document.body.appendChild(modelIndicator);

                setTimeout(() => {
                    modelIndicator.style.opacity = '0';
                    modelIndicator.style.transition = 'opacity 0.5s ease-out';
                    setTimeout(() => {
                        if (modelIndicator.parentNode) {
                            modelIndicator.parentNode.removeChild(modelIndicator);
                        }
                    }, 500);
                }, 2000);
            });

            // 模式选择
            modeButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    modeButtons.forEach(b => {
                        b.classList.remove('bg-primary', 'text-white');
                        b.classList.add('bg-gray-200', 'text-gray-700');
                    });
                    this.classList.remove('bg-gray-200', 'text-gray-700');
                    this.classList.add('bg-primary', 'text-white');
                    currentMode = this.dataset.mode;
                    currentModeElement.textContent = currentMode;

                    // 更新输入框提示
                    updateInputPlaceholder(currentMode);

                    // 添加模式切换的视觉反馈
                    const modeIndicator = document.createElement('div');
                    modeIndicator.className = 'fixed top-1/4 left-1/2 transform -translate-x-1/2 bg-primary/90 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-fadeIn';
                    modeIndicator.textContent = `已切换到${currentMode}模式`;
                    document.body.appendChild(modeIndicator);
                    
                    setTimeout(() => {
                        modeIndicator.style.opacity = '0';
                        modeIndicator.style.transition = 'opacity 0.5s ease-out';
                        setTimeout(() => {
                            modeIndicator.remove();
                        }, 500);
                    }, 1500);
                });
            });
            
            // 功能卡片点击切换模式
            window.selectMode = function(mode) {
                // 检查当前模型是否支持所选功能（使用智能匹配）
                const currentModel = apiConfig.config.model;
                const capabilities = getModelCapabilities(currentModel);

                // 检查模型是否适合对话
                if (!modelChecker.isConversationalModel(currentModel)) {
                    alert('当前选择的模型不适合对话任务，请先在设置中选择合适的对话模型。');
                    return;
                }

                let supported = true;
                let featureName = '';

                switch(mode) {
                    case '文本':
                        supported = capabilities.text.supported;
                        featureName = '文本推理';
                        break;
                    case '图像':
                    case '图像理解':
                        supported = capabilities.image.supported;
                        featureName = '图像理解';
                        break;
                    case '图像生成':
                        // 图像生成不依赖当前LLM模型，总是支持
                        supported = true;
                        featureName = '图像生成';
                        break;
                    case '语音':
                        supported = capabilities.audio.supported;
                        featureName = '语音交互';
                        break;
                }

                if (!supported) {
                    alert(`当前模型 "${currentModel}" 不支持${featureName}功能。\n请在设置中切换到支持该功能的模型。`);
                    // 打开设置面板让用户切换模型
                    openSettings();
                    return;
                }

                // 更新性能监控的当前模式
                let monitorMode = 'text';
                if ((mode === '图像' || mode === '图像理解') && capabilities.image.supported) {
                    monitorMode = 'image';
                } else if (mode === '图像生成') {
                    monitorMode = 'image';
                } else if (mode === '语音' && capabilities.audio.supported) {
                    monitorMode = 'multimodal';
                } else if (capabilities.image.supported || capabilities.audio.supported) {
                    monitorMode = 'multimodal';
                }
                performanceMonitor.setCurrentMode(monitorMode);

                // 找到对应的模式按钮并触发点击事件
                const modeBtn = Array.from(modeButtons).find(btn => btn.dataset.mode === mode);
                if (modeBtn) {
                    modeBtn.click();
                    // 打开设置面板
                    openSettings();
                }
            };
            
            // 测试连接
            testConnection.addEventListener('click', async function() {
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fa fa-spinner fa-spin mr-1"></i> 测试中...';
                this.disabled = true;

                try {
                    // 临时更新配置进行测试
                    const tempConfig = new APIConfig();
                    tempConfig.config.apiType = document.getElementById('api-type').value;
                    tempConfig.config.baseURL = document.getElementById('api-url').value;
                    tempConfig.config.apiKey = document.getElementById('api-key').value;
                    tempConfig.config.model = document.getElementById('model-name').value;

                    // 首先检查基础连接
                    if (tempConfig.config.apiType === 'ollama') {
                        const connectionResult = await tempConfig.checkOllamaConnection();
                        if (!connectionResult.connected) {
                            throw new Error(`Ollama服务未运行: ${connectionResult.error}`);
                        }
                    }

                    // 然后测试模型调用
                    const testClient = new LLMAPIClient(tempConfig);
                    const testMessages = [{ role: 'user', content: 'Hi' }];

                    await testClient.sendMessage(testMessages);

                    this.innerHTML = '<i class="fa fa-check mr-1"></i> 连接成功';
                    this.className = this.className.replace('border-primary text-primary', 'border-green-500 text-green-500');

                    // 更新连接状态
                    updateConnectionStatus(true, '', tempConfig.config.apiType === 'ollama' ? 'Ollama' : 'API');

                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.className = this.className.replace('border-green-500 text-green-500', 'border-primary text-primary');
                        this.disabled = false;
                    }, 2000);

                } catch (error) {
                    console.error('连接测试失败:', error);
                    this.innerHTML = '<i class="fa fa-times mr-1"></i> 连接失败';
                    this.className = this.className.replace('border-primary text-primary', 'border-red-500 text-red-500');

                    // 更新连接状态
                    updateConnectionStatus(false, error.message);

                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.className = this.className.replace('border-red-500 text-red-500', 'border-primary text-primary');
                        this.disabled = false;
                    }, 3000);

                    // 提供详细的故障排除指导
                    let troubleshootingMessage = `连接测试失败: ${error.message}\n\n故障排除建议:\n`;

                    if (tempConfig.config.apiType === 'ollama') {
                        troubleshootingMessage += `
1. 确保Ollama服务正在运行:
   - 在终端运行: ollama serve
   - 或检查是否已在后台运行

2. 检查模型是否已下载:
   - 运行: ollama list
   - 如果没有模型，下载一个: ollama pull llama3.2

3. 确认端口11434未被占用:
   - 访问: http://localhost:11434
   - 应该看到"Ollama is running"

4. 检查防火墙设置是否阻止了连接`;
                    } else {
                        troubleshootingMessage += `
1. 确认API服务正在运行
2. 检查API地址是否正确
3. 验证API密钥（如果需要）
4. 确认模型名称正确`;
                    }

                    alert(troubleshootingMessage);
                }
            });

            // 保存设置
            saveSettings.addEventListener('click', function() {
                // 添加保存动画
                this.innerHTML = '<i class="fa fa-spinner fa-spin mr-1"></i> 保存中...';
                this.disabled = true;

                try {
                    // 更新配置
                    apiConfig.updateFromUI();

                    // 保存到本地存储
                    apiConfig.saveConfig();

                    setTimeout(() => {
                        const apiTypeNames = {
                            'ollama': 'Ollama',
                            'lm-studio': 'LM Studio',
                            'openai-compatible': 'OpenAI兼容',
                            'custom': '自定义API'
                        };

                        appendMessage('bot', `设置已保存！当前配置：
- API类型: ${apiTypeNames[apiConfig.config.apiType] || apiConfig.config.apiType}
- API地址: ${apiConfig.config.baseURL}
- 模型: ${apiConfig.config.model}
- 温度: ${apiConfig.config.temperature}
- 最大令牌: ${apiConfig.config.maxTokens}
- 流式输出: ${apiConfig.config.streamOutput ? '启用' : '禁用'}
- GPU加速: ${apiConfig.config.useGPU ? '启用' : '禁用'}`);
                        closeSettingsModal();

                        // 重置按钮状态
                        this.innerHTML = '保存设置';
                        this.disabled = false;
                    }, 800);
                } catch (error) {
                    console.error('保存设置失败:', error);
                    alert('保存设置失败，请检查输入');
                    this.innerHTML = '保存设置';
                    this.disabled = false;
                }
            });

            // 设置标签页切换
            const settingsTabBtns = document.querySelectorAll('.settings-tab-btn');
            const settingsTabContents = document.querySelectorAll('.settings-tab-content');

            settingsTabBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const targetTab = this.dataset.tab;

                    // 更新按钮状态
                    settingsTabBtns.forEach(b => {
                        b.classList.remove('border-primary', 'text-primary');
                        b.classList.add('border-transparent', 'text-gray-500');
                    });
                    this.classList.remove('border-transparent', 'text-gray-500');
                    this.classList.add('border-primary', 'text-primary');

                    // 切换内容
                    settingsTabContents.forEach(content => {
                        content.classList.add('hidden');
                    });
                    document.getElementById(`${targetTab}-tab`).classList.remove('hidden');

                    // 如果切换到模型管理标签页，自动发现服务
                    if (targetTab === 'models') {
                        discoverAndRenderServices();
                    }
                });
            });

            // 服务发现相关函数
            async function discoverAndRenderServices() {
                const container = document.getElementById('services-container');
                if (!container) return;

                // 显示加载状态
                container.innerHTML = `
                    <div class="text-center py-8 text-gray-500">
                        <i class="fa fa-spinner fa-spin text-2xl mb-2"></i>
                        <p>正在扫描本地AI服务...</p>
                    </div>
                `;

                try {
                    await serviceDiscovery.discoverServices();
                    serviceDiscovery.renderServices(container);
                } catch (error) {
                    console.error('服务发现失败:', error);
                    container.innerHTML = `
                        <div class="text-center py-8 text-red-500">
                            <i class="fa fa-exclamation-triangle text-2xl mb-2"></i>
                            <p>服务发现失败</p>
                            <p class="text-sm mt-2">${error.message}</p>
                        </div>
                    `;
                }
            }

            // 绑定服务发现相关事件
            const refreshServicesBtn = document.getElementById('refresh-services');
            if (refreshServicesBtn) {
                refreshServicesBtn.addEventListener('click', discoverAndRenderServices);
            }

            const testAllServicesBtn = document.getElementById('test-all-services');
            if (testAllServicesBtn) {
                testAllServicesBtn.addEventListener('click', async function() {
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fa fa-spinner fa-spin mr-2"></i>测试中...';
                    this.disabled = true;

                    try {
                        await serviceDiscovery.discoverServices();
                        const onlineServices = serviceDiscovery.discoveredServices.filter(s => s.status === 'online');
                        showToast(`测试完成：${onlineServices.length}/${serviceDiscovery.discoveredServices.length} 个服务在线`, 'success');
                        serviceDiscovery.renderServices(document.getElementById('services-container'));
                    } catch (error) {
                        showToast('测试失败：' + error.message, 'error');
                    }

                    this.innerHTML = originalText;
                    this.disabled = false;
                });
            }

            const exportModelsBtn = document.getElementById('export-models');
            if (exportModelsBtn) {
                exportModelsBtn.addEventListener('click', function() {
                    const modelsData = {
                        timestamp: new Date().toISOString(),
                        services: serviceDiscovery.discoveredServices
                    };

                    const blob = new Blob([JSON.stringify(modelsData, null, 2)], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `ai-models-${new Date().toISOString().split('T')[0]}.json`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);

                    showToast('模型列表已导出', 'success');
                });
            }

            // 图像生成连接测试
            const testImageConnectionBtn = document.getElementById('test-image-connection');
            if (testImageConnectionBtn) {
                testImageConnectionBtn.addEventListener('click', async function() {
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fa fa-spinner fa-spin mr-2"></i>测试中...';
                    this.disabled = true;

                    try {
                        const imageApiType = document.getElementById('image-api-type').value;
                        const imageApiUrl = document.getElementById('image-api-url').value;
                        const imageApiKey = document.getElementById('image-api-key').value;

                        // 根据API类型进行不同的测试
                        let testUrl = '';
                        let testMethod = 'GET';
                        let testBody = null;

                        switch (imageApiType) {
                            case 'automatic1111':
                            case 'stable-diffusion-webui':
                                testUrl = `${imageApiUrl}/sdapi/v1/options`;
                                break;
                            case 'comfyui':
                                testUrl = `${imageApiUrl}/system_stats`;
                                break;
                            case 'openai-dalle':
                                testUrl = 'https://api.openai.com/v1/models';
                                break;
                            default:
                                testUrl = `${imageApiUrl}/health`;
                        }

                        const response = await fetch(testUrl, {
                            method: testMethod,
                            headers: {
                                'Content-Type': 'application/json',
                                ...(imageApiKey && { 'Authorization': `Bearer ${imageApiKey}` })
                            },
                            body: testBody
                        });

                        if (response.ok) {
                            showToast('图像生成服务连接成功！', 'success');
                        } else {
                            throw new Error(`连接失败: ${response.status} ${response.statusText}`);
                        }
                    } catch (error) {
                        console.error('图像生成连接测试失败:', error);
                        showToast(`连接失败: ${error.message}`, 'error');
                    } finally {
                        this.innerHTML = originalText;
                        this.disabled = false;
                    }
                });
            }

            // 滑块值更新
            temperatureSlider.addEventListener('input', function() {
                tempValue.textContent = this.value;
            });

            maxLengthSlider.addEventListener('input', function() {
                lengthValue.textContent = this.value;
            });

            // 企业级参数简化处理
            const responseStyleSelect = document.getElementById('response-style');
            const responseLengthSelect = document.getElementById('response-length');
            const toggleAdvancedBtn = document.getElementById('toggle-advanced');
            const advancedParams = document.getElementById('advanced-params');
            const advancedChevron = document.getElementById('advanced-chevron');

            // 响应风格映射到温度值
            const styleToTemperature = {
                'precise': 0.3,    // 精确模式 - 低温度
                'balanced': 0.7,   // 平衡模式 - 中等温度
                'creative': 1.2    // 创意模式 - 高温度
            };

            // 响应长度映射到最大令牌数
            const lengthToTokens = {
                'short': 500,      // 简洁回答
                'medium': 1000,    // 标准回答
                'long': 2000       // 详细回答
            };

            // 响应风格变化处理
            if (responseStyleSelect) {
                responseStyleSelect.addEventListener('change', function() {
                    const temperature = styleToTemperature[this.value];
                    if (temperature !== undefined) {
                        temperatureSlider.value = temperature;
                        tempValue.textContent = temperature;
                    }
                });
            }

            // 响应长度变化处理
            if (responseLengthSelect) {
                responseLengthSelect.addEventListener('change', function() {
                    const tokens = lengthToTokens[this.value];
                    if (tokens !== undefined) {
                        maxLengthSlider.value = tokens;
                        lengthValue.textContent = tokens;
                    }
                });
            }

            // 高级参数折叠切换
            if (toggleAdvancedBtn) {
                toggleAdvancedBtn.addEventListener('click', function() {
                    const isHidden = advancedParams.classList.contains('hidden');
                    if (isHidden) {
                        advancedParams.classList.remove('hidden');
                        advancedChevron.classList.add('rotate-180');
                    } else {
                        advancedParams.classList.add('hidden');
                        advancedChevron.classList.remove('rotate-180');
                    }
                });
            }
            
            // 输入框获得焦点时的动画
            userInput.addEventListener('focus', function() {
                this.parentElement.classList.add('scale-[1.01]');
                this.parentElement.style.transition = 'transform 0.2s ease';
            });
            
            userInput.addEventListener('blur', function() {
                this.parentElement.classList.remove('scale-[1.01]');
            });
            
            // 页面加载完成后的欢迎动画和初始化检查
            setTimeout(async () => {
                const welcomeMsg = document.querySelector('.chat-message-bot:first-of-type');
                if (welcomeMsg) {
                    welcomeMsg.classList.add('animate-pulse');
                    setTimeout(() => {
                        welcomeMsg.classList.remove('animate-pulse');
                    }, 2000);
                }

                // 自动检查连接状态
                await checkConnectionStatus();

                // 初始化模型能力显示
                updateModelCapabilities(apiConfig.config.model);

                // 初始化输入框提示
                updateInputPlaceholder(currentMode);

                // 如果是Ollama且连接成功，自动刷新模型列表
                if (apiConfig.config.apiType === 'ollama') {
                    const connectionResult = await apiConfig.checkOllamaConnection();
                    if (connectionResult.connected) {
                        try {
                            const models = await apiConfig.getOllamaModels();
                            if (models.length === 0) {
                                // 如果没有模型，显示提示
                                appendMessage('bot', `
<div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
    <div class="flex items-center">
        <i class="fa fa-exclamation-triangle text-yellow-600 mr-2"></i>
        <div>
            <div class="font-medium text-yellow-800">未找到已安装的模型</div>
            <div class="text-yellow-700 text-sm mt-1">
                请先下载一个模型，例如：<br>
                <code class="bg-yellow-100 px-2 py-1 rounded mt-1 inline-block">ollama pull llama3.2</code>
            </div>
        </div>
    </div>
</div>`);
                            } else {
                                // 显示可用模型信息
                                appendMessage('bot', `
<div class="bg-green-50 border border-green-200 rounded-lg p-4">
    <div class="flex items-center">
        <i class="fa fa-check-circle text-green-600 mr-2"></i>
        <div>
            <div class="font-medium text-green-800">Ollama连接成功</div>
            <div class="text-green-700 text-sm mt-1">
                找到 ${models.length} 个可用模型，当前使用：${apiConfig.config.model}
            </div>
        </div>
    </div>
</div>`);
                            }
                        } catch (error) {
                            console.error('获取模型列表失败:', error);
                        }
                    } else {
                        // 连接失败，显示故障排除指导
                        appendMessage('bot', `
<div class="bg-red-50 border border-red-200 rounded-lg p-4">
    <div class="flex items-center">
        <i class="fa fa-exclamation-circle text-red-600 mr-2"></i>
        <div>
            <div class="font-medium text-red-800">无法连接到Ollama服务</div>
            <div class="text-red-700 text-sm mt-1">
                请确保Ollama正在运行：<br>
                <code class="bg-red-100 px-2 py-1 rounded mt-1 inline-block">ollama serve</code>
            </div>
        </div>
    </div>
</div>`);
                    }
                }
            }, 1000);

            // 详细统计弹窗事件处理
            const statsDetailBtn = document.getElementById('stats-detail-btn');
            const statsDetailModal = document.getElementById('stats-detail-modal');
            const closeStatsModal = document.getElementById('close-stats-modal');

            if (statsDetailBtn && statsDetailModal && closeStatsModal) {
                statsDetailBtn.addEventListener('click', function() {
                    // 更新详细统计数据
                    updateDetailedStats();
                    statsDetailModal.classList.remove('hidden');
                });

                closeStatsModal.addEventListener('click', function() {
                    statsDetailModal.classList.add('hidden');
                });

                // 点击背景关闭弹窗
                statsDetailModal.addEventListener('click', function(e) {
                    if (e.target === statsDetailModal) {
                        statsDetailModal.classList.add('hidden');
                    }
                });
            }

            // 更新详细统计数据
            function updateDetailedStats() {
                // 字符比例
                const charRatioDisplay = document.getElementById('char-ratio-display');
                if (charRatioDisplay) {
                    if (performanceMonitor.userCharCount > 0) {
                        const ratio = (performanceMonitor.aiCharCount / performanceMonitor.userCharCount).toFixed(1);
                        charRatioDisplay.textContent = `${ratio}:1`;
                    } else {
                        charRatioDisplay.textContent = '--';
                    }
                }

                // 会话效率（字符/分钟）
                const efficiencyDisplay = document.getElementById('session-efficiency-display');
                if (efficiencyDisplay) {
                    const startTime = performanceMonitor.firstMessageTime || performanceMonitor.sessionStartTime;
                    const durationMinutes = (Date.now() - startTime) / (1000 * 60);
                    if (durationMinutes > 0) {
                        const totalChars = performanceMonitor.userCharCount + performanceMonitor.aiCharCount;
                        const efficiency = Math.round(totalChars / durationMinutes);
                        efficiencyDisplay.textContent = efficiency.toLocaleString();
                    } else {
                        efficiencyDisplay.textContent = '--';
                    }
                }
            }

            // 初始化性能监控显示
            performanceMonitor.updateAllDisplays();

            // 初始化系统监控
            performanceMonitor.initSystemMonitoring();

            // 定期更新详细统计（如果弹窗打开）
            setInterval(() => {
                if (statsDetailModal && !statsDetailModal.classList.contains('hidden')) {
                    updateDetailedStats();
                    performanceMonitor.updateDetailedSystemInfo();
                }
            }, 1000);
        });
    </script>
</body>
</html>