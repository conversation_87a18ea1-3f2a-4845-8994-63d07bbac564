<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI聊天助手 - 响应式版本</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 响应式布局样式 */
        .container-responsive {
            max-width: 100vw;
            overflow-x: hidden;
        }
        
        .chat-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .status-bar {
            flex-shrink: 0;
            position: sticky;
            top: 0;
            z-index: 50;
            background: white;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .monitoring-panel {
            flex-shrink: 0;
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            background: #f8fafc;
        }
        
        .input-area {
            flex-shrink: 0;
            background: white;
            border-top: 1px solid #e5e7eb;
            padding: 1rem;
        }
        
        /* 消息样式 */
        .user-message {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            margin-left: auto;
            margin-right: 0;
            max-width: min(85%, 600px);
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .ai-message {
            background: white;
            border: 1px solid #e2e8f0;
            color: #1e293b;
            margin-left: 0;
            margin-right: auto;
            max-width: min(85%, 600px);
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .message-container {
            position: relative;
            margin-bottom: 1rem;
            width: 100%;
        }

        .message-actions {
            opacity: 0;
            transition: opacity 0.2s ease;
            position: absolute;
            top: 8px;
            right: 8px;
            display: flex;
            gap: 4px;
        }

        .message-container:hover .message-actions {
            opacity: 1;
        }

        .action-btn {
            background: rgba(0, 0, 0, 0.1);
            border: none;
            border-radius: 4px;
            padding: 4px 6px;
            cursor: pointer;
            font-size: 12px;
            color: #64748b;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background: rgba(0, 0, 0, 0.2);
            color: #374151;
        }

        /* 响应式网格 */
        .monitoring-grid {
            display: grid;
            gap: 0.75rem;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .monitoring-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 0.5rem;
            }
            
            .user-message, .ai-message {
                max-width: 95%;
            }
            
            .chat-messages {
                padding: 0.75rem;
            }
            
            .input-area {
                padding: 0.75rem;
            }
            
            .status-bar {
                padding: 0.75rem;
            }
            
            .monitoring-panel {
                padding: 0.75rem;
            }
        }

        @media (max-width: 480px) {
            .monitoring-grid {
                grid-template-columns: 1fr 1fr;
                gap: 0.5rem;
            }
            
            .user-message, .ai-message {
                max-width: 98%;
                font-size: 0.9rem;
            }
            
            .chat-messages {
                padding: 0.5rem;
            }
            
            .input-area {
                padding: 0.5rem;
            }
        }

        /* 状态指示器 */
        .status-online { background-color: #10b981; }
        .status-offline { background-color: #ef4444; }
        .status-warning { background-color: #f59e0b; }

        .service-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-weight: 600;
        }

        .service-ollama { background-color: #3b82f6; color: white; }
        .service-lmstudio { background-color: #10b981; color: white; }

        /* 文件预览区域 */
        .file-preview-area {
            max-width: 100%;
            overflow-x: auto;
        }

        /* 输入框自适应 */
        .input-container {
            display: flex;
            gap: 0.5rem;
            align-items: flex-end;
            flex-wrap: wrap;
        }

        .input-textarea {
            flex: 1;
            min-width: 200px;
            resize: none;
            max-height: 120px;
        }

        .input-buttons {
            display: flex;
            gap: 0.5rem;
            flex-shrink: 0;
        }

        @media (max-width: 480px) {
            .input-container {
                flex-direction: column;
                align-items: stretch;
            }
            
            .input-buttons {
                justify-content: space-between;
            }
            
            .input-textarea {
                min-width: auto;
            }
        }

        /* 设置模态框响应式 */
        .modal-content {
            max-width: min(90vw, 600px);
            max-height: 90vh;
            overflow-y: auto;
        }

        /* 历史对话侧边栏 */
        .history-sidebar {
            width: 300px;
            transition: transform 0.3s ease;
        }

        @media (max-width: 768px) {
            .history-sidebar {
                width: 280px;
                position: fixed;
                top: 0;
                left: 0;
                height: 100vh;
                z-index: 100;
                transform: translateX(-100%);
                background: white;
                box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            }
            
            .history-sidebar.open {
                transform: translateX(0);
            }
        }

        @media (max-width: 480px) {
            .history-sidebar {
                width: 100vw;
            }
        }

        /* 加载动画 */
        .thinking-dots {
            display: inline-flex;
            gap: 2px;
        }

        .thinking-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: #64748b;
            animation: thinking 1.4s ease-in-out infinite both;
        }

        .thinking-dot:nth-child(1) { animation-delay: -0.32s; }
        .thinking-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes thinking {
            0%, 80%, 100% {
                transform: scale(0);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* 流式输出光标 */
        .streaming-cursor {
            display: inline-block;
            width: 2px;
            height: 1.2em;
            background-color: #3b82f6;
            animation: blink 1s infinite;
            margin-left: 2px;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        /* 滚动条样式 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 确保所有元素不超出容器 */
        * {
            box-sizing: border-box;
        }

        .overflow-hidden {
            overflow: hidden;
        }

        .text-ellipsis {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    </style>
</head>
<body class="bg-gray-50 container-responsive">
    <!-- 历史对话侧边栏 -->
    <div id="history-sidebar" class="history-sidebar bg-white border-r border-gray-200">
        <div class="p-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-800">对话历史</h3>
                <button id="close-history" class="md:hidden p-2 text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <button id="new-chat" class="w-full mt-3 px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                <i class="fas fa-plus mr-2"></i>新建对话
            </button>
        </div>
        <div class="p-4 border-b border-gray-200">
            <div class="relative">
                <input type="text" id="search-conversations" placeholder="搜索对话..."
                       class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm"></i>
            </div>
        </div>
        <div id="history-list" class="flex-1 overflow-y-auto custom-scrollbar p-4">
            <!-- 历史对话列表将在这里动态生成 -->
        </div>
    </div>

    <!-- 主聊天界面 -->
    <div class="chat-container">
        <!-- 顶部状态栏 -->
        <div class="status-bar px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button id="toggle-history" class="md:hidden p-2 text-gray-500 hover:text-gray-700">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="text-lg font-semibold text-gray-800 hidden sm:block">AI聊天助手</h1>
                    <h1 class="text-base font-semibold text-gray-800 sm:hidden">AI助手</h1>
                    <div class="flex items-center space-x-2">
                        <div id="connection-indicator" class="w-3 h-3 rounded-full status-offline"></div>
                        <span id="connection-text" class="text-sm text-gray-600 hidden sm:inline">检测中...</span>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="hidden sm:flex items-center space-x-2">
                        <span id="current-model" class="text-sm font-medium text-gray-700">未选择模型</span>
                        <span id="model-service-badge" class="service-badge hidden">未知</span>
                    </div>
                    <button id="settings-btn" class="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                        <i class="fas fa-cog"></i>
                        <span class="hidden sm:inline ml-1">设置</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 系统监控面板 -->
        <div class="monitoring-panel px-4 py-3">
            <div class="monitoring-grid">
                <!-- 当前模型 -->
                <div class="bg-white rounded-lg p-3 shadow-sm">
                    <div class="text-xs text-gray-500 mb-1">当前模型</div>
                    <div id="panel-current-model" class="text-sm font-semibold text-blue-600 text-ellipsis">未选择</div>
                    <div class="text-xs text-gray-400 mt-1">AI模型</div>
                </div>

                <!-- 消息统计 -->
                <div class="bg-white rounded-lg p-3 shadow-sm">
                    <div class="text-xs text-gray-500 mb-1">消息数量</div>
                    <div id="panel-message-count" class="text-sm font-semibold text-green-600">0</div>
                    <div class="text-xs text-gray-400 mt-1">本次对话</div>
                </div>

                <!-- 响应时间 -->
                <div class="bg-white rounded-lg p-3 shadow-sm">
                    <div class="text-xs text-gray-500 mb-1">响应时间</div>
                    <div id="panel-response-time" class="text-sm font-semibold text-yellow-600">--</div>
                    <div class="text-xs text-gray-400 mt-1">毫秒</div>
                </div>

                <!-- 输出速度 -->
                <div class="bg-white rounded-lg p-3 shadow-sm">
                    <div class="text-xs text-gray-500 mb-1">输出速度</div>
                    <div id="panel-output-speed" class="text-sm font-semibold text-purple-600">--</div>
                    <div class="text-xs text-gray-400 mt-1">字符/秒</div>
                </div>
            </div>
        </div>

        <!-- 聊天消息区域 -->
        <div id="chat-messages" class="chat-messages custom-scrollbar">
            <div class="text-center text-gray-500 py-8">
                <i class="fas fa-comments text-4xl mb-4"></i>
                <p class="text-lg mb-2">开始您的AI对话</p>
                <p class="text-sm">发送消息开始聊天，或点击设置配置AI服务</p>
            </div>
        </div>

        <!-- 文件预览区域 -->
        <div id="file-preview-area" class="hidden file-preview-area mx-4 mb-4 p-4 bg-white rounded-lg border border-gray-200">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700">已选择文件:</span>
                <button id="clear-files" class="text-red-500 hover:text-red-700 text-sm">
                    <i class="fas fa-times"></i> 清除
                </button>
            </div>
            <div id="file-list" class="space-y-2"></div>
        </div>

        <!-- 输入区域 -->
        <div class="input-area">
            <div class="input-container">
                <div class="input-buttons">
                    <!-- 文件上传按钮 -->
                    <label for="file-input" class="px-3 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors cursor-pointer flex items-center space-x-1" title="上传文件">
                        <i class="fas fa-paperclip text-sm"></i>
                        <span class="hidden sm:inline text-sm">附件</span>
                    </label>
                    <input type="file" id="file-input" multiple accept="image/*,.pdf,.txt,.doc,.docx,.json,.csv" class="hidden">
                    
                    <!-- 图片上传按钮 -->
                    <label for="image-input" class="px-3 py-2 bg-green-200 text-green-700 rounded-lg hover:bg-green-300 transition-colors cursor-pointer flex items-center space-x-1" title="上传图片">
                        <i class="fas fa-image text-sm"></i>
                        <span class="hidden sm:inline text-sm">图片</span>
                    </label>
                    <input type="file" id="image-input" multiple accept="image/png,image/jpg,image/jpeg,image/gif,image/webp" class="hidden">
                </div>

                <!-- 输入框 -->
                <textarea id="user-input" placeholder="输入您的消息..." class="input-textarea border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" rows="1"></textarea>
                
                <!-- 发送/停止按钮 -->
                <div class="flex space-x-2">
                    <button id="send-btn" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-paper-plane"></i>
                        <span class="hidden sm:inline ml-1">发送</span>
                    </button>
                    <button id="stop-btn" class="hidden px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors">
                        <i class="fas fa-stop"></i>
                        <span class="hidden sm:inline ml-1">停止</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        class ResponsiveChatApp {
            constructor() {
                console.log('响应式聊天应用初始化开始');
                
                // 基础配置
                this.uploadedFiles = [];
                this.maxFileSize = 10 * 1024 * 1024; // 10MB
                this.currentModel = null;
                this.currentService = null;
                this.isStreaming = false;
                this.currentStreamingMessage = null;
                
                // 对话管理
                this.conversations = [];
                this.currentConversationId = null;
                this.messageHistory = [];
                
                // 统计信息
                this.stats = {
                    messageCount: 0,
                    lastResponseTime: null,
                    lastOutputSpeed: null
                };
                
                // 服务发现
                this.services = {
                    ollama: { url: 'http://localhost:11434', name: 'Ollama' },
                    lmstudio: { url: 'http://localhost:1234', name: 'LM Studio' }
                };
                this.activeServices = [];
                this.discoveredModels = [];
                
                this.init();
            }

            async init() {
                try {
                    this.setupEventListeners();
                    this.loadSettings();
                    this.loadConversations();
                    this.createNewConversation();
                    await this.detectServices();
                    this.updateUI();
                    console.log('响应式聊天应用初始化完成');
                } catch (error) {
                    console.error('应用初始化失败:', error);
                }
            }

            setupEventListeners() {
                // 发送按钮
                const sendBtn = document.getElementById('send-btn');
                if (sendBtn) {
                    sendBtn.addEventListener('click', () => this.sendMessage());
                }

                // 停止按钮
                const stopBtn = document.getElementById('stop-btn');
                if (stopBtn) {
                    stopBtn.addEventListener('click', () => this.stopStreaming());
                }

                // 输入框
                const userInput = document.getElementById('user-input');
                if (userInput) {
                    userInput.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            this.sendMessage();
                        }
                    });
                    
                    // 自动调整高度
                    userInput.addEventListener('input', () => {
                        userInput.style.height = 'auto';
                        userInput.style.height = Math.min(userInput.scrollHeight, 120) + 'px';
                    });
                }

                // 文件上传
                const fileInput = document.getElementById('file-input');
                const imageInput = document.getElementById('image-input');
                const clearFilesBtn = document.getElementById('clear-files');
                
                if (fileInput) {
                    fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
                }
                if (imageInput) {
                    imageInput.addEventListener('change', (e) => this.handleFileUpload(e));
                }
                if (clearFilesBtn) {
                    clearFilesBtn.addEventListener('click', () => this.clearFiles());
                }

                // 设置按钮
                const settingsBtn = document.getElementById('settings-btn');
                if (settingsBtn) {
                    settingsBtn.addEventListener('click', () => this.openSettings());
                }

                // 历史对话相关
                const toggleHistoryBtn = document.getElementById('toggle-history');
                const closeHistoryBtn = document.getElementById('close-history');
                const newChatBtn = document.getElementById('new-chat');
                
                if (toggleHistoryBtn) {
                    toggleHistoryBtn.addEventListener('click', () => this.toggleHistorySidebar());
                }
                if (closeHistoryBtn) {
                    closeHistoryBtn.addEventListener('click', () => this.closeHistorySidebar());
                }
                if (newChatBtn) {
                    newChatBtn.addEventListener('click', () => this.createNewConversation());
                }

                // 搜索功能
                const searchInput = document.getElementById('search-conversations');
                if (searchInput) {
                    searchInput.addEventListener('input', (e) => this.searchConversations(e.target.value));
                }

                // 响应式处理
                window.addEventListener('resize', () => this.handleResize());

                console.log('事件监听器设置完成');
            }

            // 服务检测
            async detectServices() {
                console.log('开始检测AI服务');
                this.activeServices = [];
                this.discoveredModels = [];

                for (const [serviceKey, service] of Object.entries(this.services)) {
                    try {
                        const response = await fetch(`${service.url}/api/tags`, {
                            method: 'GET',
                            timeout: 5000
                        });

                        if (response.ok) {
                            const data = await response.json();
                            this.activeServices.push(serviceKey);

                            if (data.models && Array.isArray(data.models)) {
                                data.models.forEach(model => {
                                    this.discoveredModels.push({
                                        id: `${serviceKey}-${model.name}`,
                                        name: model.name,
                                        service: serviceKey,
                                        serviceName: service.name,
                                        size: model.size || 0
                                    });
                                });
                            }
                            console.log(`${service.name} 服务检测成功`);
                        }
                    } catch (error) {
                        console.log(`${service.name} 服务检测失败:`, error.message);
                    }
                }

                console.log(`检测完成: ${this.activeServices.length} 个服务, ${this.discoveredModels.length} 个模型`);
            }

            // 设置管理
            loadSettings() {
                try {
                    const saved = localStorage.getItem('chat-settings');
                    if (saved) {
                        const settings = JSON.parse(saved);
                        if (settings.services) this.services = { ...this.services, ...settings.services };
                        if (settings.maxFileSize) this.maxFileSize = settings.maxFileSize;
                        if (settings.currentModel) this.currentModel = settings.currentModel;
                        if (settings.currentService) this.currentService = settings.currentService;
                        console.log('设置加载完成');
                    }
                } catch (error) {
                    console.error('加载设置失败:', error);
                }
            }

            // 对话管理
            loadConversations() {
                try {
                    const saved = localStorage.getItem('chat-conversations');
                    this.conversations = saved ? JSON.parse(saved) : [];
                    console.log(`加载了 ${this.conversations.length} 个历史对话`);
                } catch (error) {
                    console.error('加载对话历史失败:', error);
                    this.conversations = [];
                }
            }

            saveConversations() {
                try {
                    localStorage.setItem('chat-conversations', JSON.stringify(this.conversations));
                } catch (error) {
                    console.error('保存对话历史失败:', error);
                }
            }

            createNewConversation() {
                const conversation = {
                    id: Date.now().toString(),
                    title: '新对话',
                    messages: [],
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };

                this.conversations.unshift(conversation);
                this.currentConversationId = conversation.id;
                this.messageHistory = [];
                this.stats.messageCount = 0;

                this.saveConversations();
                this.updateHistoryList();
                this.updateChatMessages();
                this.closeHistorySidebar();

                console.log('创建新对话:', conversation.id);
            }

            switchConversation(conversationId) {
                const conversation = this.conversations.find(c => c.id === conversationId);
                if (!conversation) return;

                this.currentConversationId = conversationId;
                this.messageHistory = conversation.messages || [];
                this.stats.messageCount = this.messageHistory.length;

                this.updateChatMessages();
                this.updateHistoryList();
                this.closeHistorySidebar();

                console.log('切换到对话:', conversationId);
            }

            deleteConversation(conversationId) {
                this.conversations = this.conversations.filter(c => c.id !== conversationId);

                if (this.currentConversationId === conversationId) {
                    if (this.conversations.length > 0) {
                        this.switchConversation(this.conversations[0].id);
                    } else {
                        this.createNewConversation();
                    }
                }

                this.saveConversations();
                this.updateHistoryList();
            }

            updateCurrentConversation() {
                const conversation = this.conversations.find(c => c.id === this.currentConversationId);
                if (conversation) {
                    conversation.messages = this.messageHistory;
                    conversation.updatedAt = new Date().toISOString();

                    // 自动生成标题
                    if (conversation.title === '新对话' && this.messageHistory.length > 0) {
                        const firstMessage = this.messageHistory.find(m => m.role === 'user');
                        if (firstMessage) {
                            conversation.title = firstMessage.content.substring(0, 30) + (firstMessage.content.length > 30 ? '...' : '');
                        }
                    }

                    this.saveConversations();
                    this.updateHistoryList();
                }
            }

            // UI更新
            updateUI() {
                this.updateConnectionStatus();
                this.updateModelInfo();
                this.updateStats();
                this.updateHistoryList();
            }

            updateConnectionStatus() {
                const indicator = document.getElementById('connection-indicator');
                const text = document.getElementById('connection-text');

                if (this.activeServices.length > 0) {
                    if (indicator) indicator.className = 'w-3 h-3 rounded-full status-online';
                    if (text) text.textContent = `已连接 ${this.activeServices.length} 个服务`;
                } else {
                    if (indicator) indicator.className = 'w-3 h-3 rounded-full status-offline';
                    if (text) text.textContent = '未连接';
                }
            }

            updateModelInfo() {
                const currentModelElement = document.getElementById('current-model');
                const badge = document.getElementById('model-service-badge');
                const panelModel = document.getElementById('panel-current-model');

                if (this.currentModel) {
                    if (currentModelElement) currentModelElement.textContent = this.currentModel.name;
                    if (panelModel) panelModel.textContent = this.currentModel.name;
                    if (badge) {
                        badge.textContent = this.currentModel.serviceName;
                        badge.className = `service-badge service-${this.currentModel.service}`;
                        badge.classList.remove('hidden');
                    }
                } else {
                    if (currentModelElement) currentModelElement.textContent = '未选择模型';
                    if (panelModel) panelModel.textContent = '未选择';
                    if (badge) badge.classList.add('hidden');
                }
            }

            updateStats() {
                const elements = {
                    'panel-message-count': this.stats.messageCount,
                    'panel-response-time': this.stats.lastResponseTime ? `${this.stats.lastResponseTime}ms` : '--',
                    'panel-output-speed': this.stats.lastOutputSpeed ? `${this.stats.lastOutputSpeed.toFixed(1)} 字符/秒` : '--'
                };

                Object.entries(elements).forEach(([id, value]) => {
                    const element = document.getElementById(id);
                    if (element) element.textContent = value;
                });
            }

            updateHistoryList() {
                // 清空搜索框
                const searchInput = document.getElementById('search-conversations');
                if (searchInput) searchInput.value = '';

                // 渲染完整对话列表
                this.renderConversationList(this.conversations);
            }

            searchConversations(query) {
                const filteredConversations = query.trim() === '' ?
                    this.conversations :
                    this.conversations.filter(conv =>
                        conv.title.toLowerCase().includes(query.toLowerCase()) ||
                        conv.messages.some(msg =>
                            msg.content.toLowerCase().includes(query.toLowerCase())
                        )
                    );

                this.renderConversationList(filteredConversations);
            }

            renderConversationList(conversations) {
                const historyList = document.getElementById('history-list');
                if (!historyList) return;

                if (conversations.length === 0) {
                    historyList.innerHTML = '<div class="text-center text-gray-500 py-8">暂无匹配的对话</div>';
                    return;
                }

                historyList.innerHTML = conversations.map(conversation => `
                    <div class="conversation-item p-3 rounded-lg border border-gray-200 mb-2 cursor-pointer hover:bg-gray-50 ${conversation.id === this.currentConversationId ? 'bg-blue-50 border-blue-200' : ''}"
                         data-conversation-id="${conversation.id}">
                        <div class="flex items-center justify-between">
                            <div class="flex-1 min-w-0">
                                <div class="text-sm font-medium text-gray-900 text-ellipsis">${conversation.title}</div>
                                <div class="text-xs text-gray-500 mt-1">${new Date(conversation.updatedAt).toLocaleDateString()}</div>
                                <div class="text-xs text-gray-400">${conversation.messages.length} 条消息</div>
                            </div>
                            <div class="flex items-center space-x-1">
                                <button class="rename-conversation p-1 text-gray-400 hover:text-blue-500" data-conversation-id="${conversation.id}" title="重命名">
                                    <i class="fas fa-edit text-xs"></i>
                                </button>
                                <button class="duplicate-conversation p-1 text-gray-400 hover:text-green-500" data-conversation-id="${conversation.id}" title="复制对话">
                                    <i class="fas fa-copy text-xs"></i>
                                </button>
                                <button class="export-conversation p-1 text-gray-400 hover:text-purple-500" data-conversation-id="${conversation.id}" title="导出对话">
                                    <i class="fas fa-download text-xs"></i>
                                </button>
                                <button class="delete-conversation p-1 text-gray-400 hover:text-red-500" data-conversation-id="${conversation.id}" title="删除对话">
                                    <i class="fas fa-trash text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('');

                // 绑定事件
                this.bindConversationEvents(historyList);
            }

            bindConversationEvents(container) {
                // 点击对话项切换对话
                container.querySelectorAll('.conversation-item').forEach(item => {
                    item.addEventListener('click', (e) => {
                        if (!e.target.closest('button')) {
                            this.switchConversation(item.dataset.conversationId);
                        }
                    });
                });

                // 重命名对话
                container.querySelectorAll('.rename-conversation').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.renameConversation(btn.dataset.conversationId);
                    });
                });

                // 复制对话
                container.querySelectorAll('.duplicate-conversation').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.duplicateConversation(btn.dataset.conversationId);
                    });
                });

                // 导出对话
                container.querySelectorAll('.export-conversation').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.exportSingleConversation(btn.dataset.conversationId);
                    });
                });

                // 删除对话
                container.querySelectorAll('.delete-conversation').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        if (confirm('确定要删除这个对话吗？')) {
                            this.deleteConversation(btn.dataset.conversationId);
                        }
                    });
                });
            }

            renameConversation(conversationId) {
                const conversation = this.conversations.find(c => c.id === conversationId);
                if (!conversation) return;

                const newTitle = prompt('请输入新的对话标题:', conversation.title);
                if (newTitle && newTitle.trim() !== '') {
                    conversation.title = newTitle.trim();
                    conversation.updatedAt = new Date().toISOString();
                    this.saveConversations();
                    this.updateHistoryList();
                }
            }

            duplicateConversation(conversationId) {
                const conversation = this.conversations.find(c => c.id === conversationId);
                if (!conversation) return;

                const newConversation = {
                    id: Date.now().toString(),
                    title: conversation.title + ' (副本)',
                    messages: [...conversation.messages],
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };

                this.conversations.unshift(newConversation);
                this.saveConversations();
                this.updateHistoryList();
                this.showNotification('对话已复制', 'success');
            }

            exportSingleConversation(conversationId) {
                const conversation = this.conversations.find(c => c.id === conversationId);
                if (!conversation) return;

                const exportData = {
                    title: conversation.title,
                    createdAt: conversation.createdAt,
                    updatedAt: conversation.updatedAt,
                    messages: conversation.messages
                };

                this.downloadJSON(exportData, `${conversation.title}.json`);
            }

            // 响应式处理
            handleResize() {
                // 处理窗口大小变化
                this.adjustLayout();
            }

            adjustLayout() {
                const sidebar = document.getElementById('history-sidebar');
                if (window.innerWidth <= 768) {
                    // 移动端：确保侧边栏正确隐藏
                    if (sidebar && !sidebar.classList.contains('open')) {
                        sidebar.style.transform = 'translateX(-100%)';
                    }
                } else {
                    // 桌面端：显示侧边栏
                    if (sidebar) {
                        sidebar.style.transform = 'translateX(0)';
                        sidebar.classList.remove('open');
                    }
                }
            }

            toggleHistorySidebar() {
                const sidebar = document.getElementById('history-sidebar');
                if (sidebar) {
                    sidebar.classList.toggle('open');
                }
            }

            closeHistorySidebar() {
                const sidebar = document.getElementById('history-sidebar');
                if (sidebar) {
                    sidebar.classList.remove('open');
                }
            }

            // 消息处理
            async sendMessage() {
                if (this.isStreaming) return;

                const userInput = document.getElementById('user-input');
                const message = userInput ? userInput.value.trim() : '';

                if (!message && this.uploadedFiles.length === 0) return;
                if (!this.currentModel) {
                    this.showNotification('请先选择一个模型', 'warning');
                    return;
                }

                // 构建消息内容
                let messageContent = message;
                let hasImages = false;

                // 处理文件上传
                if (this.uploadedFiles.length > 0) {
                    for (const fileData of this.uploadedFiles) {
                        if (fileData.type.startsWith('image/')) {
                            hasImages = true;
                            // 这里可以添加图片处理逻辑
                        }
                    }
                }

                // 检查模型是否支持多模态
                if (hasImages && !this.isMultimodalModel(this.currentModel.name)) {
                    this.showNotification('当前模型不支持图片分析，请选择支持多模态的模型', 'warning');
                    return;
                }

                // 清空输入框
                if (userInput) {
                    userInput.value = '';
                    userInput.style.height = 'auto';
                }

                // 添加用户消息
                this.addMessage('user', messageContent);
                this.clearFiles();

                // 开始流式响应
                await this.streamAIResponse(messageContent);
            }

            async streamAIResponse(userMessage) {
                this.isStreaming = true;
                this.toggleSendButton(false);

                const startTime = Date.now();
                let aiMessageElement = null;
                let aiMessageContent = '';
                let charCount = 0;

                try {
                    // 添加思考指示器
                    this.addThinkingMessage();

                    // 调用AI API获取流式响应
                    const response = await this.callAIAPI(userMessage);

                    // 移除思考指示器，添加AI消息容器
                    this.removeThinkingMessage();
                    aiMessageElement = this.addMessage('ai', '', true); // 第三个参数表示这是流式消息

                    // 处理流式响应
                    if (response.body) {
                        const reader = response.body.getReader();
                        const decoder = new TextDecoder();

                        while (this.isStreaming) {
                            const { done, value } = await reader.read();
                            if (done) break;

                            const chunk = decoder.decode(value, { stream: true });
                            const lines = chunk.split('\n').filter(line => line.trim());

                            for (const line of lines) {
                                if (!this.isStreaming) break;

                                try {
                                    let content = '';

                                    if (this.currentService === 'ollama') {
                                        // Ollama格式: {"response": "text", "done": false}
                                        if (line.startsWith('data: ')) {
                                            const jsonStr = line.slice(6);
                                            const data = JSON.parse(jsonStr);
                                            content = data.response || '';
                                        } else {
                                            const data = JSON.parse(line);
                                            content = data.response || '';
                                        }
                                    } else if (this.currentService === 'lmstudio') {
                                        // LM Studio格式: {"choices": [{"delta": {"content": "text"}}]}
                                        if (line.startsWith('data: ')) {
                                            const jsonStr = line.slice(6);
                                            if (jsonStr === '[DONE]') break;
                                            const data = JSON.parse(jsonStr);
                                            content = data.choices?.[0]?.delta?.content || '';
                                        }
                                    }

                                    if (content) {
                                        aiMessageContent += content;
                                        charCount += content.length;

                                        // 更新消息内容
                                        if (aiMessageElement) {
                                            const contentElement = aiMessageElement.querySelector('.message-content');
                                            if (contentElement) {
                                                contentElement.innerHTML = this.formatMessage(aiMessageContent) + '<span class="streaming-cursor"></span>';
                                            }
                                        }

                                        // 滚动到底部
                                        this.scrollToBottom();
                                    }
                                } catch (parseError) {
                                    console.warn('解析流式数据失败:', parseError, line);
                                }
                            }
                        }
                    } else {
                        // 如果不支持流式，回退到普通模式
                        const data = await response.json();
                        const fullResponse = data.response || data.choices?.[0]?.message?.content || '无响应内容';

                        // 模拟打字机效果
                        for (let i = 0; i < fullResponse.length; i++) {
                            if (!this.isStreaming) break;

                            aiMessageContent += fullResponse[i];
                            charCount++;

                            if (aiMessageElement) {
                                const contentElement = aiMessageElement.querySelector('.message-content');
                                if (contentElement) {
                                    contentElement.innerHTML = this.formatMessage(aiMessageContent) + '<span class="streaming-cursor"></span>';
                                }
                            }

                            this.scrollToBottom();
                            await this.delay(30 + Math.random() * 20);
                        }
                    }

                    // 移除光标，完成输出
                    if (aiMessageElement) {
                        const contentElement = aiMessageElement.querySelector('.message-content');
                        if (contentElement) {
                            contentElement.innerHTML = this.formatMessage(aiMessageContent);
                        }
                    }

                    // 计算统计信息
                    const responseTime = Date.now() - startTime;
                    const outputSpeed = charCount / (responseTime / 1000);

                    this.stats.lastResponseTime = responseTime;
                    this.stats.lastOutputSpeed = outputSpeed;
                    this.stats.messageCount += 2; // 用户消息 + AI消息

                    // 保存消息到历史
                    this.messageHistory.push(
                        { role: 'user', content: userMessage, timestamp: new Date().toISOString() },
                        { role: 'ai', content: aiMessageContent, timestamp: new Date().toISOString() }
                    );

                    this.updateCurrentConversation();
                    this.updateStats();

                } catch (error) {
                    console.error('AI响应失败:', error);
                    this.removeThinkingMessage();
                    this.addMessage('ai', `抱歉，发生了错误: ${error.message}`);
                    this.showNotification('发送失败: ' + error.message, 'error');
                } finally {
                    this.isStreaming = false;
                    this.toggleSendButton(true);
                    this.currentStreamingMessage = null;
                }
            }

            async callAIAPI(message) {
                // 这里实现实际的AI API调用
                // 根据当前选择的服务和模型进行调用
                if (!this.currentService || !this.currentModel) {
                    throw new Error('未选择AI服务或模型');
                }

                const serviceConfig = this.services[this.currentService];

                // 根据服务类型选择不同的API端点
                let apiUrl, requestBody;

                if (this.currentService === 'ollama') {
                    apiUrl = `${serviceConfig.url}/api/generate`;
                    requestBody = {
                        model: this.currentModel.name,
                        prompt: message,
                        stream: true // 启用流式输出
                    };
                } else if (this.currentService === 'lmstudio') {
                    apiUrl = `${serviceConfig.url}/v1/chat/completions`;
                    requestBody = {
                        model: this.currentModel.name,
                        messages: [{ role: 'user', content: message }],
                        stream: true,
                        temperature: 0.7
                    };
                }

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    throw new Error(`API请求失败: ${response.status}`);
                }

                return response;
            }

            stopStreaming() {
                this.isStreaming = false;
                this.toggleSendButton(true);
                console.log('流式输出已停止');
            }

            toggleSendButton(showSend) {
                const sendBtn = document.getElementById('send-btn');
                const stopBtn = document.getElementById('stop-btn');

                if (showSend) {
                    if (sendBtn) sendBtn.classList.remove('hidden');
                    if (stopBtn) stopBtn.classList.add('hidden');
                } else {
                    if (sendBtn) sendBtn.classList.add('hidden');
                    if (stopBtn) stopBtn.classList.remove('hidden');
                }
            }

            addThinkingMessage() {
                const chatMessages = document.getElementById('chat-messages');
                if (!chatMessages) return;

                const thinkingDiv = document.createElement('div');
                thinkingDiv.id = 'thinking-message';
                thinkingDiv.className = 'message-container flex justify-start mb-4';
                thinkingDiv.innerHTML = `
                    <div class="ai-message rounded-lg px-4 py-3 shadow-sm">
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-gray-600">AI正在思考</span>
                            <div class="thinking-dots">
                                <div class="thinking-dot"></div>
                                <div class="thinking-dot"></div>
                                <div class="thinking-dot"></div>
                            </div>
                        </div>
                    </div>
                `;

                chatMessages.appendChild(thinkingDiv);
                this.scrollToBottom();
            }

            removeThinkingMessage() {
                const thinkingMessage = document.getElementById('thinking-message');
                if (thinkingMessage) {
                    thinkingMessage.remove();
                }
            }

            addMessage(sender, content, isStreaming = false) {
                const chatMessages = document.getElementById('chat-messages');
                if (!chatMessages) return null;

                // 清除欢迎消息
                const welcomeMessage = chatMessages.querySelector('.text-center');
                if (welcomeMessage && welcomeMessage.textContent.includes('开始您的AI对话')) {
                    welcomeMessage.remove();
                }

                const messageDiv = document.createElement('div');
                messageDiv.className = `message-container flex ${sender === 'user' ? 'justify-end' : 'justify-start'} mb-4`;

                const messageClass = sender === 'user' ? 'user-message' : 'ai-message';
                const formattedContent = this.formatMessage(content);

                messageDiv.innerHTML = `
                    <div class="${messageClass} rounded-lg px-4 py-3 shadow-sm relative">
                        <div class="message-content">${formattedContent}</div>
                        <div class="message-actions">
                            <button class="action-btn copy-btn" title="复制">
                                <i class="fas fa-copy"></i>
                            </button>
                            ${sender === 'ai' ? '<button class="action-btn regenerate-btn" title="重新生成"><i class="fas fa-redo"></i></button>' : ''}
                            <button class="action-btn delete-btn" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;

                chatMessages.appendChild(messageDiv);

                // 绑定消息操作事件
                this.bindMessageActions(messageDiv);

                this.scrollToBottom();
                return messageDiv;
            }

            bindMessageActions(messageElement) {
                const copyBtn = messageElement.querySelector('.copy-btn');
                const regenerateBtn = messageElement.querySelector('.regenerate-btn');
                const deleteBtn = messageElement.querySelector('.delete-btn');

                if (copyBtn) {
                    copyBtn.addEventListener('click', () => {
                        const content = messageElement.querySelector('.message-content').textContent;
                        navigator.clipboard.writeText(content).then(() => {
                            this.showNotification('已复制到剪贴板', 'success');
                        });
                    });
                }

                if (regenerateBtn) {
                    regenerateBtn.addEventListener('click', () => {
                        // 重新生成逻辑
                        this.regenerateLastResponse();
                    });
                }

                if (deleteBtn) {
                    deleteBtn.addEventListener('click', () => {
                        if (confirm('确定要删除这条消息吗？')) {
                            messageElement.remove();
                            // 这里可以添加从历史记录中删除的逻辑
                        }
                    });
                }
            }

            formatMessage(content) {
                // 简单的Markdown渲染
                return content
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/`(.*?)`/g, '<code class="bg-gray-100 px-1 rounded">$1</code>')
                    .replace(/\n/g, '<br>');
            }

            updateChatMessages() {
                const chatMessages = document.getElementById('chat-messages');
                if (!chatMessages) return;

                if (this.messageHistory.length === 0) {
                    chatMessages.innerHTML = `
                        <div class="text-center text-gray-500 py-8">
                            <i class="fas fa-comments text-4xl mb-4"></i>
                            <p class="text-lg mb-2">开始您的AI对话</p>
                            <p class="text-sm">发送消息开始聊天，或点击设置配置AI服务</p>
                        </div>
                    `;
                    return;
                }

                chatMessages.innerHTML = '';
                this.messageHistory.forEach(msg => {
                    this.addMessage(msg.role === 'user' ? 'user' : 'ai', msg.content);
                });
            }

            scrollToBottom() {
                const chatMessages = document.getElementById('chat-messages');
                if (chatMessages) {
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }

            // 文件处理
            handleFileUpload(event) {
                const files = Array.from(event.target.files);

                files.forEach(file => {
                    if (file.size > this.maxFileSize) {
                        this.showNotification(`文件 ${file.name} 超过大小限制 (10MB)`, 'error');
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = (e) => {
                        const fileData = {
                            name: file.name,
                            type: file.type,
                            size: file.size,
                            content: e.target.result,
                            preview: null
                        };

                        // 为图片生成预览
                        if (file.type.startsWith('image/')) {
                            fileData.preview = e.target.result;
                        }

                        this.uploadedFiles.push(fileData);
                        this.updateFilePreview();
                    };

                    if (file.type.startsWith('image/')) {
                        reader.readAsDataURL(file);
                    } else {
                        reader.readAsText(file);
                    }
                });

                // 清空input
                event.target.value = '';
            }

            updateFilePreview() {
                const previewArea = document.getElementById('file-preview-area');
                const fileList = document.getElementById('file-list');

                if (this.uploadedFiles.length === 0) {
                    if (previewArea) previewArea.classList.add('hidden');
                    return;
                }

                if (previewArea) previewArea.classList.remove('hidden');
                if (!fileList) return;

                fileList.innerHTML = this.uploadedFiles.map((file, index) => `
                    <div class="flex items-center space-x-3 p-2 bg-gray-50 rounded border">
                        ${file.preview ?
                            `<img src="${file.preview}" alt="${file.name}" class="w-12 h-12 object-cover rounded">` :
                            `<div class="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
                                <i class="fas fa-file text-gray-500"></i>
                            </div>`
                        }
                        <div class="flex-1 min-w-0">
                            <div class="text-sm font-medium text-gray-900 text-ellipsis">${file.name}</div>
                            <div class="text-xs text-gray-500">${this.formatFileSize(file.size)}</div>
                        </div>
                        <button class="text-red-500 hover:text-red-700" onclick="responsiveChatApp.removeFile(${index})">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `).join('');
            }

            removeFile(index) {
                this.uploadedFiles.splice(index, 1);
                this.updateFilePreview();
            }

            clearFiles() {
                this.uploadedFiles = [];
                this.updateFilePreview();
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // 设置界面
            openSettings() {
                this.createSettingsModal();
            }

            createSettingsModal() {
                // 移除现有模态框
                const existingModal = document.getElementById('settings-modal');
                if (existingModal) existingModal.remove();

                const modal = document.createElement('div');
                modal.id = 'settings-modal';
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
                modal.innerHTML = `
                    <div class="modal-content bg-white rounded-lg shadow-xl">
                        <div class="flex items-center justify-between p-6 border-b border-gray-200">
                            <h2 class="text-xl font-semibold text-gray-800">设置</h2>
                            <button id="close-settings" class="text-gray-500 hover:text-gray-700">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>

                        <div class="p-6 space-y-6">
                            <!-- 服务配置 -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-800 mb-4">AI服务配置</h3>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">选择模型</label>
                                        <select id="model-select" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option value="">请选择模型</option>
                                            ${this.discoveredModels.map(model => `
                                                <option value="${model.id}" ${this.currentModel && this.currentModel.id === model.id ? 'selected' : ''}>
                                                    ${model.name} (${model.serviceName})
                                                </option>
                                            `).join('')}
                                        </select>
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Ollama URL</label>
                                            <input type="text" id="ollama-url" value="${this.services.ollama.url}"
                                                   class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">LM Studio URL</label>
                                            <input type="text" id="lmstudio-url" value="${this.services.lmstudio.url}"
                                                   class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                    </div>

                                    <button id="refresh-services" class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                                        <i class="fas fa-sync-alt mr-2"></i>重新检测服务
                                    </button>
                                </div>
                            </div>

                            <!-- 聊天设置 -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-800 mb-4">聊天设置</h3>
                                <div class="space-y-4">
                                    <div>
                                        <label class="flex items-center">
                                            <input type="checkbox" id="auto-scroll" checked class="mr-2">
                                            <span class="text-sm text-gray-700">自动滚动到底部</span>
                                        </label>
                                    </div>
                                    <div>
                                        <label class="flex items-center">
                                            <input type="checkbox" id="show-timestamps" class="mr-2">
                                            <span class="text-sm text-gray-700">显示消息时间戳</span>
                                        </label>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">最大文件大小 (MB)</label>
                                        <input type="number" id="max-file-size" value="${this.maxFileSize / 1024 / 1024}" min="1" max="100"
                                               class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>
                                </div>
                            </div>

                            <!-- 导出功能 -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-800 mb-4">数据管理</h3>
                                <div class="space-y-2">
                                    <button id="export-current" class="w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                                        <i class="fas fa-download mr-2"></i>导出当前对话
                                    </button>
                                    <button id="export-all" class="w-full px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
                                        <i class="fas fa-download mr-2"></i>导出所有对话
                                    </button>
                                    <button id="clear-history" class="w-full px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors">
                                        <i class="fas fa-trash mr-2"></i>清除所有历史
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3 p-6 border-t border-gray-200">
                            <button id="cancel-settings" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                                取消
                            </button>
                            <button id="save-settings" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                                保存设置
                            </button>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);
                this.bindSettingsEvents();
            }

            bindSettingsEvents() {
                const modal = document.getElementById('settings-modal');
                const closeBtn = document.getElementById('close-settings');
                const cancelBtn = document.getElementById('cancel-settings');
                const saveBtn = document.getElementById('save-settings');
                const refreshBtn = document.getElementById('refresh-services');
                const modelSelect = document.getElementById('model-select');

                // 关闭模态框
                [closeBtn, cancelBtn].forEach(btn => {
                    if (btn) {
                        btn.addEventListener('click', () => modal.remove());
                    }
                });

                // 点击背景关闭
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) modal.remove();
                });

                // 保存设置
                if (saveBtn) {
                    saveBtn.addEventListener('click', () => this.saveSettings());
                }

                // 重新检测服务
                if (refreshBtn) {
                    refreshBtn.addEventListener('click', async () => {
                        refreshBtn.disabled = true;
                        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>检测中...';

                        await this.detectServices();
                        this.updateUI();
                        modal.remove();
                        this.createSettingsModal(); // 重新创建以更新模型列表

                        refreshBtn.disabled = false;
                        refreshBtn.innerHTML = '<i class="fas fa-sync-alt mr-2"></i>重新检测服务';
                    });
                }

                // 模型选择
                if (modelSelect) {
                    modelSelect.addEventListener('change', (e) => {
                        const selectedModel = this.discoveredModels.find(m => m.id === e.target.value);
                        if (selectedModel) {
                            this.currentModel = selectedModel;
                            this.currentService = selectedModel.service;
                            this.updateModelInfo();
                        }
                    });
                }

                // 导出功能
                const exportCurrentBtn = document.getElementById('export-current');
                const exportAllBtn = document.getElementById('export-all');
                const clearHistoryBtn = document.getElementById('clear-history');

                if (exportCurrentBtn) {
                    exportCurrentBtn.addEventListener('click', () => this.exportConversation());
                }

                if (exportAllBtn) {
                    exportAllBtn.addEventListener('click', () => this.exportAllConversations());
                }

                if (clearHistoryBtn) {
                    clearHistoryBtn.addEventListener('click', () => {
                        if (confirm('确定要清除所有对话历史吗？此操作不可恢复。')) {
                            this.clearAllHistory();
                            modal.remove();
                        }
                    });
                }
            }

            saveSettings() {
                const modal = document.getElementById('settings-modal');

                // 保存服务URL
                const ollamaUrl = document.getElementById('ollama-url')?.value;
                const lmstudioUrl = document.getElementById('lmstudio-url')?.value;

                if (ollamaUrl) this.services.ollama.url = ollamaUrl;
                if (lmstudioUrl) this.services.lmstudio.url = lmstudioUrl;

                // 保存其他设置
                const maxFileSize = document.getElementById('max-file-size')?.value;
                if (maxFileSize) {
                    this.maxFileSize = parseInt(maxFileSize) * 1024 * 1024;
                }

                // 保存到localStorage
                localStorage.setItem('chat-settings', JSON.stringify({
                    services: this.services,
                    maxFileSize: this.maxFileSize,
                    currentModel: this.currentModel,
                    currentService: this.currentService
                }));

                this.showNotification('设置已保存', 'success');
                modal.remove();
            }

            // 工具函数
            isMultimodalModel(modelName) {
                const multimodalKeywords = ['vision', 'vl', 'visual', 'multimodal', 'llava', 'qwen2-vl'];
                return multimodalKeywords.some(keyword =>
                    modelName.toLowerCase().includes(keyword)
                );
            }

            showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white shadow-lg transition-all duration-300 ${
                    type === 'success' ? 'bg-green-500' :
                    type === 'error' ? 'bg-red-500' :
                    type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
                }`;
                notification.textContent = message;

                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.style.opacity = '0';
                    notification.style.transform = 'translateX(100%)';
                    setTimeout(() => notification.remove(), 300);
                }, 3000);
            }

            regenerateLastResponse() {
                // 重新生成最后一个AI回复
                if (this.messageHistory.length >= 2) {
                    const lastUserMessage = this.messageHistory[this.messageHistory.length - 2];
                    if (lastUserMessage.role === 'user') {
                        // 移除最后一个AI回复
                        this.messageHistory.pop();
                        this.updateChatMessages();
                        // 重新发送
                        this.streamAIResponse(lastUserMessage.content);
                    }
                }
            }

            exportConversation() {
                const conversation = this.conversations.find(c => c.id === this.currentConversationId);
                if (!conversation) return;

                const exportData = {
                    title: conversation.title,
                    createdAt: conversation.createdAt,
                    updatedAt: conversation.updatedAt,
                    messages: conversation.messages
                };

                this.downloadJSON(exportData, `conversation-${conversation.title}.json`);
            }

            exportAllConversations() {
                const exportData = {
                    exportDate: new Date().toISOString(),
                    conversations: this.conversations
                };

                this.downloadJSON(exportData, 'all-conversations.json');
            }

            downloadJSON(data, filename) {
                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }

            clearAllHistory() {
                this.conversations = [];
                this.messageHistory = [];
                this.currentConversationId = null;
                this.stats.messageCount = 0;

                localStorage.removeItem('chat-conversations');

                this.createNewConversation();
                this.updateHistoryList();
                this.updateChatMessages();

                this.showNotification('所有历史记录已清除', 'success');
            }
        }

        // 启动应用
        document.addEventListener('DOMContentLoaded', () => {
            try {
                window.responsiveChatApp = new ResponsiveChatApp();
            } catch (error) {
                console.error('应用启动失败:', error);
            }
        });
    </script>
</body>
</html>
