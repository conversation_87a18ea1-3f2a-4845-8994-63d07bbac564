<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: #e9ecef;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>简单功能测试</h1>
        
        <div class="status" id="status">等待测试...</div>
        
        <button id="test-btn-1" onclick="testFunction1()">测试按钮1</button>
        <button id="test-btn-2" onclick="testFunction2()">测试按钮2</button>
        <button id="settings-test" onclick="openTestSettings()">设置测试</button>
        <button id="api-test" onclick="testAPI()">API连接测试</button>
        
        <div id="test-results" style="margin-top: 20px;"></div>
        
        <!-- 简单的设置弹窗 -->
        <div id="simple-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 8px; min-width: 300px;">
                <h3>设置测试</h3>
                <p>这是一个简单的设置弹窗测试</p>
                <button onclick="closeTestSettings()">关闭</button>
            </div>
        </div>
    </div>

    <script>
        console.log('简单测试页面加载完成');
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + type;
            console.log('状态更新:', message);
        }
        
        function testFunction1() {
            console.log('测试按钮1被点击');
            updateStatus('测试按钮1工作正常！', 'success');
            alert('测试按钮1工作正常！');
        }
        
        function testFunction2() {
            console.log('测试按钮2被点击');
            updateStatus('测试按钮2工作正常！', 'success');
            
            // 测试DOM操作
            const results = document.getElementById('test-results');
            results.innerHTML += '<div style="padding: 5px; background: #e7f3ff; margin: 5px 0; border-radius: 3px;">测试按钮2 - ' + new Date().toLocaleTimeString() + '</div>';
        }
        
        function openTestSettings() {
            console.log('设置按钮被点击');
            updateStatus('打开设置弹窗', 'success');
            document.getElementById('simple-modal').style.display = 'block';
        }
        
        function closeTestSettings() {
            console.log('关闭设置弹窗');
            document.getElementById('simple-modal').style.display = 'none';
            updateStatus('设置弹窗已关闭', 'success');
        }
        
        async function testAPI() {
            console.log('API测试开始');
            updateStatus('正在测试API连接...', 'info');
            
            try {
                // 测试Ollama连接
                const response = await fetch('http://localhost:11434/api/tags', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    updateStatus(`Ollama连接成功！找到 ${data.models?.length || 0} 个模型`, 'success');
                    console.log('Ollama响应:', data);
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error('API测试失败:', error);
                updateStatus(`API连接失败: ${error.message}`, 'error');
                
                // 尝试LM Studio
                try {
                    const lmResponse = await fetch('http://localhost:1234/v1/models', {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    if (lmResponse.ok) {
                        const lmData = await lmResponse.json();
                        updateStatus(`LM Studio连接成功！找到 ${lmData.data?.length || 0} 个模型`, 'success');
                        console.log('LM Studio响应:', lmData);
                    } else {
                        updateStatus('Ollama和LM Studio都无法连接', 'error');
                    }
                } catch (lmError) {
                    console.error('LM Studio测试也失败:', lmError);
                    updateStatus('所有API服务都无法连接', 'error');
                }
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成');
            updateStatus('页面初始化完成，所有按钮应该可以点击', 'success');
            
            // 检查关键元素
            const buttons = ['test-btn-1', 'test-btn-2', 'settings-test', 'api-test'];
            buttons.forEach(id => {
                const btn = document.getElementById(id);
                if (btn) {
                    console.log(`按钮 ${id} 存在且可访问`);
                } else {
                    console.error(`按钮 ${id} 不存在！`);
                }
            });
        });
        
        // 全局错误捕获
        window.addEventListener('error', function(e) {
            console.error('全局错误:', e.error);
            updateStatus(`JavaScript错误: ${e.message}`, 'error');
        });
        
        console.log('脚本加载完成');
    </script>
</body>
</html>
