<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>初始化测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .loading-screen {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #3b82f6, #1d4ed8, #3b82f6);
            background-size: 200% 100%;
            animation: progress-flow 2s linear infinite;
        }
        
        @keyframes progress-flow {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        .pulse-ring {
            animation: pulse-ring 1.25s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
        }
        
        .pulse-dot {
            animation: pulse-dot 1.25s cubic-bezier(0.455, 0.03, 0.515, 0.955) -.4s infinite;
        }
        
        @keyframes pulse-ring {
            0% { transform: scale(0.33); }
            80%, 100% { opacity: 0; }
        }
        
        @keyframes pulse-dot {
            0% { transform: scale(0.8); }
            50% { transform: scale(1); }
            100% { transform: scale(0.8); }
        }
    </style>
</head>
<body>
    <!-- 加载屏幕 -->
    <div id="loading-screen" class="loading-screen fixed inset-0 flex flex-col items-center justify-center text-white">
        <div class="text-center">
            <!-- Logo -->
            <div class="w-24 h-24 mx-auto mb-4 relative">
                <div class="absolute inset-0 bg-white rounded-full pulse-ring"></div>
                <div class="absolute inset-0 bg-white rounded-full pulse-dot flex items-center justify-center">
                    <i class="fas fa-robot text-4xl text-blue-600"></i>
                </div>
            </div>
            
            <h1 class="text-4xl font-bold mb-2">AI聊天助手</h1>
            <p class="text-xl opacity-90 mb-8">智能服务发现与初始化</p>
            
            <!-- 进度条 -->
            <div class="w-80 mx-auto mb-8">
                <div class="flex justify-between text-sm mb-2">
                    <span>初始化进度</span>
                    <span id="progress-percent">0%</span>
                </div>
                <div class="w-full bg-white/20 rounded-full h-3">
                    <div id="progress-bar" class="progress-bar h-3 rounded-full transition-all duration-500" style="width: 0%"></div>
                </div>
            </div>
            
            <!-- 状态文本 -->
            <div id="status-message" class="text-lg mb-8">正在启动系统...</div>
            
            <!-- 信息卡片 -->
            <div class="grid grid-cols-2 gap-4 max-w-md mx-auto">
                <div class="bg-white/10 rounded-lg p-4 text-center">
                    <div class="text-2xl mb-2">🔍</div>
                    <div class="text-sm font-semibold">AI服务状态</div>
                    <div id="service-status" class="text-xs mt-1">检测中...</div>
                </div>
                <div class="bg-white/10 rounded-lg p-4 text-center">
                    <div class="text-2xl mb-2">🤖</div>
                    <div class="text-sm font-semibold">发现模型</div>
                    <div id="model-count" class="text-xs mt-1">0 个</div>
                </div>
                <div class="bg-white/10 rounded-lg p-4 text-center">
                    <div class="text-2xl mb-2">💻</div>
                    <div class="text-sm font-semibold">系统信息</div>
                    <div id="system-info" class="text-xs mt-1">检测中...</div>
                </div>
                <div class="bg-white/10 rounded-lg p-4 text-center">
                    <div class="text-2xl mb-2">⚙️</div>
                    <div class="text-sm font-semibold">配置状态</div>
                    <div id="config-status" class="text-xs mt-1">加载中...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主应用 -->
    <div id="main-app" class="hidden min-h-screen bg-gray-100 p-8">
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-2xl shadow-lg p-8 text-center">
                <h1 class="text-3xl font-bold text-gray-800 mb-4">🎉 初始化完成！</h1>
                <p class="text-gray-600 mb-6">AI聊天助手已成功启动</p>
                <div id="init-results" class="text-left bg-gray-50 rounded-lg p-4">
                    <!-- 结果将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('脚本开始执行');
        
        class SimpleInitApp {
            constructor() {
                console.log('SimpleInitApp 构造函数开始');
                this.progress = 0;
                this.services = {
                    ollama: { name: 'Ollama', url: 'http://localhost:11434', endpoint: '/api/tags' },
                    lmstudio: { name: 'LM Studio', url: 'http://localhost:1234', endpoint: '/v1/models' }
                };
                this.discoveredModels = [];
                this.activeServices = [];
                
                console.log('开始延迟初始化');
                setTimeout(() => {
                    console.log('开始执行初始化');
                    this.startInit();
                }, 500);
            }
            
            async startInit() {
                console.log('startInit 方法开始');
                
                const steps = [
                    { name: '检测系统信息', progress: 25, action: () => this.detectSystem() },
                    { name: '扫描AI服务', progress: 50, action: () => this.scanServices() },
                    { name: '收集模型信息', progress: 75, action: () => this.collectModels() },
                    { name: '完成初始化', progress: 100, action: () => this.finishInit() }
                ];
                
                for (const step of steps) {
                    console.log(`执行步骤: ${step.name}`);
                    this.updateProgress(step.name, step.progress);
                    
                    try {
                        await step.action();
                        await this.delay(1000);
                    } catch (error) {
                        console.error(`步骤失败: ${step.name}`, error);
                    }
                }
                
                this.complete();
            }
            
            updateProgress(message, progress) {
                console.log(`更新进度: ${progress}% - ${message}`);
                document.getElementById('status-message').textContent = message;
                document.getElementById('progress-percent').textContent = `${progress}%`;
                document.getElementById('progress-bar').style.width = `${progress}%`;
            }
            
            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
            
            async detectSystem() {
                console.log('检测系统信息');
                const os = navigator.userAgent.includes('Mac') ? 'macOS' : 
                          navigator.userAgent.includes('Windows') ? 'Windows' : 'Linux';
                document.getElementById('system-info').textContent = os;
                document.getElementById('config-status').textContent = '已加载';
            }
            
            async scanServices() {
                console.log('扫描AI服务');
                this.activeServices = [];
                
                for (const [key, service] of Object.entries(this.services)) {
                    console.log(`检测服务: ${service.name}`);
                    try {
                        const response = await fetch(service.url + service.endpoint, {
                            method: 'GET',
                            headers: { 'Content-Type': 'application/json' }
                        });
                        
                        if (response.ok) {
                            this.activeServices.push(key);
                            console.log(`✅ ${service.name} 在线`);
                        } else {
                            console.log(`❌ ${service.name} 离线`);
                        }
                    } catch (error) {
                        console.log(`❌ ${service.name} 连接失败:`, error.message);
                    }
                }
                
                document.getElementById('service-status').textContent = 
                    `${this.activeServices.length} 个在线`;
            }
            
            async collectModels() {
                console.log('收集模型信息');
                this.discoveredModels = [];
                
                for (const serviceKey of this.activeServices) {
                    const service = this.services[serviceKey];
                    try {
                        const response = await fetch(service.url + service.endpoint);
                        if (response.ok) {
                            const data = await response.json();
                            
                            if (serviceKey === 'ollama' && data.models) {
                                this.discoveredModels.push(...data.models.map(m => ({
                                    name: m.name,
                                    service: serviceKey
                                })));
                            } else if (serviceKey === 'lmstudio' && data.data) {
                                this.discoveredModels.push(...data.data.map(m => ({
                                    name: m.id,
                                    service: serviceKey
                                })));
                            }
                        }
                    } catch (error) {
                        console.error(`获取 ${service.name} 模型失败:`, error);
                    }
                }
                
                document.getElementById('model-count').textContent = 
                    `${this.discoveredModels.length} 个`;
            }
            
            async finishInit() {
                console.log('完成初始化');
                const results = document.getElementById('init-results');
                results.innerHTML = `
                    <h3 class="font-bold mb-4">初始化结果:</h3>
                    <div class="space-y-2">
                        <div><strong>活跃服务:</strong> ${this.activeServices.length} 个</div>
                        <div><strong>发现模型:</strong> ${this.discoveredModels.length} 个</div>
                        <div><strong>服务列表:</strong></div>
                        <ul class="ml-4">
                            ${this.activeServices.map(key => `<li>• ${this.services[key].name}</li>`).join('')}
                        </ul>
                        ${this.discoveredModels.length > 0 ? `
                            <div><strong>模型列表:</strong></div>
                            <ul class="ml-4 max-h-32 overflow-y-auto">
                                ${this.discoveredModels.slice(0, 10).map(model => 
                                    `<li>• ${model.name} (${this.services[model.service].name})</li>`
                                ).join('')}
                                ${this.discoveredModels.length > 10 ? `<li>... 还有 ${this.discoveredModels.length - 10} 个模型</li>` : ''}
                            </ul>
                        ` : ''}
                    </div>
                `;
            }
            
            complete() {
                console.log('切换到主界面');
                document.getElementById('loading-screen').style.display = 'none';
                document.getElementById('main-app').classList.remove('hidden');
            }
        }
        
        // 启动应用
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM加载完成，启动应用');
            try {
                window.app = new SimpleInitApp();
                console.log('应用启动成功');
            } catch (error) {
                console.error('应用启动失败:', error);
                document.getElementById('status-message').textContent = '启动失败: ' + error.message;
            }
        });
        
        console.log('脚本执行完成');
    </script>
</body>
</html>
