<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI聊天助手 - 调试版</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .loading-screen {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #3b82f6, #1d4ed8, #3b82f6);
            background-size: 200% 100%;
            animation: progress-flow 2s linear infinite;
        }
        
        @keyframes progress-flow {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        .pulse-ring {
            animation: pulse-ring 1.25s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
        }
        
        .pulse-dot {
            animation: pulse-dot 1.25s cubic-bezier(0.455, 0.03, 0.515, 0.955) -.4s infinite;
        }
        
        @keyframes pulse-ring {
            0% { transform: scale(0.33); }
            80%, 100% { opacity: 0; }
        }
        
        @keyframes pulse-dot {
            0% { transform: scale(0.8); }
            50% { transform: scale(1); }
            100% { transform: scale(0.8); }
        }
    </style>
</head>
<body>
    <!-- 加载屏幕 -->
    <div id="loading-screen" class="loading-screen">
        <div class="text-center text-white">
            <!-- Logo区域 -->
            <div class="mb-8">
                <div class="w-24 h-24 mx-auto mb-4 relative">
                    <div class="absolute inset-0 bg-white rounded-full pulse-ring"></div>
                    <div class="absolute inset-0 bg-white rounded-full pulse-dot flex items-center justify-center">
                        <i class="fas fa-robot text-4xl text-blue-600"></i>
                    </div>
                </div>
                <h1 class="text-4xl font-bold mb-2">AI聊天助手</h1>
                <p class="text-xl opacity-90">智能服务发现与初始化</p>
            </div>
            
            <!-- 进度条 -->
            <div class="w-80 mx-auto mb-8">
                <div class="flex justify-between text-sm mb-2">
                    <span>初始化进度</span>
                    <span id="progress-percent">0%</span>
                </div>
                <div class="w-full bg-white/20 rounded-full h-3">
                    <div id="progress-bar" class="progress-bar h-3 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
            </div>
            
            <!-- 状态文本 -->
            <div id="status-message" class="text-lg mb-8 min-h-[2rem]">正在启动系统...</div>
            
            <!-- 调试信息 -->
            <div class="bg-black/20 rounded-lg p-4 text-left text-sm max-w-md mx-auto">
                <h3 class="font-bold mb-2">调试信息:</h3>
                <div id="debug-log" class="space-y-1 max-h-32 overflow-y-auto">
                    <div>等待初始化...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主应用界面 -->
    <div id="main-app" class="hidden min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-2xl shadow-lg p-8 text-center">
                <h1 class="text-3xl font-bold text-gray-800 mb-4">🎉 初始化完成！</h1>
                <p class="text-gray-600 mb-6">AI聊天助手已成功启动</p>
                <div id="results" class="text-left bg-gray-50 rounded-lg p-4">
                    <h3 class="font-bold mb-2">检测结果:</h3>
                    <div id="service-results"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class DebugChatApp {
            constructor() {
                this.log('构造函数开始');
                this.services = {
                    ollama: { name: 'Ollama', url: 'http://localhost:11434', endpoint: '/api/tags' },
                    lmstudio: { name: 'LM Studio', url: 'http://localhost:1234', endpoint: '/v1/models' }
                };
                this.discoveredModels = [];
                this.activeServices = [];
                this.log('构造函数完成，开始初始化');
                this.init();
            }
            
            log(message) {
                console.log('[DebugChatApp]', message);
                const debugLog = document.getElementById('debug-log');
                if (debugLog) {
                    const logEntry = document.createElement('div');
                    logEntry.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
                    debugLog.appendChild(logEntry);
                    debugLog.scrollTop = debugLog.scrollHeight;
                }
            }
            
            async init() {
                this.log('开始初始化流程');
                try {
                    await this.startInitialization();
                } catch (error) {
                    this.log('初始化失败: ' + error.message);
                    console.error('初始化错误:', error);
                }
            }
            
            async startInitialization() {
                const steps = [
                    { name: '检测系统信息', progress: 20, action: () => this.detectSystemInfo() },
                    { name: '扫描AI服务', progress: 50, action: () => this.scanServices() },
                    { name: '收集模型信息', progress: 80, action: () => this.collectModels() },
                    { name: '完成初始化', progress: 100, action: () => this.finishInit() }
                ];
                
                for (let i = 0; i < steps.length; i++) {
                    const step = steps[i];
                    this.log(`执行步骤: ${step.name}`);
                    this.updateProgress(step.name, step.progress);
                    
                    try {
                        await step.action();
                        await this.delay(1000);
                        this.log(`步骤完成: ${step.name}`);
                    } catch (error) {
                        this.log(`步骤失败: ${step.name} - ${error.message}`);
                    }
                }
                
                this.completeInitialization();
            }
            
            updateProgress(message, progress) {
                this.log(`更新进度: ${progress}% - ${message}`);
                document.getElementById('status-message').textContent = message;
                document.getElementById('progress-percent').textContent = `${progress}%`;
                document.getElementById('progress-bar').style.width = `${progress}%`;
            }
            
            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
            
            async detectSystemInfo() {
                this.log('检测操作系统和浏览器');
                const os = navigator.userAgent.includes('Mac') ? 'macOS' : 
                          navigator.userAgent.includes('Windows') ? 'Windows' : 'Linux';
                this.log(`检测到操作系统: ${os}`);
            }
            
            async scanServices() {
                this.log('开始扫描AI服务');
                this.activeServices = [];
                
                for (const [key, service] of Object.entries(this.services)) {
                    this.log(`检测服务: ${service.name} (${service.url})`);
                    try {
                        const response = await fetch(service.url + service.endpoint, {
                            method: 'GET',
                            headers: { 'Content-Type': 'application/json' }
                        });
                        
                        if (response.ok) {
                            this.activeServices.push(key);
                            this.log(`✅ ${service.name} 在线`);
                        } else {
                            this.log(`❌ ${service.name} 响应错误: ${response.status}`);
                        }
                    } catch (error) {
                        this.log(`❌ ${service.name} 连接失败: ${error.message}`);
                    }
                }
                
                this.log(`发现 ${this.activeServices.length} 个活跃服务`);
            }
            
            async collectModels() {
                this.log('开始收集模型信息');
                this.discoveredModels = [];
                
                for (const serviceKey of this.activeServices) {
                    const service = this.services[serviceKey];
                    this.log(`从 ${service.name} 获取模型列表`);
                    
                    try {
                        const response = await fetch(service.url + service.endpoint);
                        if (response.ok) {
                            const data = await response.json();
                            let modelCount = 0;
                            
                            if (serviceKey === 'ollama' && data.models) {
                                modelCount = data.models.length;
                                this.discoveredModels.push(...data.models.map(m => ({
                                    name: m.name,
                                    service: serviceKey,
                                    serviceName: service.name
                                })));
                            } else if (serviceKey === 'lmstudio' && data.data) {
                                modelCount = data.data.length;
                                this.discoveredModels.push(...data.data.map(m => ({
                                    name: m.id,
                                    service: serviceKey,
                                    serviceName: service.name
                                })));
                            }
                            
                            this.log(`从 ${service.name} 发现 ${modelCount} 个模型`);
                        }
                    } catch (error) {
                        this.log(`获取 ${service.name} 模型失败: ${error.message}`);
                    }
                }
                
                this.log(`总共发现 ${this.discoveredModels.length} 个模型`);
            }
            
            async finishInit() {
                this.log('准备完成初始化');
                
                // 显示结果
                const resultsDiv = document.getElementById('service-results');
                resultsDiv.innerHTML = `
                    <div class="space-y-2">
                        <div><strong>活跃服务:</strong> ${this.activeServices.length}</div>
                        <div><strong>发现模型:</strong> ${this.discoveredModels.length}</div>
                        <div><strong>服务列表:</strong></div>
                        <ul class="ml-4">
                            ${this.activeServices.map(key => `<li>• ${this.services[key].name}</li>`).join('')}
                        </ul>
                        <div><strong>模型列表:</strong></div>
                        <ul class="ml-4 max-h-32 overflow-y-auto">
                            ${this.discoveredModels.map(model => `<li>• ${model.name} (${model.serviceName})</li>`).join('')}
                        </ul>
                    </div>
                `;
            }
            
            completeInitialization() {
                this.log('初始化完成，切换到主界面');
                document.getElementById('loading-screen').style.display = 'none';
                document.getElementById('main-app').classList.remove('hidden');
            }
        }
        
        // 启动应用
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM加载完成，启动调试应用...');
            try {
                window.debugApp = new DebugChatApp();
            } catch (error) {
                console.error('应用启动失败:', error);
                document.getElementById('status-message').textContent = '启动失败: ' + error.message;
            }
        });
    </script>
</body>
</html>
