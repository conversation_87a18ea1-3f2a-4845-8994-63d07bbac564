<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI聊天助手 - 智能服务发现</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 加载动画 */
        @keyframes pulse-ring {
            0% { transform: scale(0.33); }
            80%, 100% { opacity: 0; }
        }
        @keyframes pulse-dot {
            0% { transform: scale(0.8); }
            50% { transform: scale(1); }
            100% { transform: scale(0.8); }
        }
        .pulse-ring {
            animation: pulse-ring 1.25s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
        }
        .pulse-dot {
            animation: pulse-dot 1.25s cubic-bezier(0.455, 0.03, 0.515, 0.955) -.4s infinite;
        }
        
        /* 进度条动画 */
        .progress-bar {
            background: linear-gradient(90deg, #3b82f6, #1d4ed8, #3b82f6);
            background-size: 200% 100%;
            animation: progress-flow 2s linear infinite;
        }
        @keyframes progress-flow {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        /* 状态指示器 */
        .status-online { background: linear-gradient(45deg, #10b981, #34d399); }
        .status-offline { background: linear-gradient(45deg, #ef4444, #f87171); }
        .status-checking { background: linear-gradient(45deg, #f59e0b, #fbbf24); animation: pulse 2s infinite; }
        
        /* 消息气泡 */
        .user-message { background: linear-gradient(135deg, #3b82f6, #1d4ed8); }
        .ai-message { background: linear-gradient(135deg, #f3f4f6, #e5e7eb); }
        
        /* 思考动画 */
        .thinking-dot {
            width: 8px;
            height: 8px;
            background: #6b7280;
            border-radius: 50%;
            animation: thinking 1.4s infinite ease-in-out;
        }
        .thinking-dot:nth-child(1) { animation-delay: -0.32s; }
        .thinking-dot:nth-child(2) { animation-delay: -0.16s; }
        .thinking-dot:nth-child(3) { animation-delay: 0s; }
        @keyframes thinking {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }
        
        /* 卡片悬停效果 */
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        /* 渐变背景 */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        /* 加载屏幕 */
        .loading-screen {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            transition: opacity 0.5s ease-out;
        }
        
        .loading-screen.fade-out {
            opacity: 0;
            pointer-events: none;
        }
        
        /* 系统信息卡片 */
        .info-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        /* 服务状态徽章 */
        .service-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-weight: 600;
        }
        .service-ollama { background: #10b981; color: white; }
        .service-lmstudio { background: #3b82f6; color: white; }
        .service-openai { background: #f59e0b; color: white; }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    
    <!-- 加载屏幕 -->
    <div id="loading-screen" class="loading-screen">
        <div class="text-center text-white">
            <!-- Logo区域 -->
            <div class="mb-8">
                <div class="w-24 h-24 mx-auto mb-4 relative">
                    <div class="absolute inset-0 bg-white rounded-full pulse-ring"></div>
                    <div class="absolute inset-0 bg-white rounded-full pulse-dot flex items-center justify-center">
                        <i class="fas fa-robot text-4xl text-blue-600"></i>
                    </div>
                </div>
                <h1 class="text-4xl font-bold mb-2">AI聊天助手</h1>
                <p class="text-xl opacity-90">智能服务发现与初始化</p>
            </div>
            
            <!-- 进度条 -->
            <div class="w-80 mx-auto mb-8">
                <div class="flex justify-between text-sm mb-2">
                    <span>初始化进度</span>
                    <span id="progress-percent">0%</span>
                </div>
                <div class="w-full bg-white/20 rounded-full h-3">
                    <div id="progress-bar" class="progress-bar h-3 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
            </div>
            
            <!-- 状态文本 -->
            <div id="status-message" class="text-lg mb-8 min-h-[2rem]">正在启动系统...</div>
            
            <!-- 系统信息卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 w-full max-w-2xl mx-auto">
                <div class="info-card rounded-lg p-4">
                    <h3 class="font-semibold mb-2 flex items-center">
                        <i class="fas fa-server mr-2"></i>AI服务状态
                    </h3>
                    <div id="service-status" class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span>Ollama</span>
                            <span id="ollama-status" class="text-yellow-300">检测中...</span>
                        </div>
                        <div class="flex justify-between">
                            <span>LM Studio</span>
                            <span id="lmstudio-status" class="text-yellow-300">检测中...</span>
                        </div>
                    </div>
                </div>
                
                <div class="info-card rounded-lg p-4">
                    <h3 class="font-semibold mb-2 flex items-center">
                        <i class="fas fa-brain mr-2"></i>发现的模型
                    </h3>
                    <div id="model-count" class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span>总计模型</span>
                            <span id="total-models">0</span>
                        </div>
                        <div class="flex justify-between">
                            <span>可用服务</span>
                            <span id="available-services">0</span>
                        </div>
                    </div>
                </div>
                
                <div class="info-card rounded-lg p-4">
                    <h3 class="font-semibold mb-2 flex items-center">
                        <i class="fas fa-desktop mr-2"></i>系统信息
                    </h3>
                    <div id="system-info" class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span>操作系统</span>
                            <span id="os-info">检测中...</span>
                        </div>
                        <div class="flex justify-between">
                            <span>浏览器</span>
                            <span id="browser-info">检测中...</span>
                        </div>
                    </div>
                </div>
                
                <div class="info-card rounded-lg p-4">
                    <h3 class="font-semibold mb-2 flex items-center">
                        <i class="fas fa-cog mr-2"></i>配置状态
                    </h3>
                    <div id="config-status" class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span>本地配置</span>
                            <span id="local-config">加载中...</span>
                        </div>
                        <div class="flex justify-between">
                            <span>网络连接</span>
                            <span id="network-status">测试中...</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 使用指南 -->
            <div class="mt-8 text-sm opacity-75 max-w-md mx-auto">
                <p class="mb-2">🚀 <strong>功能特色</strong></p>
                <p class="text-xs leading-relaxed">
                    • 自动发现本地AI服务 • 统一模型管理 • 智能对话体验<br>
                    • 实时性能监控 • 多服务支持 • 响应式设计
                </p>
            </div>
        </div>
    </div>
    
    <!-- 主应用界面 -->
    <div id="main-app" class="hidden">
        <!-- 头部状态栏 -->
        <header class="bg-white/80 backdrop-blur-md border-b border-gray-200/50 px-4 py-3 sticky top-0 z-30">
            <div class="flex items-center justify-between max-w-6xl mx-auto">
                <div class="flex items-center space-x-4">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                        <i class="fas fa-robot text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-800">AI聊天助手</h1>
                        <div class="flex items-center space-x-3 text-sm">
                            <div class="flex items-center space-x-1">
                                <div id="connection-indicator" class="w-3 h-3 rounded-full status-offline"></div>
                                <span id="connection-text" class="text-gray-600">未连接</span>
                            </div>
                            <span class="text-gray-400">|</span>
                            <div class="flex items-center space-x-1">
                                <span id="current-model" class="text-gray-600">未选择模型</span>
                                <span id="model-service-badge" class="service-badge hidden">Ollama</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center space-x-2">
                    <button id="refresh-services" class="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors" title="刷新服务">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button id="settings-btn" class="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors" title="设置">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 系统信息面板 -->
        <div class="max-w-6xl mx-auto px-4 py-4">
            <div id="system-panel" class="bg-white/70 backdrop-blur-sm rounded-xl shadow-sm p-4 mb-4">
                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 text-sm">
                    <div class="text-center">
                        <div class="text-gray-500 mb-1">当前模型</div>
                        <div id="panel-current-model" class="font-semibold text-blue-600">未选择</div>
                    </div>
                    <div class="text-center">
                        <div class="text-gray-500 mb-1">可用模型</div>
                        <div id="panel-total-models" class="font-semibold text-green-600">0</div>
                    </div>
                    <div class="text-center">
                        <div class="text-gray-500 mb-1">活跃服务</div>
                        <div id="panel-active-services" class="font-semibold text-purple-600">0</div>
                    </div>
                    <div class="text-center">
                        <div class="text-gray-500 mb-1">响应时间</div>
                        <div id="panel-response-time" class="font-semibold text-orange-600">--</div>
                    </div>
                    <div class="text-center">
                        <div class="text-gray-500 mb-1">输出速度</div>
                        <div id="panel-output-speed" class="font-semibold text-pink-600">--</div>
                    </div>
                    <div class="text-center">
                        <div class="text-gray-500 mb-1">消息数量</div>
                        <div id="panel-message-count" class="font-semibold text-indigo-600">0</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 聊天区域 -->
        <main class="max-w-6xl mx-auto px-4 pb-6">
            <div id="chat-container" class="bg-white/70 backdrop-blur-sm rounded-2xl shadow-lg p-6 mb-6 min-h-96 max-h-96 overflow-y-auto space-y-4">
                <div id="welcome-message" class="text-center py-16">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-robot text-white text-2xl"></i>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-800 mb-2">欢迎使用AI聊天助手</h2>
                    <p class="text-gray-500 mb-4">已为您发现 <span id="welcome-model-count" class="font-semibold text-blue-600">0</span> 个可用模型</p>
                    <div class="flex justify-center space-x-3">
                        <button id="quick-start" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-rocket mr-2"></i>快速开始
                        </button>
                        <button id="view-models" class="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                            <i class="fas fa-list mr-2"></i>查看模型
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 输入区域 -->
            <div class="bg-white/70 backdrop-blur-sm rounded-2xl shadow-lg p-4">
                <div class="flex items-end space-x-3">
                    <div class="flex-1">
                        <textarea 
                            id="user-input" 
                            placeholder="输入您的消息..." 
                            class="w-full p-3 border border-gray-200 rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            rows="1"
                            style="min-height: 44px; max-height: 120px;"
                        ></textarea>
                    </div>
                    <button id="send-btn" class="bg-blue-600 text-white p-3 rounded-xl hover:bg-blue-700 transition-colors min-w-[44px] h-[44px] flex items-center justify-center">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 应用主类
        class AdvancedChatApp {
            constructor() {
                this.services = {
                    ollama: { name: 'Ollama', url: 'http://localhost:11434', endpoint: '/api/tags', type: 'ollama', models: [] },
                    lmstudio: { name: 'LM Studio', url: 'http://localhost:1234', endpoint: '/v1/models', type: 'lmstudio', models: [] }
                };
                
                this.discoveredModels = [];
                this.activeServices = [];
                this.currentModel = null;
                this.currentService = null;
                this.stats = {
                    messageCount: 0,
                    lastResponseTime: 0,
                    lastOutputSpeed: 0
                };
                
                this.init();
            }
            
            async init() {
                await this.startInitialization();
            }
            
            // 初始化流程
            async startInitialization() {
                const steps = [
                    { name: '检测系统信息', progress: 10, action: () => this.detectSystemInfo() },
                    { name: '扫描AI服务', progress: 30, action: () => this.scanServices() },
                    { name: '收集模型信息', progress: 60, action: () => this.collectModels() },
                    { name: '加载用户配置', progress: 80, action: () => this.loadUserConfig() },
                    { name: '初始化界面', progress: 100, action: () => this.initializeUI() }
                ];

                for (let i = 0; i < steps.length; i++) {
                    const step = steps[i];
                    this.updateLoadingStatus(step.name, step.progress);

                    try {
                        await step.action();
                        await this.delay(800); // 让用户看到进度
                    } catch (error) {
                        console.error(`步骤 "${step.name}" 执行失败:`, error);
                    }
                }

                // 完成初始化
                await this.delay(500);
                this.completeInitialization();
            }

            // 更新加载状态
            updateLoadingStatus(message, progress) {
                document.getElementById('status-message').textContent = message;
                document.getElementById('progress-percent').textContent = `${progress}%`;
                document.getElementById('progress-bar').style.width = `${progress}%`;
            }

            // 延迟函数
            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }

            // 检测系统信息
            async detectSystemInfo() {
                const osInfo = this.getOSInfo();
                const browserInfo = this.getBrowserInfo();

                document.getElementById('os-info').textContent = osInfo;
                document.getElementById('browser-info').textContent = browserInfo;
                document.getElementById('local-config').textContent = '已加载';
                document.getElementById('network-status').textContent = '正常';
            }

            // 获取操作系统信息
            getOSInfo() {
                const userAgent = navigator.userAgent;
                if (userAgent.includes('Windows')) return 'Windows';
                if (userAgent.includes('Mac')) return 'macOS';
                if (userAgent.includes('Linux')) return 'Linux';
                if (userAgent.includes('Android')) return 'Android';
                if (userAgent.includes('iOS')) return 'iOS';
                return '未知';
            }

            // 获取浏览器信息
            getBrowserInfo() {
                const userAgent = navigator.userAgent;
                if (userAgent.includes('Chrome')) return 'Chrome';
                if (userAgent.includes('Firefox')) return 'Firefox';
                if (userAgent.includes('Safari')) return 'Safari';
                if (userAgent.includes('Edge')) return 'Edge';
                return '未知';
            }

            // 扫描AI服务
            async scanServices() {
                this.activeServices = [];

                // 并发检测所有服务
                const serviceChecks = Object.keys(this.services).map(async (serviceKey) => {
                    const service = this.services[serviceKey];
                    try {
                        const response = await fetch(service.url + service.endpoint, {
                            method: 'GET',
                            headers: { 'Content-Type': 'application/json' }
                        });

                        if (response.ok) {
                            this.activeServices.push(serviceKey);
                            document.getElementById(`${serviceKey}-status`).textContent = '✅ 在线';
                            document.getElementById(`${serviceKey}-status`).className = 'text-green-300';
                            return { service: serviceKey, success: true, data: await response.json() };
                        } else {
                            throw new Error(`HTTP ${response.status}`);
                        }
                    } catch (error) {
                        document.getElementById(`${serviceKey}-status`).textContent = '❌ 离线';
                        document.getElementById(`${serviceKey}-status`).className = 'text-red-300';
                        return { service: serviceKey, success: false, error: error.message };
                    }
                });

                const results = await Promise.all(serviceChecks);

                // 更新服务计数
                document.getElementById('available-services').textContent = this.activeServices.length;

                return results;
            }

            // 收集模型信息
            async collectModels() {
                this.discoveredModels = [];

                for (const serviceKey of this.activeServices) {
                    const service = this.services[serviceKey];
                    try {
                        const response = await fetch(service.url + service.endpoint);
                        if (response.ok) {
                            const data = await response.json();
                            const models = this.parseModels(data, serviceKey);
                            this.services[serviceKey].models = models;
                            this.discoveredModels.push(...models);
                        }
                    } catch (error) {
                        console.error(`获取 ${service.name} 模型失败:`, error);
                    }
                }

                // 更新模型计数
                document.getElementById('total-models').textContent = this.discoveredModels.length;
            }

            // 解析不同服务的模型数据
            parseModels(data, serviceKey) {
                const service = this.services[serviceKey];
                const models = [];

                if (serviceKey === 'ollama' && data.models) {
                    data.models.forEach(model => {
                        models.push({
                            id: model.name,
                            name: model.name,
                            service: serviceKey,
                            serviceName: service.name,
                            size: model.size || 'Unknown',
                            modified: model.modified_at || null
                        });
                    });
                } else if (serviceKey === 'lmstudio' && data.data) {
                    data.data.forEach(model => {
                        models.push({
                            id: model.id,
                            name: model.id,
                            service: serviceKey,
                            serviceName: service.name,
                            size: 'Unknown',
                            modified: model.created || null
                        });
                    });
                }

                return models;
            }

            // 加载用户配置
            async loadUserConfig() {
                try {
                    const savedConfig = localStorage.getItem('advanced-chat-config');
                    if (savedConfig) {
                        const config = JSON.parse(savedConfig);
                        this.currentModel = config.currentModel || null;
                        this.currentService = config.currentService || null;
                    }
                } catch (error) {
                    console.error('加载配置失败:', error);
                }
            }

            // 初始化UI
            async initializeUI() {
                this.bindEvents();
                this.updateMainUI();
            }

            // 完成初始化
            completeInitialization() {
                document.getElementById('loading-screen').classList.add('fade-out');
                setTimeout(() => {
                    document.getElementById('loading-screen').style.display = 'none';
                    document.getElementById('main-app').classList.remove('hidden');
                }, 500);
            }

            // 绑定事件
            bindEvents() {
                // 刷新服务按钮
                document.getElementById('refresh-services').addEventListener('click', () => this.refreshServices());

                // 设置按钮
                document.getElementById('settings-btn').addEventListener('click', () => this.openSettings());

                // 快速开始按钮
                document.getElementById('quick-start').addEventListener('click', () => this.quickStart());

                // 查看模型按钮
                document.getElementById('view-models').addEventListener('click', () => this.showModelList());

                // 发送消息
                document.getElementById('send-btn').addEventListener('click', () => this.sendMessage());

                // 输入框事件
                const userInput = document.getElementById('user-input');
                userInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                userInput.addEventListener('input', () => {
                    userInput.style.height = 'auto';
                    userInput.style.height = Math.min(userInput.scrollHeight, 120) + 'px';
                });
            }

            // 更新主界面UI
            updateMainUI() {
                // 更新连接状态
                if (this.activeServices.length > 0) {
                    document.getElementById('connection-indicator').className = 'w-3 h-3 rounded-full status-online';
                    document.getElementById('connection-text').textContent = `已连接 ${this.activeServices.length} 个服务`;
                } else {
                    document.getElementById('connection-indicator').className = 'w-3 h-3 rounded-full status-offline';
                    document.getElementById('connection-text').textContent = '未连接';
                }

                // 更新模型信息
                if (this.currentModel) {
                    document.getElementById('current-model').textContent = this.currentModel.name;
                    const badge = document.getElementById('model-service-badge');
                    badge.textContent = this.currentModel.serviceName;
                    badge.className = `service-badge service-${this.currentModel.service}`;
                    badge.classList.remove('hidden');
                } else {
                    document.getElementById('current-model').textContent = '未选择模型';
                    document.getElementById('model-service-badge').classList.add('hidden');
                }

                // 更新系统面板
                document.getElementById('panel-current-model').textContent = this.currentModel ? this.currentModel.name : '未选择';
                document.getElementById('panel-total-models').textContent = this.discoveredModels.length;
                document.getElementById('panel-active-services').textContent = this.activeServices.length;
                document.getElementById('panel-response-time').textContent = this.stats.lastResponseTime ? `${this.stats.lastResponseTime}ms` : '--';
                document.getElementById('panel-output-speed').textContent = this.stats.lastOutputSpeed ? `${this.stats.lastOutputSpeed.toFixed(1)} 字符/秒` : '--';
                document.getElementById('panel-message-count').textContent = this.stats.messageCount;

                // 更新欢迎消息
                document.getElementById('welcome-model-count').textContent = this.discoveredModels.length;
            }

            // 刷新服务
            async refreshServices() {
                const refreshBtn = document.getElementById('refresh-services');
                const icon = refreshBtn.querySelector('i');

                // 显示加载状态
                icon.classList.add('fa-spin');
                refreshBtn.disabled = true;

                try {
                    await this.scanServices();
                    await this.collectModels();
                    this.updateMainUI();
                    this.showNotification('服务刷新完成', 'success');
                } catch (error) {
                    this.showNotification('刷新失败: ' + error.message, 'error');
                } finally {
                    icon.classList.remove('fa-spin');
                    refreshBtn.disabled = false;
                }
            }

            // 快速开始
            quickStart() {
                if (this.discoveredModels.length === 0) {
                    this.showNotification('未发现可用模型，请检查AI服务状态', 'warning');
                    return;
                }

                // 自动选择第一个可用模型
                this.currentModel = this.discoveredModels[0];
                this.currentService = this.currentModel.service;
                this.saveConfig();
                this.updateMainUI();
                this.hideWelcomeMessage();
                this.showNotification(`已选择模型: ${this.currentModel.name}`, 'success');
            }

            // 显示模型列表
            showModelList() {
                if (this.discoveredModels.length === 0) {
                    this.showNotification('未发现可用模型', 'warning');
                    return;
                }

                // 创建模型选择界面
                this.createModelSelectionModal();
            }

            // 隐藏欢迎消息
            hideWelcomeMessage() {
                const welcomeMessage = document.getElementById('welcome-message');
                if (welcomeMessage) {
                    welcomeMessage.style.display = 'none';
                }
            }

            // 保存配置
            saveConfig() {
                const config = {
                    currentModel: this.currentModel,
                    currentService: this.currentService,
                    lastUpdate: Date.now()
                };
                localStorage.setItem('advanced-chat-config', JSON.stringify(config));
            }

            // 显示通知
            showNotification(message, type = 'info') {
                // 简化版通知，可以后续扩展
                const colors = {
                    success: 'text-green-600',
                    error: 'text-red-600',
                    warning: 'text-yellow-600',
                    info: 'text-blue-600'
                };

                console.log(`[${type.toUpperCase()}] ${message}`);
                // 这里可以添加更复杂的通知UI
            }

            // 创建模型选择模态框
            createModelSelectionModal() {
                // 移除现有模态框
                const existingModal = document.getElementById('model-selection-modal');
                if (existingModal) {
                    existingModal.remove();
                }

                // 创建模态框HTML
                const modalHTML = `
                    <div id="model-selection-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[80vh] overflow-hidden">
                            <div class="p-6 border-b border-gray-200">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-xl font-bold text-gray-800">选择AI模型</h3>
                                    <button id="close-model-modal" class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-times text-xl"></i>
                                    </button>
                                </div>
                                <p class="text-gray-500 mt-2">发现 ${this.discoveredModels.length} 个可用模型</p>
                            </div>
                            <div class="p-6 max-h-96 overflow-y-auto">
                                <div class="space-y-3">
                                    ${this.discoveredModels.map(model => `
                                        <div class="model-item p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 cursor-pointer transition-all" data-model='${JSON.stringify(model)}'>
                                            <div class="flex items-center justify-between">
                                                <div class="flex-1">
                                                    <div class="flex items-center space-x-2">
                                                        <h4 class="font-semibold text-gray-800">${model.name}</h4>
                                                        <span class="service-badge service-${model.service}">${model.serviceName}</span>
                                                    </div>
                                                    <p class="text-sm text-gray-500 mt-1">
                                                        大小: ${this.formatSize(model.size)} |
                                                        服务: ${model.serviceName}
                                                    </p>
                                                </div>
                                                <div class="text-right">
                                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                                </div>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // 添加到页面
                document.body.insertAdjacentHTML('beforeend', modalHTML);

                // 绑定事件
                document.getElementById('close-model-modal').addEventListener('click', () => {
                    document.getElementById('model-selection-modal').remove();
                });

                // 模型选择事件
                document.querySelectorAll('.model-item').forEach(item => {
                    item.addEventListener('click', () => {
                        const modelData = JSON.parse(item.dataset.model);
                        this.selectModel(modelData);
                        document.getElementById('model-selection-modal').remove();
                    });
                });

                // 点击背景关闭
                document.getElementById('model-selection-modal').addEventListener('click', (e) => {
                    if (e.target.id === 'model-selection-modal') {
                        document.getElementById('model-selection-modal').remove();
                    }
                });
            }

            // 选择模型
            selectModel(model) {
                this.currentModel = model;
                this.currentService = model.service;
                this.saveConfig();
                this.updateMainUI();
                this.hideWelcomeMessage();
                this.showNotification(`已选择模型: ${model.name} (${model.serviceName})`, 'success');
            }

            // 格式化文件大小
            formatSize(size) {
                if (typeof size === 'number') {
                    const units = ['B', 'KB', 'MB', 'GB'];
                    let unitIndex = 0;
                    while (size >= 1024 && unitIndex < units.length - 1) {
                        size /= 1024;
                        unitIndex++;
                    }
                    return `${size.toFixed(1)} ${units[unitIndex]}`;
                }
                return size || 'Unknown';
            }

            // 发送消息
            async sendMessage() {
                const input = document.getElementById('user-input');
                const message = input.value.trim();

                if (!message) return;

                if (!this.currentModel) {
                    this.showNotification('请先选择一个AI模型', 'warning');
                    this.showModelList();
                    return;
                }

                input.value = '';
                input.style.height = 'auto';
                this.hideWelcomeMessage();

                // 添加用户消息
                this.addMessage('user', message);
                this.stats.messageCount++;

                // 添加AI思考消息
                const thinkingId = this.addMessage('assistant', '', true);

                const startTime = Date.now();

                try {
                    const service = this.services[this.currentService];
                    let response;

                    if (this.currentService === 'ollama') {
                        response = await fetch(`${service.url}/api/generate`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                model: this.currentModel.id,
                                prompt: message,
                                stream: false
                            })
                        });
                    } else if (this.currentService === 'lmstudio') {
                        response = await fetch(`${service.url}/v1/chat/completions`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                model: this.currentModel.id,
                                messages: [{ role: 'user', content: message }],
                                stream: false
                            })
                        });
                    }

                    const endTime = Date.now();
                    const responseTime = endTime - startTime;
                    this.stats.lastResponseTime = responseTime;

                    if (response.ok) {
                        const data = await response.json();
                        let aiResponse;

                        if (this.currentService === 'ollama') {
                            aiResponse = data.response || '抱歉，没有收到回复';
                        } else if (this.currentService === 'lmstudio') {
                            aiResponse = data.choices?.[0]?.message?.content || '抱歉，没有收到回复';
                        }

                        // 计算输出速度
                        const outputSpeed = aiResponse.length / (responseTime / 1000);
                        this.stats.lastOutputSpeed = outputSpeed;

                        this.updateMessage(thinkingId, aiResponse, false);
                        this.updateMainUI();
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                } catch (error) {
                    this.updateMessage(thinkingId, `❌ 发送失败: ${error.message}`, false);
                }
            }

            // 添加消息
            addMessage(role, content, isThinking = false) {
                const messageId = 'msg-' + Date.now();
                const messageDiv = document.createElement('div');
                messageDiv.id = messageId;
                messageDiv.className = `w-full ${role === 'user' ? 'flex justify-end' : 'flex justify-start'}`;

                const bubble = document.createElement('div');
                bubble.className = `max-w-xs sm:max-w-md lg:max-w-lg px-4 py-3 rounded-2xl ${
                    role === 'user'
                        ? 'user-message text-white rounded-br-md'
                        : 'ai-message text-gray-800 rounded-bl-md'
                }`;

                // 添加发送者标识
                const header = document.createElement('div');
                header.className = `text-xs font-semibold mb-2 ${role === 'user' ? 'text-blue-200' : 'text-gray-600'}`;
                header.textContent = role === 'user' ? '👤 您' : '🤖 AI助手';
                bubble.appendChild(header);

                // 添加消息内容
                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';

                if (isThinking) {
                    contentDiv.innerHTML = `
                        <div class="flex items-center space-x-2">
                            <span class="text-sm">正在思考</span>
                            <div class="flex space-x-1">
                                <div class="thinking-dot"></div>
                                <div class="thinking-dot"></div>
                                <div class="thinking-dot"></div>
                            </div>
                        </div>
                    `;
                } else {
                    contentDiv.innerHTML = `<div class="whitespace-pre-wrap break-words">${content}</div>`;
                }

                bubble.appendChild(contentDiv);

                // 添加时间戳
                const timestamp = document.createElement('div');
                timestamp.className = `text-xs mt-2 ${role === 'user' ? 'text-blue-200' : 'text-gray-500'}`;
                timestamp.textContent = new Date().toLocaleTimeString();
                bubble.appendChild(timestamp);

                messageDiv.appendChild(bubble);

                const chatContainer = document.getElementById('chat-container');
                chatContainer.appendChild(messageDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;

                return messageId;
            }

            // 更新消息
            updateMessage(messageId, newContent, isThinking = false) {
                const messageDiv = document.getElementById(messageId);
                if (messageDiv) {
                    const contentDiv = messageDiv.querySelector('.message-content');
                    const timestamp = messageDiv.querySelector('.text-xs:last-child');

                    if (isThinking) {
                        contentDiv.innerHTML = `
                            <div class="flex items-center space-x-2">
                                <span class="text-sm">正在思考</span>
                                <div class="flex space-x-1">
                                    <div class="thinking-dot"></div>
                                    <div class="thinking-dot"></div>
                                    <div class="thinking-dot"></div>
                                </div>
                            </div>
                        `;
                    } else {
                        contentDiv.innerHTML = `<div class="whitespace-pre-wrap break-words">${newContent}</div>`;
                        timestamp.textContent = new Date().toLocaleTimeString();
                    }
                }
            }

            // 打开设置
            openSettings() {
                // 移除现有设置模态框
                const existingModal = document.getElementById('settings-modal');
                if (existingModal) {
                    existingModal.remove();
                }

                // 创建设置模态框
                const settingsHTML = `
                    <div id="settings-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                        <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md">
                            <div class="p-6 border-b border-gray-200">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-xl font-bold text-gray-800">系统设置</h3>
                                    <button id="close-settings-modal" class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-times text-xl"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="p-6 space-y-6">
                                <div>
                                    <h4 class="font-semibold text-gray-800 mb-3">服务状态</h4>
                                    <div class="space-y-2">
                                        ${Object.keys(this.services).map(serviceKey => {
                                            const service = this.services[serviceKey];
                                            const isActive = this.activeServices.includes(serviceKey);
                                            return `
                                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                                    <div class="flex items-center space-x-3">
                                                        <div class="w-3 h-3 rounded-full ${isActive ? 'bg-green-500' : 'bg-red-500'}"></div>
                                                        <span class="font-medium">${service.name}</span>
                                                    </div>
                                                    <span class="text-sm text-gray-500">${service.url}</span>
                                                </div>
                                            `;
                                        }).join('')}
                                    </div>
                                </div>

                                <div>
                                    <h4 class="font-semibold text-gray-800 mb-3">当前配置</h4>
                                    <div class="space-y-2">
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">选择的模型:</span>
                                            <span class="font-medium">${this.currentModel ? this.currentModel.name : '未选择'}</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">服务提供商:</span>
                                            <span class="font-medium">${this.currentModel ? this.currentModel.serviceName : '未选择'}</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">可用模型数:</span>
                                            <span class="font-medium">${this.discoveredModels.length}</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="flex space-x-3">
                                    <button id="refresh-from-settings" class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                                        <i class="fas fa-sync-alt mr-2"></i>刷新服务
                                    </button>
                                    <button id="select-model-from-settings" class="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                                        <i class="fas fa-list mr-2"></i>选择模型
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // 添加到页面
                document.body.insertAdjacentHTML('beforeend', settingsHTML);

                // 绑定事件
                document.getElementById('close-settings-modal').addEventListener('click', () => {
                    document.getElementById('settings-modal').remove();
                });

                document.getElementById('refresh-from-settings').addEventListener('click', async () => {
                    await this.refreshServices();
                    document.getElementById('settings-modal').remove();
                });

                document.getElementById('select-model-from-settings').addEventListener('click', () => {
                    document.getElementById('settings-modal').remove();
                    this.showModelList();
                });

                // 点击背景关闭
                document.getElementById('settings-modal').addEventListener('click', (e) => {
                    if (e.target.id === 'settings-modal') {
                        document.getElementById('settings-modal').remove();
                    }
                });
            }
        }
        }
        
        // 启动应用
        document.addEventListener('DOMContentLoaded', () => {
            window.chatApp = new AdvancedChatApp();
        });
    </script>
</body>
</html>
