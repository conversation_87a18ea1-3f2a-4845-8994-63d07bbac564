<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试修复版 - AI聊天助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 1000;
            max-width: 300px;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 调试信息面板 -->
    <div id="debug-panel" class="debug-info">
        <div>调试信息:</div>
        <div id="debug-log"></div>
    </div>

    <!-- 主界面 -->
    <div class="min-h-screen flex flex-col">
        <!-- 顶部状态栏 -->
        <div class="bg-white shadow-sm border-b border-gray-200 px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <h1 class="text-lg font-semibold text-gray-800">AI聊天助手</h1>
                    <div id="connection-status" class="text-sm text-gray-500">检测中...</div>
                </div>
                <button id="settings-btn" class="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600">
                    <i class="fas fa-cog"></i> 设置
                </button>
            </div>
        </div>

        <!-- 系统监控面板 -->
        <div class="bg-gray-100 px-4 py-3">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <!-- 内存使用 -->
                <div class="bg-white rounded-lg p-3 shadow-sm">
                    <div class="text-xs text-gray-500 mb-1">内存使用</div>
                    <div class="flex items-center space-x-2">
                        <div id="panel-memory-usage" class="text-sm font-semibold text-cyan-600">--</div>
                        <div class="flex-1 bg-gray-200 rounded-full h-2">
                            <div id="memory-progress" class="bg-cyan-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="text-xs text-gray-400 mt-1">JS堆内存</div>
                </div>

                <!-- CPU信息 -->
                <div class="bg-white rounded-lg p-3 shadow-sm">
                    <div class="text-xs text-gray-500 mb-1">CPU信息</div>
                    <div id="panel-cpu-info" class="text-sm font-semibold text-yellow-600">检测中...</div>
                    <div class="text-xs text-gray-400 mt-1">逻辑核心</div>
                </div>

                <!-- GPU信息 -->
                <div class="bg-white rounded-lg p-3 shadow-sm">
                    <div class="text-xs text-gray-500 mb-1">GPU信息</div>
                    <div id="panel-gpu-info" class="text-sm font-semibold text-pink-600">检测中...</div>
                    <div class="text-xs text-gray-400 mt-1">图形处理器</div>
                </div>

                <!-- 网络状态 -->
                <div class="bg-white rounded-lg p-3 shadow-sm">
                    <div class="text-xs text-gray-500 mb-1">网络状态</div>
                    <div id="panel-network-status" class="text-sm font-semibold text-green-600">检测中...</div>
                    <div class="text-xs text-gray-400 mt-1">连接状态</div>
                </div>
            </div>
        </div>

        <!-- 聊天区域 -->
        <div class="flex-1 flex flex-col">
            <!-- 消息列表 -->
            <div id="chat-messages" class="flex-1 overflow-y-auto p-4 space-y-4">
                <div class="text-center text-gray-500 py-8">
                    <i class="fas fa-comments text-4xl mb-4"></i>
                    <p>开始您的AI对话</p>
                </div>
            </div>

            <!-- 文件预览区域 -->
            <div id="file-preview-area" class="hidden mx-4 mb-4 p-4 bg-white rounded-lg border border-gray-200">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">已选择文件:</span>
                    <button id="clear-files" class="text-red-500 hover:text-red-700 text-sm">
                        <i class="fas fa-times"></i> 清除
                    </button>
                </div>
                <div id="file-list" class="space-y-2"></div>
            </div>

            <!-- 输入区域 -->
            <div class="bg-white border-t border-gray-200 p-4">
                <div class="flex space-x-2">
                    <!-- 文件上传按钮 -->
                    <label for="file-input" class="px-3 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors cursor-pointer flex items-center space-x-1" title="上传文件">
                        <i class="fas fa-paperclip text-sm"></i>
                        <span class="hidden sm:inline text-sm">附件</span>
                    </label>
                    <input type="file" id="file-input" multiple accept="image/*,.pdf,.txt,.doc,.docx,.json,.csv" class="hidden">
                    
                    <!-- 图片上传按钮 -->
                    <label for="image-input" class="px-3 py-2 bg-green-200 text-green-700 rounded-lg hover:bg-green-300 transition-colors cursor-pointer flex items-center space-x-1" title="上传图片">
                        <i class="fas fa-image text-sm"></i>
                        <span class="hidden sm:inline text-sm">图片</span>
                    </label>
                    <input type="file" id="image-input" multiple accept="image/png,image/jpg,image/jpeg,image/gif,image/webp" class="hidden">

                    <!-- 输入框 -->
                    <textarea id="user-input" placeholder="输入您的消息..." class="flex-1 resize-none border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" rows="1"></textarea>
                    
                    <!-- 发送按钮 -->
                    <button id="send-btn" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        class DebugChatApp {
            constructor() {
                this.debugLog('应用初始化开始');
                
                // 基础配置
                this.uploadedFiles = [];
                this.maxFileSize = 10 * 1024 * 1024; // 10MB
                this.currentModel = null;
                
                this.debugLog('设置事件监听器');
                this.setupEventListeners();
                
                this.debugLog('启动系统监控');
                this.startSystemMonitoring();
                
                this.debugLog('应用初始化完成');
            }

            debugLog(message) {
                const timestamp = new Date().toLocaleTimeString();
                const logElement = document.getElementById('debug-log');
                if (logElement) {
                    logElement.innerHTML += `<div>[${timestamp}] ${message}</div>`;
                    // 保持最新的10条日志
                    const logs = logElement.children;
                    if (logs.length > 10) {
                        logElement.removeChild(logs[0]);
                    }
                }
                console.log(`[DebugChatApp] ${message}`);
            }

            setupEventListeners() {
                try {
                    // 发送按钮
                    const sendBtn = document.getElementById('send-btn');
                    if (sendBtn) {
                        sendBtn.addEventListener('click', () => this.sendMessage());
                        this.debugLog('发送按钮事件绑定成功');
                    } else {
                        this.debugLog('发送按钮未找到');
                    }

                    // 输入框
                    const userInput = document.getElementById('user-input');
                    if (userInput) {
                        userInput.addEventListener('keydown', (e) => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                                e.preventDefault();
                                this.sendMessage();
                            }
                        });
                        this.debugLog('输入框事件绑定成功');
                    } else {
                        this.debugLog('输入框未找到');
                    }

                    // 文件上传
                    const fileInput = document.getElementById('file-input');
                    const imageInput = document.getElementById('image-input');
                    const clearFilesBtn = document.getElementById('clear-files');
                    
                    if (fileInput) {
                        fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
                        this.debugLog('文件上传事件绑定成功');
                    }
                    if (imageInput) {
                        imageInput.addEventListener('change', (e) => this.handleFileUpload(e));
                        this.debugLog('图片上传事件绑定成功');
                    }
                    if (clearFilesBtn) {
                        clearFilesBtn.addEventListener('click', () => this.clearFiles());
                        this.debugLog('清除文件按钮事件绑定成功');
                    }

                    // 设置按钮
                    const settingsBtn = document.getElementById('settings-btn');
                    if (settingsBtn) {
                        settingsBtn.addEventListener('click', () => this.openSettings());
                        this.debugLog('设置按钮事件绑定成功');
                    }

                } catch (error) {
                    this.debugLog('事件监听器设置失败: ' + error.message);
                }
            }

            startSystemMonitoring() {
                try {
                    this.debugLog('开始系统硬件检测');
                    this.detectSystemHardware();
                    
                    // 每5秒更新一次系统指标
                    setInterval(() => {
                        this.updateSystemMetrics();
                    }, 5000);
                    
                    this.debugLog('系统监控启动成功');
                } catch (error) {
                    this.debugLog('系统监控启动失败: ' + error.message);
                }
            }

            detectSystemHardware() {
                try {
                    // 内存检测
                    if ('memory' in performance) {
                        const memory = performance.memory;
                        const usedMemory = (memory.usedJSHeapSize / 1024 / 1024).toFixed(1);
                        const limitMemory = (memory.jsHeapSizeLimit / 1024 / 1024).toFixed(1);

                        const memoryElement = document.getElementById('panel-memory-usage');
                        const progressElement = document.getElementById('memory-progress');
                        
                        if (memoryElement) {
                            memoryElement.textContent = `${usedMemory}MB`;
                            this.debugLog(`内存使用: ${usedMemory}MB / ${limitMemory}MB`);
                        }

                        if (progressElement) {
                            const percentage = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
                            progressElement.style.width = `${percentage}%`;
                        }
                    } else {
                        this.debugLog('浏览器不支持内存API');
                        this.hideMonitoringCard('panel-memory-usage');
                    }

                    // CPU检测
                    if ('hardwareConcurrency' in navigator) {
                        const cores = navigator.hardwareConcurrency;
                        const cpuElement = document.getElementById('panel-cpu-info');
                        if (cpuElement) {
                            cpuElement.textContent = `${cores}核心`;
                            this.debugLog(`CPU核心数: ${cores}`);
                        }
                    } else {
                        this.debugLog('浏览器不支持CPU API');
                        this.hideMonitoringCard('panel-cpu-info');
                    }

                    // GPU检测
                    this.detectGPUInfo();

                    // 网络状态
                    const networkElement = document.getElementById('panel-network-status');
                    if (networkElement) {
                        networkElement.textContent = navigator.onLine ? '在线' : '离线';
                        this.debugLog(`网络状态: ${navigator.onLine ? '在线' : '离线'}`);
                    }

                } catch (error) {
                    this.debugLog('系统硬件检测失败: ' + error.message);
                }
            }

            detectGPUInfo() {
                try {
                    const canvas = document.createElement('canvas');
                    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                    
                    const gpuElement = document.getElementById('panel-gpu-info');
                    if (!gpuElement) {
                        this.debugLog('GPU信息元素未找到');
                        return;
                    }
                    
                    if (gl) {
                        const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
                        if (debugInfo) {
                            const vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
                            const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
                            const gpuInfo = `${vendor} ${renderer}`.substring(0, 30);
                            gpuElement.textContent = gpuInfo;
                            this.debugLog(`GPU信息: ${gpuInfo}`);
                        } else {
                            gpuElement.textContent = 'WebGL支持';
                            this.debugLog('GPU信息: WebGL支持但无详细信息');
                        }
                    } else {
                        this.debugLog('浏览器不支持WebGL');
                        this.hideMonitoringCard('panel-gpu-info');
                    }
                } catch (error) {
                    this.debugLog('GPU信息检测失败: ' + error.message);
                    this.hideMonitoringCard('panel-gpu-info');
                }
            }

            updateSystemMetrics() {
                try {
                    // 更新内存使用
                    if ('memory' in performance) {
                        const memory = performance.memory;
                        const usedMemory = (memory.usedJSHeapSize / 1024 / 1024).toFixed(1);

                        const memoryElement = document.getElementById('panel-memory-usage');
                        const progressElement = document.getElementById('memory-progress');
                        
                        if (memoryElement) {
                            memoryElement.textContent = `${usedMemory}MB`;
                        }

                        if (progressElement) {
                            const percentage = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
                            progressElement.style.width = `${percentage.toFixed(1)}%`;
                        }
                    }

                    // 更新网络状态
                    const networkElement = document.getElementById('panel-network-status');
                    if (networkElement) {
                        networkElement.textContent = navigator.onLine ? '在线' : '离线';
                    }

                } catch (error) {
                    this.debugLog('系统指标更新失败: ' + error.message);
                }
            }

            hideMonitoringCard(elementId) {
                try {
                    const element = document.getElementById(elementId);
                    if (element) {
                        const card = element.closest('.bg-white');
                        if (card) {
                            card.style.display = 'none';
                            this.debugLog(`隐藏监控卡片: ${elementId}`);
                        }
                    } else {
                        this.debugLog(`监控卡片元素未找到: ${elementId}`);
                    }
                } catch (error) {
                    this.debugLog(`隐藏监控卡片失败: ${elementId} - ${error.message}`);
                }
            }

            handleFileUpload(event) {
                try {
                    const files = Array.from(event.target.files);
                    this.debugLog(`文件上传: ${files.length}个文件`);
                    
                    for (const file of files) {
                        if (file.size > this.maxFileSize) {
                            this.debugLog(`文件过大: ${file.name} (${file.size} bytes)`);
                            continue;
                        }
                        
                        this.uploadedFiles.push({
                            file: file,
                            id: Date.now() + Math.random(),
                            name: file.name,
                            size: file.size,
                            type: file.type
                        });
                        
                        this.debugLog(`文件添加成功: ${file.name}`);
                    }
                    
                    event.target.value = '';
                    this.updateFilePreview();
                    
                } catch (error) {
                    this.debugLog('文件上传处理失败: ' + error.message);
                }
            }

            updateFilePreview() {
                try {
                    const previewArea = document.getElementById('file-preview-area');
                    const fileList = document.getElementById('file-list');
                    
                    if (!previewArea || !fileList) {
                        this.debugLog('文件预览元素未找到');
                        return;
                    }
                    
                    if (this.uploadedFiles.length === 0) {
                        previewArea.classList.add('hidden');
                        return;
                    }
                    
                    previewArea.classList.remove('hidden');
                    fileList.innerHTML = this.uploadedFiles.map(fileData => `
                        <div class="flex items-center space-x-3 p-2 bg-gray-50 rounded">
                            <i class="fas fa-file text-gray-500"></i>
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-900">${fileData.name}</div>
                                <div class="text-xs text-gray-500">${this.formatFileSize(fileData.size)}</div>
                            </div>
                            <button onclick="window.debugChatApp.removeFile('${fileData.id}')" class="text-red-500 hover:text-red-700">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    `).join('');
                    
                    this.debugLog(`文件预览更新: ${this.uploadedFiles.length}个文件`);
                    
                } catch (error) {
                    this.debugLog('文件预览更新失败: ' + error.message);
                }
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            removeFile(fileId) {
                try {
                    this.uploadedFiles = this.uploadedFiles.filter(f => f.id !== fileId);
                    this.updateFilePreview();
                    this.debugLog(`文件删除: ${fileId}`);
                } catch (error) {
                    this.debugLog('文件删除失败: ' + error.message);
                }
            }

            clearFiles() {
                try {
                    this.uploadedFiles = [];
                    this.updateFilePreview();
                    this.debugLog('所有文件已清除');
                } catch (error) {
                    this.debugLog('清除文件失败: ' + error.message);
                }
            }

            sendMessage() {
                try {
                    const userInput = document.getElementById('user-input');
                    const message = userInput ? userInput.value.trim() : '';
                    
                    if (!message && this.uploadedFiles.length === 0) {
                        this.debugLog('消息为空且无文件');
                        return;
                    }
                    
                    this.debugLog(`发送消息: "${message}" + ${this.uploadedFiles.length}个文件`);
                    
                    // 清空输入框
                    if (userInput) {
                        userInput.value = '';
                    }
                    
                    // 添加消息到聊天区域
                    this.addMessage('user', message);
                    
                    // 模拟AI回复
                    setTimeout(() => {
                        this.addMessage('ai', '这是一个测试回复。所有功能正常工作！');
                    }, 1000);
                    
                    // 清除文件
                    this.clearFiles();
                    
                } catch (error) {
                    this.debugLog('发送消息失败: ' + error.message);
                }
            }

            addMessage(sender, content) {
                try {
                    const chatMessages = document.getElementById('chat-messages');
                    if (!chatMessages) {
                        this.debugLog('聊天消息容器未找到');
                        return;
                    }
                    
                    const messageDiv = document.createElement('div');
                    messageDiv.className = `flex ${sender === 'user' ? 'justify-end' : 'justify-start'}`;
                    
                    messageDiv.innerHTML = `
                        <div class="max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                            sender === 'user' 
                                ? 'bg-blue-500 text-white' 
                                : 'bg-white border border-gray-200 text-gray-800'
                        }">
                            <div class="text-sm">${content}</div>
                        </div>
                    `;
                    
                    chatMessages.appendChild(messageDiv);
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                    
                    this.debugLog(`消息添加成功: ${sender} - ${content.substring(0, 50)}...`);
                    
                } catch (error) {
                    this.debugLog('添加消息失败: ' + error.message);
                }
            }

            openSettings() {
                this.debugLog('打开设置');
                alert('设置功能正常工作！');
            }
        }

        // 启动应用
        document.addEventListener('DOMContentLoaded', () => {
            try {
                window.debugChatApp = new DebugChatApp();
                console.log('调试版聊天应用启动成功');
            } catch (error) {
                console.error('应用启动失败:', error);
                const debugLog = document.getElementById('debug-log');
                if (debugLog) {
                    debugLog.innerHTML = `<div style="color: red;">启动失败: ${error.message}</div>`;
                }
            }
        });
    </script>
</body>
</html>
