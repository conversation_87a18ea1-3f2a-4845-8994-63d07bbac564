<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI聊天助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                        },
                        gray: {
                            50: '#f9fafb',
                            100: '#f3f4f6',
                            200: '#e5e7eb',
                            300: '#d1d5db',
                            400: '#9ca3af',
                            500: '#6b7280',
                            600: '#4b5563',
                            700: '#374151',
                            800: '#1f2937',
                            900: '#111827',
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.3s ease-in-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'pulse-soft': 'pulseSoft 2s infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(10px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        },
                        pulseSoft: {
                            '0%, 100%': { opacity: '1' },
                            '50%': { opacity: '0.7' }
                        }
                    }
                }
            }
            // UI更新方法
            updateUI() {
                this.updateStatus();
                this.updateModelInfo();
                this.updateSettingsForm();
            }

            updateStatus() {
                const indicator = document.getElementById('status-indicator');
                const text = document.getElementById('status-text');

                if (this.isConnected) {
                    indicator.className = 'w-3 h-3 rounded-full status-online';
                    text.textContent = '已连接';
                } else {
                    indicator.className = 'w-3 h-3 rounded-full status-offline';
                    text.textContent = '未连接';
                }
            }

            updateModelInfo() {
                const modelInfo = document.getElementById('model-info');
                modelInfo.textContent = this.config.model || '未选择模型';
            }

            updateSettingsForm() {
                document.getElementById('api-type').value = this.config.apiType;
                document.getElementById('api-url').value = this.config.apiUrl;
                document.getElementById('api-key').value = this.config.apiKey;
            }

            // 设置弹窗管理
            openSettings() {
                const modal = document.getElementById('settings-modal');
                const content = document.getElementById('settings-content');

                modal.classList.remove('hidden');
                setTimeout(() => {
                    content.style.transform = 'scale(1)';
                    content.style.opacity = '1';
                }, 10);

                this.updateSettingsForm();
            }

            closeSettings() {
                const modal = document.getElementById('settings-modal');
                const content = document.getElementById('settings-content');

                content.style.transform = 'scale(0.95)';
                content.style.opacity = '0';

                setTimeout(() => {
                    modal.classList.add('hidden');
                }, 300);
            }

            // 显示加载状态
            showLoading() {
                document.getElementById('loading-overlay').classList.remove('hidden');
            }

            hideLoading() {
                document.getElementById('loading-overlay').classList.add('hidden');
            }

            // 显示通知
            showNotification(message, type = 'info') {
                // 创建通知元素
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full ${
                    type === 'success' ? 'bg-green-500 text-white' :
                    type === 'error' ? 'bg-red-500 text-white' :
                    type === 'warning' ? 'bg-yellow-500 text-white' :
                    'bg-blue-500 text-white'
                }`;
                notification.innerHTML = `
                    <div class="flex items-center space-x-2">
                        <i class="fas ${
                            type === 'success' ? 'fa-check-circle' :
                            type === 'error' ? 'fa-exclamation-circle' :
                            type === 'warning' ? 'fa-exclamation-triangle' :
                            'fa-info-circle'
                        }"></i>
                        <span>${message}</span>
                    </div>
                `;

                document.body.appendChild(notification);

                // 显示动画
                setTimeout(() => {
                    notification.style.transform = 'translateX(0)';
                }, 10);

                // 自动隐藏
                setTimeout(() => {
                    notification.style.transform = 'translateX(full)';
                    setTimeout(() => {
                        document.body.removeChild(notification);
                    }, 300);
                }, 3000);
            }

            // 自动检测服务
            async autoDetect() {
                this.showLoading();

                const services = [
                    { name: 'Ollama', type: 'ollama', url: 'http://localhost:11434', endpoint: '/api/tags' },
                    { name: 'LM Studio', type: 'lm-studio', url: 'http://localhost:1234', endpoint: '/v1/models' }
                ];

                let detected = null;

                for (const service of services) {
                    try {
                        console.log(`检测 ${service.name}...`);
                        const response = await fetch(service.url + service.endpoint, {
                            method: 'GET',
                            headers: { 'Content-Type': 'application/json' }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            let modelCount = 0;

                            if (service.type === 'ollama' && data.models) {
                                modelCount = data.models.length;
                            } else if (service.type === 'lm-studio' && data.data) {
                                modelCount = data.data.length;
                            }

                            detected = { ...service, modelCount };
                            console.log(`✅ 检测到 ${service.name}，模型数量: ${modelCount}`);
                            break;
                        }
                    } catch (error) {
                        console.log(`❌ ${service.name} 检测失败:`, error.message);
                    }
                }

                this.hideLoading();

                if (detected) {
                    this.config.apiType = detected.type;
                    this.config.apiUrl = detected.url;
                    this.updateSettingsForm();
                    this.showNotification(`检测到 ${detected.name}，发现 ${detected.modelCount} 个模型`, 'success');

                    // 自动测试连接
                    setTimeout(() => this.testConnection(), 500);
                } else {
                    this.showNotification('未检测到可用的AI服务，请检查服务是否运行', 'warning');
                }
            }
        }
    </script>
    <style>
        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 消息动画 */
        .message-enter {
            animation: slideUp 0.3s ease-out;
        }

        /* 输入框聚焦效果 */
        .input-focus:focus {
            box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
            border-color: #0ea5e9;
        }

        /* 按钮悬停效果 */
        .btn-hover {
            transition: all 0.2s ease;
        }
        .btn-hover:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .mobile-padding {
                padding-left: 1rem;
                padding-right: 1rem;
            }
            .mobile-text {
                font-size: 0.875rem;
            }
        }

        /* 状态指示器动画 */
        .status-online {
            background: linear-gradient(45deg, #10b981, #059669);
            animation: pulseSoft 2s infinite;
        }
        .status-offline {
            background: #ef4444;
        }
        .status-connecting {
            background: #f59e0b;
            animation: pulseSoft 1s infinite;
        }

        /* 消息气泡渐变 */
        .user-message {
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
        }
        .ai-message {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid #e2e8f0;
        }

        /* 加载动画 */
        .typing-indicator {
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }
        .typing-dot {
            width: 6px;
            height: 6px;
            background: #64748b;
            border-radius: 50%;
            animation: typingDot 1.4s infinite ease-in-out;
        }
        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typingDot {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <!-- 主容器 -->
    <div class="flex flex-col h-screen max-w-6xl mx-auto">
        <!-- 顶部状态栏 -->
        <header class="bg-white/80 backdrop-blur-md border-b border-gray-200/50 px-4 py-3 sticky top-0 z-30">
            <div class="flex items-center justify-between">
                <!-- 左侧状态信息 -->
                <div class="flex items-center space-x-3">
                    <div class="flex items-center space-x-2">
                        <div id="status-indicator" class="w-3 h-3 rounded-full status-offline"></div>
                        <span id="status-text" class="text-sm font-medium text-gray-700">未连接</span>
                    </div>
                    <div class="hidden sm:block w-px h-4 bg-gray-300"></div>
                    <div id="model-info" class="text-sm text-gray-500 hidden sm:block">未选择模型</div>
                </div>
                
                <!-- 右侧操作按钮 -->
                <div class="flex items-center space-x-2">
                    <button id="stats-toggle" class="p-2 text-gray-500 hover:text-primary-600 hover:bg-gray-100 rounded-lg transition-colors hidden sm:block" title="统计信息">
                        <i class="fas fa-chart-line"></i>
                    </button>
                    <button id="settings-btn" class="p-2 text-gray-500 hover:text-primary-600 hover:bg-gray-100 rounded-lg transition-colors" title="设置">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>
            
            <!-- 统计信息栏（可折叠） -->
            <div id="stats-bar" class="mt-3 pt-3 border-t border-gray-200/50 hidden">
                <div class="grid grid-cols-2 sm:grid-cols-4 gap-4 text-xs">
                    <div class="text-center">
                        <div id="message-count" class="font-semibold text-gray-900">0</div>
                        <div class="text-gray-500">消息</div>
                    </div>
                    <div class="text-center">
                        <div id="response-time" class="font-semibold text-gray-900">--</div>
                        <div class="text-gray-500">响应时间</div>
                    </div>
                    <div class="text-center">
                        <div id="output-speed" class="font-semibold text-gray-900">--</div>
                        <div class="text-gray-500">输出速度</div>
                    </div>
                    <div class="text-center">
                        <div id="token-count" class="font-semibold text-gray-900">--</div>
                        <div class="text-gray-500">Token</div>
                    </div>
                </div>
            </div>
        </header>

        <!-- 聊天区域 -->
        <main class="flex-1 flex flex-col overflow-hidden">
            <!-- 消息容器 -->
            <div id="chat-container" class="flex-1 overflow-y-auto custom-scrollbar px-4 py-6">
                <!-- 欢迎消息 -->
                <div id="welcome-message" class="text-center py-16">
                    <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-robot text-white text-2xl"></i>
                    </div>
                    <h2 class="text-xl font-semibold text-gray-800 mb-2">AI聊天助手</h2>
                    <p class="text-gray-500 mb-6">开始与AI进行智能对话</p>
                    <button id="quick-setup" class="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors btn-hover">
                        <i class="fas fa-rocket mr-2"></i>快速设置
                    </button>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="bg-white/80 backdrop-blur-md border-t border-gray-200/50 p-4">
                <div class="max-w-4xl mx-auto">
                    <!-- 输入框容器 -->
                    <div class="flex items-end space-x-3">
                        <div class="flex-1 relative">
                            <textarea 
                                id="user-input" 
                                class="w-full bg-white border border-gray-300 rounded-xl px-4 py-3 pr-12 resize-none input-focus transition-all duration-200 mobile-text"
                                rows="1"
                                placeholder="输入您的问题..."
                                style="min-height: 44px; max-height: 120px;"
                            ></textarea>
                            <!-- 输入框内的发送按钮（移动端） -->
                            <button id="send-btn-mobile" class="absolute right-2 bottom-2 w-8 h-8 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors sm:hidden">
                                <i class="fas fa-paper-plane text-sm"></i>
                            </button>
                        </div>
                        <!-- 桌面端发送按钮 -->
                        <button id="send-btn-desktop" class="hidden sm:flex items-center space-x-2 bg-primary-600 text-white px-6 py-3 rounded-xl hover:bg-primary-700 transition-colors btn-hover">
                            <i class="fas fa-paper-plane"></i>
                            <span>发送</span>
                        </button>
                    </div>
                    
                    <!-- 底部操作栏 -->
                    <div class="flex items-center justify-between mt-3">
                        <div class="flex items-center space-x-4">
                            <button id="clear-chat" class="text-gray-500 hover:text-red-500 transition-colors text-sm">
                                <i class="fas fa-trash mr-1"></i>
                                <span class="hidden sm:inline">清空对话</span>
                            </button>
                            <button id="export-chat" class="text-gray-500 hover:text-primary-600 transition-colors text-sm hidden sm:block">
                                <i class="fas fa-download mr-1"></i>导出对话
                            </button>
                        </div>
                        <div class="text-xs text-gray-400">
                            <span class="hidden sm:inline">按 Enter 发送，Shift+Enter 换行</span>
                            <span class="sm:hidden">点击发送</span>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 设置弹窗 -->
    <div id="settings-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-md transform transition-all duration-300 scale-95 opacity-0" id="settings-content">
                <!-- 弹窗头部 -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">连接设置</h3>
                    <button id="close-settings" class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <!-- 弹窗内容 -->
                <div class="p-6 space-y-6">
                    <!-- 快速检测 -->
                    <div class="bg-gradient-to-r from-primary-50 to-blue-50 rounded-xl p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-medium text-gray-900">自动检测服务</h4>
                            <i class="fas fa-magic text-primary-600"></i>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">自动检测本地运行的AI服务</p>
                        <button id="auto-detect" class="w-full bg-primary-600 text-white py-2 rounded-lg hover:bg-primary-700 transition-colors btn-hover">
                            <i class="fas fa-search mr-2"></i>开始检测
                        </button>
                    </div>

                    <!-- 手动配置 -->
                    <div class="space-y-4">
                        <h4 class="font-medium text-gray-900">手动配置</h4>

                        <!-- API类型 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">服务类型</label>
                            <select id="api-type" class="w-full border border-gray-300 rounded-lg px-3 py-2 input-focus">
                                <option value="ollama">Ollama</option>
                                <option value="lm-studio">LM Studio</option>
                                <option value="openai-compatible">OpenAI兼容</option>
                            </select>
                        </div>

                        <!-- API地址 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">服务地址</label>
                            <input id="api-url" type="text" class="w-full border border-gray-300 rounded-lg px-3 py-2 input-focus" placeholder="http://localhost:11434">
                        </div>

                        <!-- API密钥 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">API密钥（可选）</label>
                            <input id="api-key" type="password" class="w-full border border-gray-300 rounded-lg px-3 py-2 input-focus" placeholder="留空表示无需密钥">
                        </div>

                        <!-- 模型选择 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">选择模型</label>
                            <select id="model-select" class="w-full border border-gray-300 rounded-lg px-3 py-2 input-focus">
                                <option value="">请先测试连接</option>
                            </select>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex space-x-3">
                        <button id="test-connection" class="flex-1 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors btn-hover">
                            <i class="fas fa-plug mr-2"></i>测试连接
                        </button>
                        <button id="save-settings" class="flex-1 bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition-colors btn-hover">
                            <i class="fas fa-save mr-2"></i>保存设置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div id="loading-overlay" class="fixed inset-0 bg-black/30 backdrop-blur-sm z-40 hidden">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white rounded-xl p-6 shadow-xl">
                <div class="flex items-center space-x-3">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
                    <span class="text-gray-700">处理中...</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局配置和状态管理
        class ChatApp {
            constructor() {
                this.config = {
                    apiType: 'ollama',
                    apiUrl: 'http://localhost:11434',
                    apiKey: '',
                    model: ''
                };

                this.stats = {
                    messageCount: 0,
                    totalTokens: 0,
                    lastResponseTime: 0,
                    lastOutputSpeed: 0
                };

                this.isConnected = false;
                this.init();
            }

            init() {
                this.loadConfig();
                this.bindEvents();
                this.updateUI();
                console.log('AI聊天助手已初始化');
            }

            // 配置管理
            loadConfig() {
                const saved = localStorage.getItem('modern-chat-config');
                if (saved) {
                    this.config = { ...this.config, ...JSON.parse(saved) };
                }
            }

            saveConfig() {
                localStorage.setItem('modern-chat-config', JSON.stringify(this.config));
            }

            // 事件绑定
            bindEvents() {
                // 设置相关
                document.getElementById('settings-btn').addEventListener('click', () => this.openSettings());
                document.getElementById('quick-setup').addEventListener('click', () => this.openSettings());
                document.getElementById('close-settings').addEventListener('click', () => this.closeSettings());
                document.getElementById('auto-detect').addEventListener('click', () => this.autoDetect());
                document.getElementById('test-connection').addEventListener('click', () => this.testConnection());
                document.getElementById('save-settings').addEventListener('click', () => this.saveSettings());

                // 聊天相关
                document.getElementById('send-btn-desktop').addEventListener('click', () => this.sendMessage());
                document.getElementById('send-btn-mobile').addEventListener('click', () => this.sendMessage());
                document.getElementById('clear-chat').addEventListener('click', () => this.clearChat());
                document.getElementById('export-chat').addEventListener('click', () => this.exportChat());

                // 统计信息切换
                document.getElementById('stats-toggle').addEventListener('click', () => this.toggleStats());

                // 输入框事件
                const userInput = document.getElementById('user-input');
                userInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                // 自动调整输入框高度
                userInput.addEventListener('input', () => {
                    userInput.style.height = 'auto';
                    userInput.style.height = Math.min(userInput.scrollHeight, 120) + 'px';
                });

                // API类型变化
                document.getElementById('api-type').addEventListener('change', (e) => {
                    const apiType = e.target.value;
                    const urlInput = document.getElementById('api-url');
                    if (apiType === 'ollama') {
                        urlInput.value = 'http://localhost:11434';
                    } else if (apiType === 'lm-studio') {
                        urlInput.value = 'http://localhost:1234';
                    } else {
                        urlInput.value = 'http://localhost:1234';
                    }
                });

                // 模态框背景点击关闭
                document.getElementById('settings-modal').addEventListener('click', (e) => {
                    if (e.target.id === 'settings-modal') {
                        this.closeSettings();
                    }
                });
            }

            // 测试连接
            async testConnection() {
                this.showLoading();

                try {
                    const apiType = document.getElementById('api-type').value;
                    const apiUrl = document.getElementById('api-url').value;

                    let endpoint = '';
                    if (apiType === 'ollama') {
                        endpoint = '/api/tags';
                    } else {
                        endpoint = '/v1/models';
                    }

                    const response = await fetch(apiUrl + endpoint);

                    if (response.ok) {
                        const data = await response.json();
                        this.populateModels(data, apiType);
                        this.showNotification('连接成功！', 'success');
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                } catch (error) {
                    console.error('连接测试失败:', error);
                    this.showNotification(`连接失败: ${error.message}`, 'error');
                } finally {
                    this.hideLoading();
                }
            }

            // 填充模型列表
            populateModels(data, apiType) {
                const modelSelect = document.getElementById('model-select');
                modelSelect.innerHTML = '<option value="">请选择模型</option>';

                let models = [];
                if (apiType === 'ollama' && data.models) {
                    models = data.models.map(m => ({ id: m.name, name: m.name }));
                } else if (data.data) {
                    models = data.data.map(m => ({ id: m.id, name: m.id }));
                }

                models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.id;
                    option.textContent = model.name;
                    modelSelect.appendChild(option);
                });

                // 如果有保存的模型，自动选择
                if (this.config.model) {
                    modelSelect.value = this.config.model;
                }
            }

            // 保存设置
            saveSettings() {
                this.config.apiType = document.getElementById('api-type').value;
                this.config.apiUrl = document.getElementById('api-url').value;
                this.config.apiKey = document.getElementById('api-key').value;
                this.config.model = document.getElementById('model-select').value;

                if (!this.config.model) {
                    this.showNotification('请选择一个模型', 'warning');
                    return;
                }

                this.saveConfig();
                this.isConnected = true;
                this.updateUI();
                this.closeSettings();
                this.showNotification('设置已保存', 'success');
            }

            // 统计信息切换
            toggleStats() {
                const statsBar = document.getElementById('stats-bar');
                statsBar.classList.toggle('hidden');
            }

            // 更新统计信息
            updateStats() {
                document.getElementById('message-count').textContent = this.stats.messageCount;
                document.getElementById('response-time').textContent = `${this.stats.lastResponseTime}ms`;
                document.getElementById('output-speed').textContent = `${this.stats.lastOutputSpeed.toFixed(1)} 字符/秒`;
                document.getElementById('token-count').textContent = this.stats.totalTokens;
            }

            // 添加消息
            addMessage(role, content, isThinking = false) {
                const messageId = 'msg-' + Date.now();
                const messageDiv = document.createElement('div');
                messageDiv.id = messageId;
                messageDiv.className = `message-enter mb-6 ${role === 'user' ? 'flex justify-end' : 'flex justify-start'}`;

                const bubble = document.createElement('div');
                bubble.className = `max-w-xs sm:max-w-md lg:max-w-lg px-4 py-3 rounded-2xl ${
                    role === 'user'
                        ? 'user-message text-white rounded-br-md'
                        : 'ai-message text-gray-800 rounded-bl-md'
                }`;

                if (isThinking) {
                    bubble.innerHTML = `
                        <div class="typing-indicator">
                            <span>AI正在思考</span>
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                        </div>
                    `;
                } else {
                    const contentDiv = document.createElement('div');
                    contentDiv.className = 'whitespace-pre-wrap break-words';
                    contentDiv.textContent = content;
                    bubble.appendChild(contentDiv);

                    // 添加时间戳
                    const timestamp = document.createElement('div');
                    timestamp.className = `text-xs mt-2 ${role === 'user' ? 'text-blue-200' : 'text-gray-500'}`;
                    timestamp.textContent = new Date().toLocaleTimeString();
                    bubble.appendChild(timestamp);
                }

                messageDiv.appendChild(bubble);

                const chatContainer = document.getElementById('chat-container');
                chatContainer.appendChild(messageDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;

                return messageId;
            }

            // 更新消息
            updateMessage(messageId, newContent) {
                const messageDiv = document.getElementById(messageId);
                if (messageDiv) {
                    const bubble = messageDiv.querySelector('div');
                    bubble.innerHTML = `
                        <div class="whitespace-pre-wrap break-words">${newContent}</div>
                        <div class="text-xs mt-2 text-gray-500">${new Date().toLocaleTimeString()}</div>
                    `;
                }
            }
            // 发送消息
            async sendMessage() {
                const input = document.getElementById('user-input');
                const message = input.value.trim();

                if (!message) return;

                if (!this.config.model) {
                    this.showNotification('请先配置AI服务', 'warning');
                    this.openSettings();
                    return;
                }

                // 清空输入框并重置高度
                input.value = '';
                input.style.height = 'auto';

                // 隐藏欢迎消息
                const welcomeMessage = document.getElementById('welcome-message');
                if (welcomeMessage) {
                    welcomeMessage.style.display = 'none';
                }

                // 添加用户消息
                this.addMessage('user', message);
                this.stats.messageCount++;

                // 添加AI思考消息
                const thinkingId = this.addMessage('assistant', '', true);

                const startTime = Date.now();

                try {
                    let apiUrl, requestBody;

                    if (this.config.apiType === 'ollama') {
                        apiUrl = `${this.config.apiUrl}/api/generate`;
                        requestBody = {
                            model: this.config.model,
                            prompt: message,
                            stream: false
                        };
                    } else {
                        apiUrl = `${this.config.apiUrl}/v1/chat/completions`;
                        requestBody = {
                            model: this.config.model,
                            messages: [{ role: 'user', content: message }],
                            stream: false
                        };
                    }

                    const response = await fetch(apiUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` })
                        },
                        body: JSON.stringify(requestBody)
                    });

                    const endTime = Date.now();
                    const responseTime = endTime - startTime;
                    this.stats.lastResponseTime = responseTime;

                    if (response.ok) {
                        const data = await response.json();
                        let aiResponse = '';
                        let tokenInfo = {};

                        if (this.config.apiType === 'ollama') {
                            aiResponse = data.response || '抱歉，没有收到回复';
                            tokenInfo = {
                                promptTokens: data.prompt_eval_count || 0,
                                completionTokens: data.eval_count || 0,
                                totalTokens: (data.prompt_eval_count || 0) + (data.eval_count || 0)
                            };
                        } else {
                            aiResponse = data.choices?.[0]?.message?.content || '抱歉，没有收到回复';
                            tokenInfo = {
                                promptTokens: data.usage?.prompt_tokens || 0,
                                completionTokens: data.usage?.completion_tokens || 0,
                                totalTokens: data.usage?.total_tokens || 0
                            };
                        }

                        // 计算输出速度
                        const outputSpeed = aiResponse.length / (responseTime / 1000);
                        this.stats.lastOutputSpeed = outputSpeed;
                        this.stats.totalTokens += tokenInfo.totalTokens;

                        // 更新AI消息
                        this.updateMessage(thinkingId, aiResponse);

                        // 更新统计信息
                        this.updateStats();

                    } else {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                } catch (error) {
                    console.error('发送消息失败:', error);
                    this.updateMessage(thinkingId, `❌ 发送失败: ${error.message}`);
                    this.showNotification('消息发送失败', 'error');
                }
            }

            // 清空聊天
            clearChat() {
                if (confirm('确定要清空所有对话吗？')) {
                    // 重置统计信息
                    this.stats = {
                        messageCount: 0,
                        totalTokens: 0,
                        lastResponseTime: 0,
                        lastOutputSpeed: 0
                    };

                    // 清空聊天容器
                    const chatContainer = document.getElementById('chat-container');
                    chatContainer.innerHTML = `
                        <div id="welcome-message" class="text-center py-16">
                            <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-robot text-white text-2xl"></i>
                            </div>
                            <h2 class="text-xl font-semibold text-gray-800 mb-2">AI聊天助手</h2>
                            <p class="text-gray-500 mb-6">开始与AI进行智能对话</p>
                            <button id="quick-setup" class="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors btn-hover">
                                <i class="fas fa-rocket mr-2"></i>快速设置
                            </button>
                        </div>
                    `;

                    // 重新绑定快速设置按钮
                    document.getElementById('quick-setup').addEventListener('click', () => this.openSettings());

                    // 更新统计显示
                    this.updateStats();

                    this.showNotification('对话已清空', 'success');
                }
            }

            // 导出聊天记录
            exportChat() {
                const messages = [];
                const messageElements = document.querySelectorAll('#chat-container .message-enter');

                messageElements.forEach(element => {
                    const isUser = element.classList.contains('justify-end');
                    const content = element.querySelector('.whitespace-pre-wrap')?.textContent || '';
                    const timestamp = element.querySelector('.text-xs')?.textContent || '';

                    if (content && !content.includes('AI正在思考')) {
                        messages.push({
                            role: isUser ? 'user' : 'assistant',
                            content: content,
                            timestamp: timestamp
                        });
                    }
                });

                if (messages.length === 0) {
                    this.showNotification('没有可导出的对话记录', 'warning');
                    return;
                }

                const exportData = {
                    exportTime: new Date().toISOString(),
                    model: this.config.model,
                    messageCount: messages.length,
                    messages: messages
                };

                const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `chat-export-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                this.showNotification('对话记录已导出', 'success');
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            window.chatApp = new ChatApp();
        });
    </script>
</body>
</html>
