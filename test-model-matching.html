<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模型匹配测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .failure { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <h1>模型匹配算法测试</h1>
    <div id="test-results"></div>

    <script>
        // 复制模型能力数据库（简化版）
        const modelCapabilities = {
            'qwen/qwen2.5-vl-7b': {
                text: { supported: true, description: '阿里巴巴Qwen2.5-VL多模态模型7B版本，专为LM Studio优化' },
                image: { supported: true, description: '强大的视觉语言模型，支持图像理解、中文OCR、图表分析' },
                audio: { supported: false, description: '此模型不支持语音交互功能。' }
            },
            'qwen2.5vl': {
                text: { supported: true, description: '阿里巴巴Qwen2.5-VL多模态模型' },
                image: { supported: true, description: '先进的视觉语言模型' },
                audio: { supported: false, description: '此模型不支持语音交互功能。' }
            },
            'default': {
                text: { supported: true, description: '未知模型的文本生成能力' },
                image: { supported: false, description: '未知模型的图像理解能力' },
                audio: { supported: false, description: '未知模型的语音交互能力' }
            }
        };

        // 复制增强的匹配算法
        function getModelCapabilities(modelName) {
            // 直接匹配
            if (modelCapabilities[modelName]) {
                return modelCapabilities[modelName];
            }

            // 增强的模糊匹配算法
            const normalizedName = modelName.toLowerCase();
            const modelKeys = Object.keys(modelCapabilities);
            
            // 第一轮：精确匹配（包括命名空间）
            for (const key of modelKeys) {
                if (key === 'default') continue;
                const keyLower = key.toLowerCase();
                if (normalizedName === keyLower) {
                    return modelCapabilities[key];
                }
            }
            
            // 第二轮：包含关系匹配
            for (const key of modelKeys) {
                if (key === 'default') continue;
                const keyLower = key.toLowerCase();
                if (normalizedName.includes(keyLower) || keyLower.includes(normalizedName)) {
                    return modelCapabilities[key];
                }
            }
            
            // 第三轮：智能模式匹配（处理连字符、版本号等）
            for (const key of modelKeys) {
                if (key === 'default') continue;
                
                // 标准化处理：移除连字符、点、版本号等
                const normalizeForMatching = (name) => {
                    return name.toLowerCase()
                        .replace(/[-_.]/g, '')  // 移除连字符、下划线、点
                        .replace(/:\w+$/, '')   // 移除版本标签 (:latest, :v1等)
                        .replace(/\/.*?\//, '') // 移除命名空间前缀
                        .replace(/\d+b$/, '')   // 移除模型大小标识 (7b, 13b等)
                        .replace(/v\d+/, '')    // 移除版本号 (v1, v2等)
                        .trim();
                };
                
                const normalizedInput = normalizeForMatching(normalizedName);
                const normalizedKey = normalizeForMatching(key);
                
                if (normalizedInput === normalizedKey || 
                    normalizedInput.includes(normalizedKey) || 
                    normalizedKey.includes(normalizedInput)) {
                    return modelCapabilities[key];
                }
            }
            
            // 第四轮：基础名称匹配（移除命名空间和版本）
            const baseModelName = normalizedName.split(':')[0].split('/').pop();
            for (const key of modelKeys) {
                if (key === 'default') continue;
                const baseKeyName = key.toLowerCase().split(':')[0].split('/').pop();
                if (baseModelName === baseKeyName) {
                    return modelCapabilities[key];
                }
            }

            // 如果没有找到匹配，返回默认配置
            return modelCapabilities['default'];
        }

        // 测试用例
        const testCases = [
            { input: 'qwen/qwen2.5-vl-7b', expected: 'qwen/qwen2.5-vl-7b', description: '用户实际使用的模型名称' },
            { input: 'qwen2.5vl', expected: 'qwen2.5vl', description: 'Ollama格式的模型名称' },
            { input: 'qwen/qwen2.5-vl', expected: 'qwen/qwen2.5-vl-7b', description: '无版本号的LM Studio格式' },
            { input: 'qwen2.5-vl-7b', expected: 'qwen/qwen2.5-vl-7b', description: '无命名空间的模型名称' },
            { input: 'unknown-model', expected: 'default', description: '未知模型应使用默认配置' }
        ];

        // 运行测试
        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            let passCount = 0;
            let totalCount = testCases.length;

            testCases.forEach((testCase, index) => {
                const result = getModelCapabilities(testCase.input);
                const isMatch = result === modelCapabilities[testCase.expected];
                const isImageSupported = result.image.supported;
                
                const resultDiv = document.createElement('div');
                resultDiv.className = `test-result ${isMatch ? 'success' : 'failure'}`;
                
                resultDiv.innerHTML = `
                    <strong>测试 ${index + 1}: ${testCase.description}</strong><br>
                    输入: "${testCase.input}"<br>
                    期望匹配: "${testCase.expected}"<br>
                    实际匹配: "${getMatchedKey(testCase.input)}"<br>
                    图像支持: ${isImageSupported ? '✅ 支持' : '❌ 不支持'}<br>
                    结果: ${isMatch ? '✅ 通过' : '❌ 失败'}
                `;
                
                resultsDiv.appendChild(resultDiv);
                
                if (isMatch) passCount++;
            });

            // 添加总结
            const summaryDiv = document.createElement('div');
            summaryDiv.className = `test-result info`;
            summaryDiv.innerHTML = `
                <strong>测试总结</strong><br>
                通过: ${passCount}/${totalCount}<br>
                成功率: ${(passCount/totalCount*100).toFixed(1)}%
            `;
            resultsDiv.appendChild(summaryDiv);
        }

        // 辅助函数：获取匹配的键名
        function getMatchedKey(modelName) {
            if (modelCapabilities[modelName]) {
                return modelName;
            }
            
            const result = getModelCapabilities(modelName);
            for (const [key, value] of Object.entries(modelCapabilities)) {
                if (value === result) {
                    return key;
                }
            }
            return 'default';
        }

        // 页面加载后运行测试
        window.onload = runTests;
    </script>
</body>
</html>
