<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地大模型聊天界面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .modal {
            transform: translate(-50%, -50%) scale(0.9);
            transition: all 0.3s ease;
            opacity: 0;
        }
        .modal.active {
            transform: translate(-50%, -50%) scale(1);
            opacity: 1;
        }
        .modal-backdrop {
            opacity: 0;
            pointer-events: none;
            transition: all 0.3s ease;
        }
        .modal-backdrop.active {
            opacity: 1;
            pointer-events: auto;
        }
        .btn-hover:hover {
            transform: translateY(-1px);
        }
        .status-bar {
            position: sticky;
            top: 0;
            z-index: 20;
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 状态栏 -->
    <div class="status-bar bg-white/90 border-b border-gray-200 px-4 py-2">
        <div class="flex items-center justify-between max-w-4xl mx-auto">
            <div class="flex items-center space-x-3">
                <div class="flex items-center space-x-2">
                    <div id="status-indicator" class="w-3 h-3 bg-gray-400 rounded-full"></div>
                    <span id="status-text" class="text-sm text-gray-600">未连接</span>
                </div>
                <div id="model-info" class="text-sm text-gray-500">未选择模型</div>
            </div>
            <div class="flex items-center space-x-2">
                <button id="open-settings-btn" class="text-gray-500 hover:text-blue-600 transition-colors" title="设置">
                    <i class="fa fa-cog"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 主界面 -->
    <div class="max-w-4xl mx-auto p-4">
        <!-- 聊天容器 -->
        <div id="chat-container" class="bg-white rounded-lg shadow-sm border border-gray-200 mb-4 h-96 overflow-y-auto p-4">
            <div id="welcome-message" class="text-center text-gray-500 mt-20">
                <i class="fa fa-comments text-4xl mb-4"></i>
                <p>开始与AI对话</p>
                <p class="text-sm mt-2">请先在设置中配置API连接</p>
            </div>
        </div>

        <!-- 对话统计信息 -->
        <div id="chat-stats" class="bg-white rounded-lg shadow-sm border border-gray-200 mb-4 p-3 hidden">
            <div class="flex items-center justify-between text-sm text-gray-600">
                <div class="flex items-center space-x-4">
                    <span id="message-count">消息: 0</span>
                    <span id="response-time">响应时间: --</span>
                    <span id="output-speed">输出速度: --</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span id="token-count">Token: --</span>
                    <span id="model-status">模型: --</span>
                </div>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-end space-x-3">
                <div class="flex-1">
                    <textarea id="user-input" 
                        class="w-full border border-gray-300 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none" 
                        rows="2" 
                        placeholder="输入您的问题..."></textarea>
                </div>
                <button id="send-button" 
                    class="bg-blue-600 hover:bg-blue-700 text-white rounded-lg px-6 py-3 transition-colors flex items-center space-x-2">
                    <i class="fa fa-paper-plane"></i>
                    <span>发送</span>
                </button>
            </div>
            <div class="flex justify-between items-center mt-3">
                <div class="flex space-x-2">
                    <button id="clear-chat" class="text-gray-500 hover:text-red-600 transition-colors text-sm">
                        <i class="fa fa-trash mr-1"></i>清空对话
                    </button>
                </div>
                <button id="settings-button" class="text-gray-500 hover:text-blue-600 transition-colors text-sm">
                    <i class="fa fa-cog mr-1"></i>设置
                </button>
            </div>
        </div>
    </div>

    <!-- 设置弹窗 -->
    <div id="modal-backdrop" class="modal-backdrop fixed inset-0 bg-black bg-opacity-50 z-40">
        <div id="settings-modal" class="modal fixed top-1/2 left-1/2 bg-white rounded-lg shadow-xl z-50 w-full max-w-md mx-4">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold">API设置</h3>
                    <button id="close-settings" class="text-gray-400 hover:text-gray-600">
                        <i class="fa fa-times"></i>
                    </button>
                </div>

                <div class="space-y-4">
                    <!-- API类型选择 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">API类型</label>
                        <select id="api-type" class="w-full border border-gray-300 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="ollama">Ollama</option>
                            <option value="lm-studio">LM Studio</option>
                            <option value="openai-compatible">OpenAI兼容</option>
                        </select>
                    </div>

                    <!-- API地址 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">API地址</label>
                        <input id="api-url" type="text" 
                            class="w-full border border-gray-300 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="http://localhost:11434">
                    </div>

                    <!-- API密钥 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">API密钥（可选）</label>
                        <input id="api-key" type="password" 
                            class="w-full border border-gray-300 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="留空表示无需密钥">
                    </div>

                    <!-- 模型选择 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">模型</label>
                        <select id="model-select" class="w-full border border-gray-300 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">请先测试连接</option>
                        </select>
                    </div>

                    <!-- 按钮组 -->
                    <div class="flex space-x-2 pt-4">
                        <button id="auto-detect"
                            class="flex-1 bg-purple-600 text-white rounded-lg py-3 hover:bg-purple-700 transition-colors text-sm">
                            <i class="fa fa-search mr-1"></i>自动检测
                        </button>
                        <button id="test-connection"
                            class="flex-1 bg-blue-600 text-white rounded-lg py-3 hover:bg-blue-700 transition-colors text-sm">
                            <i class="fa fa-plug mr-1"></i>测试连接
                        </button>
                        <button id="save-settings"
                            class="flex-1 bg-green-600 text-white rounded-lg py-3 hover:bg-green-700 transition-colors text-sm">
                            <i class="fa fa-save mr-1"></i>保存
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('页面开始加载...');

        // 全局配置对象
        const config = {
            apiType: 'ollama',
            apiUrl: 'http://localhost:11434',
            apiKey: '',
            model: '',
            
            save() {
                localStorage.setItem('llm-chat-config', JSON.stringify(this));
                console.log('配置已保存');
            },
            
            load() {
                const saved = localStorage.getItem('llm-chat-config');
                if (saved) {
                    Object.assign(this, JSON.parse(saved));
                    console.log('配置已加载');
                }
            }
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成，开始初始化...');
            
            // 加载配置
            config.load();
            
            // 获取DOM元素
            const elements = {
                settingsButton: document.getElementById('settings-button'),
                openSettingsBtn: document.getElementById('open-settings-btn'),
                closeSettings: document.getElementById('close-settings'),
                saveSettings: document.getElementById('save-settings'),
                testConnection: document.getElementById('test-connection'),
                sendButton: document.getElementById('send-button'),
                clearChat: document.getElementById('clear-chat'),
                userInput: document.getElementById('user-input'),
                chatContainer: document.getElementById('chat-container'),
                modalBackdrop: document.getElementById('modal-backdrop'),
                settingsModal: document.getElementById('settings-modal'),
                apiType: document.getElementById('api-type'),
                apiUrl: document.getElementById('api-url'),
                apiKey: document.getElementById('api-key'),
                modelSelect: document.getElementById('model-select'),
                statusIndicator: document.getElementById('status-indicator'),
                statusText: document.getElementById('status-text'),
                modelInfo: document.getElementById('model-info')
            };
            
            // 检查关键元素
            Object.entries(elements).forEach(([name, element]) => {
                if (element) {
                    console.log(`✓ ${name} 元素找到`);
                } else {
                    console.error(`✗ ${name} 元素未找到`);
                }
            });

            // 更新UI显示当前配置
            function updateUI() {
                elements.apiType.value = config.apiType;
                elements.apiUrl.value = config.apiUrl;
                elements.apiKey.value = config.apiKey;
                elements.modelSelect.value = config.model;

                // 更新状态显示
                if (config.model) {
                    elements.modelInfo.textContent = config.model;
                    elements.statusText.textContent = '已配置';
                    elements.statusIndicator.className = 'w-3 h-3 bg-yellow-400 rounded-full';
                } else {
                    elements.modelInfo.textContent = '未选择模型';
                    elements.statusText.textContent = '未连接';
                    elements.statusIndicator.className = 'w-3 h-3 bg-gray-400 rounded-full';
                }
            }

            // 设置弹窗控制
            function openSettings() {
                console.log('打开设置弹窗');
                elements.modalBackdrop.classList.add('active');
                elements.settingsModal.classList.add('active');
                updateUI();
            }

            function closeSettings() {
                console.log('关闭设置弹窗');
                elements.modalBackdrop.classList.remove('active');
                elements.settingsModal.classList.remove('active');
            }

            // 测试API连接
            async function testConnection() {
                console.log('开始测试连接...');
                const button = elements.testConnection;
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fa fa-spinner fa-spin mr-2"></i>测试中...';
                button.disabled = true;

                try {
                    const apiType = elements.apiType.value;
                    const apiUrl = elements.apiUrl.value;

                    console.log('测试连接参数:', { apiType, apiUrl });

                    let endpoint = '';
                    if (apiType === 'ollama') {
                        endpoint = '/api/tags';
                    } else if (apiType === 'lm-studio') {
                        endpoint = '/v1/models';
                    } else {
                        endpoint = '/v1/models';
                    }

                    const fullUrl = apiUrl + endpoint;
                    console.log('请求URL:', fullUrl);

                    const response = await fetch(fullUrl, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            ...(elements.apiKey.value && { 'Authorization': `Bearer ${elements.apiKey.value}` })
                        }
                    });

                    console.log('响应状态:', response.status, response.statusText);

                    if (response.ok) {
                        const data = await response.json();
                        console.log('API响应数据:', data);
                        console.log('数据结构分析:', {
                            hasModels: !!data.models,
                            hasData: !!data.data,
                            modelsLength: data.models?.length,
                            dataLength: data.data?.length,
                            keys: Object.keys(data)
                        });

                        // 更新模型列表
                        elements.modelSelect.innerHTML = '<option value="">请选择模型</option>';

                        let modelCount = 0;

                        if (apiType === 'ollama' && data.models) {
                            console.log('处理Ollama模型列表:', data.models);
                            data.models.forEach(model => {
                                const option = document.createElement('option');
                                option.value = model.name;
                                option.textContent = `${model.name} (${model.size || '未知大小'})`;
                                elements.modelSelect.appendChild(option);
                                modelCount++;
                            });
                        } else if (data.data) {
                            console.log('处理LM Studio模型列表:', data.data);
                            data.data.forEach(model => {
                                const option = document.createElement('option');
                                option.value = model.id;
                                option.textContent = model.id;
                                elements.modelSelect.appendChild(option);
                                modelCount++;
                            });
                        } else {
                            console.warn('未找到预期的模型数据结构');
                            console.log('完整响应数据:', data);
                        }

                        if (modelCount > 0) {
                            alert(`连接成功！找到 ${modelCount} 个模型`);
                            elements.statusIndicator.className = 'w-3 h-3 bg-green-400 rounded-full';
                            elements.statusText.textContent = `已连接 (${modelCount}个模型)`;
                        } else {
                            alert('连接成功，但未找到任何模型');
                            elements.statusIndicator.className = 'w-3 h-3 bg-yellow-400 rounded-full';
                            elements.statusText.textContent = '已连接 (无模型)';
                        }
                    } else {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                } catch (error) {
                    console.error('连接失败:', error);
                    alert(`连接失败: ${error.message}`);
                    elements.statusIndicator.className = 'w-3 h-3 bg-red-400 rounded-full';
                    elements.statusText.textContent = '连接失败';
                } finally {
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            }

            // 自动检测服务
            async function autoDetectServices() {
                console.log('开始自动检测服务...');
                const button = document.getElementById('auto-detect');
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fa fa-spinner fa-spin mr-1"></i>检测中...';
                button.disabled = true;

                const services = [
                    { name: 'Ollama', type: 'ollama', url: 'http://localhost:11434', endpoint: '/api/tags' },
                    { name: 'LM Studio', type: 'lm-studio', url: 'http://localhost:1234', endpoint: '/v1/models' }
                ];

                let foundService = null;

                for (const service of services) {
                    try {
                        console.log(`检测 ${service.name}...`);
                        const response = await fetch(service.url + service.endpoint, {
                            method: 'GET',
                            headers: { 'Content-Type': 'application/json' }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            const modelCount = data.models?.length || data.data?.length || 0;
                            console.log(`✓ ${service.name} 检测成功，找到 ${modelCount} 个模型`);

                            foundService = { ...service, modelCount, data };
                            break;
                        }
                    } catch (error) {
                        console.log(`✗ ${service.name} 检测失败:`, error.message);
                    }
                }

                if (foundService) {
                    // 自动配置
                    elements.apiType.value = foundService.type;
                    elements.apiUrl.value = foundService.url;

                    // 触发URL更新事件
                    elements.apiType.dispatchEvent(new Event('change'));

                    alert(`检测成功！\n服务: ${foundService.name}\n地址: ${foundService.url}\n模型数量: ${foundService.modelCount}\n\n请点击"测试连接"获取模型列表`);

                    elements.statusIndicator.className = 'w-3 h-3 bg-green-400 rounded-full';
                    elements.statusText.textContent = `检测到${foundService.name}`;
                } else {
                    alert('未检测到任何运行中的AI服务\n\n请确保以下服务之一正在运行：\n• Ollama (端口 11434)\n• LM Studio (端口 1234)');
                    elements.statusIndicator.className = 'w-3 h-3 bg-red-400 rounded-full';
                    elements.statusText.textContent = '未检测到服务';
                }

                button.innerHTML = originalText;
                button.disabled = false;
            }

            // 保存设置
            function saveSettings() {
                config.apiType = elements.apiType.value;
                config.apiUrl = elements.apiUrl.value;
                config.apiKey = elements.apiKey.value;
                config.model = elements.modelSelect.value;
                config.save();

                updateUI();
                closeSettings();
                alert('设置已保存！');
                console.log('设置已保存');
            }

            // 全局统计变量
            let chatStats = {
                messageCount: 0,
                totalTokens: 0,
                lastResponseTime: 0,
                lastOutputSpeed: 0
            };

            // 发送消息
            async function sendMessage() {
                const message = elements.userInput.value.trim();
                if (!message) return;

                if (!config.model) {
                    alert('请先在设置中选择模型');
                    return;
                }

                console.log('发送消息:', message);
                elements.userInput.value = '';

                // 隐藏欢迎消息，显示统计信息
                const welcomeMessage = document.getElementById('welcome-message');
                const chatStatsDiv = document.getElementById('chat-stats');
                if (welcomeMessage) welcomeMessage.style.display = 'none';
                if (chatStatsDiv) chatStatsDiv.classList.remove('hidden');

                // 添加用户消息到聊天容器
                addMessage('user', message);
                chatStats.messageCount++;

                // 显示AI正在思考
                const thinkingId = addMessage('assistant', '🤔 正在思考...', true);

                // 记录开始时间
                const startTime = Date.now();

                try {
                    // 构建API请求
                    let apiUrl, requestBody;

                    if (config.apiType === 'ollama') {
                        apiUrl = `${config.apiUrl}/api/generate`;
                        requestBody = {
                            model: config.model,
                            prompt: message,
                            stream: false
                        };
                    } else {
                        apiUrl = `${config.apiUrl}/v1/chat/completions`;
                        requestBody = {
                            model: config.model,
                            messages: [{ role: 'user', content: message }],
                            stream: false
                        };
                    }

                    console.log('发送请求到:', apiUrl);
                    console.log('请求体:', requestBody);

                    const response = await fetch(apiUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            ...(config.apiKey && { 'Authorization': `Bearer ${config.apiKey}` })
                        },
                        body: JSON.stringify(requestBody)
                    });

                    const endTime = Date.now();
                    const responseTime = endTime - startTime;
                    chatStats.lastResponseTime = responseTime;

                    if (response.ok) {
                        const data = await response.json();
                        console.log('API响应:', data);

                        let aiResponse = '';
                        let tokenInfo = {};

                        if (config.apiType === 'ollama') {
                            aiResponse = data.response || '抱歉，没有收到回复';
                            tokenInfo = {
                                promptTokens: data.prompt_eval_count || 0,
                                completionTokens: data.eval_count || 0,
                                totalTokens: (data.prompt_eval_count || 0) + (data.eval_count || 0)
                            };
                        } else {
                            aiResponse = data.choices?.[0]?.message?.content || '抱歉，没有收到回复';
                            tokenInfo = {
                                promptTokens: data.usage?.prompt_tokens || 0,
                                completionTokens: data.usage?.completion_tokens || 0,
                                totalTokens: data.usage?.total_tokens || 0
                            };
                        }

                        // 计算输出速度 (字符/秒)
                        const outputSpeed = aiResponse.length / (responseTime / 1000);
                        chatStats.lastOutputSpeed = outputSpeed;
                        chatStats.totalTokens += tokenInfo.totalTokens;

                        // 更新AI消息
                        updateMessage(thinkingId, aiResponse);

                        // 更新统计信息
                        updateChatStats(tokenInfo);

                    } else {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                } catch (error) {
                    console.error('发送消息失败:', error);
                    updateMessage(thinkingId, `❌ 错误: ${error.message}`);

                    // 更新统计信息显示错误
                    updateChatStats({ error: error.message });
                }
            }

            // 更新聊天统计信息
            function updateChatStats(tokenInfo = {}) {
                const messageCountEl = document.getElementById('message-count');
                const responseTimeEl = document.getElementById('response-time');
                const outputSpeedEl = document.getElementById('output-speed');
                const tokenCountEl = document.getElementById('token-count');
                const modelStatusEl = document.getElementById('model-status');

                if (messageCountEl) {
                    messageCountEl.textContent = `消息: ${chatStats.messageCount}`;
                }

                if (responseTimeEl) {
                    responseTimeEl.textContent = `响应时间: ${chatStats.lastResponseTime}ms`;
                }

                if (outputSpeedEl) {
                    const speed = chatStats.lastOutputSpeed.toFixed(1);
                    outputSpeedEl.textContent = `输出速度: ${speed} 字符/秒`;
                }

                if (tokenCountEl) {
                    if (tokenInfo.error) {
                        tokenCountEl.textContent = `Token: 错误`;
                    } else {
                        tokenCountEl.textContent = `Token: ${tokenInfo.totalTokens || 0} (总计: ${chatStats.totalTokens})`;
                    }
                }

                if (modelStatusEl) {
                    modelStatusEl.textContent = `模型: ${config.model}`;
                }
            }

            // 添加消息到聊天容器
            function addMessage(role, content, isTemporary = false) {
                const messageId = 'msg-' + Date.now();
                const messageDiv = document.createElement('div');
                messageDiv.id = messageId;
                messageDiv.className = `mb-4 ${role === 'user' ? 'text-right' : 'text-left'}`;

                // 创建消息气泡
                const bubble = document.createElement('div');
                bubble.className = `inline-block max-w-xs lg:max-w-md px-4 py-3 rounded-lg ${
                    role === 'user'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 text-gray-800'
                }`;

                // 添加消息内容
                const contentDiv = document.createElement('div');
                contentDiv.textContent = content;
                bubble.appendChild(contentDiv);

                // 添加时间戳
                const timestamp = document.createElement('div');
                timestamp.className = `text-xs mt-1 ${
                    role === 'user' ? 'text-blue-200' : 'text-gray-500'
                }`;
                timestamp.textContent = new Date().toLocaleTimeString();
                bubble.appendChild(timestamp);

                messageDiv.appendChild(bubble);
                elements.chatContainer.appendChild(messageDiv);
                elements.chatContainer.scrollTop = elements.chatContainer.scrollHeight;

                return messageId;
            }

            // 更新消息内容
            function updateMessage(messageId, newContent) {
                const messageDiv = document.getElementById(messageId);
                if (messageDiv) {
                    const bubble = messageDiv.querySelector('div');
                    const contentDiv = bubble.querySelector('div:first-child');
                    if (contentDiv) {
                        contentDiv.textContent = newContent;

                        // 更新时间戳
                        const timestamp = bubble.querySelector('div:last-child');
                        if (timestamp) {
                            timestamp.textContent = new Date().toLocaleTimeString();
                        }
                    }
                }
            }

            // 清空聊天
            function clearChat() {
                if (confirm('确定要清空所有对话吗？')) {
                    // 重置统计信息
                    chatStats = {
                        messageCount: 0,
                        totalTokens: 0,
                        lastResponseTime: 0,
                        lastOutputSpeed: 0
                    };

                    // 隐藏统计信息，显示欢迎消息
                    const chatStatsDiv = document.getElementById('chat-stats');
                    if (chatStatsDiv) chatStatsDiv.classList.add('hidden');

                    elements.chatContainer.innerHTML = `
                        <div id="welcome-message" class="text-center text-gray-500 mt-20">
                            <i class="fa fa-comments text-4xl mb-4"></i>
                            <p>开始与AI对话</p>
                            <p class="text-sm mt-2">请先在设置中配置API连接</p>
                        </div>
                    `;
                    console.log('聊天已清空，统计信息已重置');
                }
            }

            // 绑定事件监听器
            console.log('开始绑定事件监听器...');

            // 设置按钮事件
            if (elements.settingsButton) {
                elements.settingsButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('主设置按钮被点击');
                    openSettings();
                });
                console.log('✓ 主设置按钮事件已绑定');
            }

            if (elements.openSettingsBtn) {
                elements.openSettingsBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('状态栏设置按钮被点击');
                    openSettings();
                });
                console.log('✓ 状态栏设置按钮事件已绑定');
            }

            // 关闭设置事件
            if (elements.closeSettings) {
                elements.closeSettings.addEventListener('click', closeSettings);
                console.log('✓ 关闭设置按钮事件已绑定');
            }

            // 模态框背景点击关闭
            if (elements.modalBackdrop) {
                elements.modalBackdrop.addEventListener('click', function(e) {
                    if (e.target === elements.modalBackdrop) {
                        closeSettings();
                    }
                });
                console.log('✓ 模态框背景点击事件已绑定');
            }

            // 自动检测按钮
            const autoDetectBtn = document.getElementById('auto-detect');
            if (autoDetectBtn) {
                autoDetectBtn.addEventListener('click', autoDetectServices);
                console.log('✓ 自动检测按钮事件已绑定');
            }

            // 测试连接按钮
            if (elements.testConnection) {
                elements.testConnection.addEventListener('click', testConnection);
                console.log('✓ 测试连接按钮事件已绑定');
            }

            // 保存设置按钮
            if (elements.saveSettings) {
                elements.saveSettings.addEventListener('click', saveSettings);
                console.log('✓ 保存设置按钮事件已绑定');
            }

            // 发送消息按钮
            if (elements.sendButton) {
                elements.sendButton.addEventListener('click', sendMessage);
                console.log('✓ 发送按钮事件已绑定');
            }

            // 输入框回车发送
            if (elements.userInput) {
                elements.userInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendMessage();
                    }
                });
                console.log('✓ 输入框回车事件已绑定');
            }

            // 清空聊天按钮
            if (elements.clearChat) {
                elements.clearChat.addEventListener('click', clearChat);
                console.log('✓ 清空聊天按钮事件已绑定');
            }

            // API类型变化时更新默认URL
            if (elements.apiType) {
                elements.apiType.addEventListener('change', function() {
                    const apiType = this.value;
                    if (apiType === 'ollama') {
                        elements.apiUrl.value = 'http://localhost:11434';
                    } else if (apiType === 'lm-studio') {
                        elements.apiUrl.value = 'http://localhost:1234';
                    } else {
                        elements.apiUrl.value = 'http://localhost:1234';
                    }
                });
                console.log('✓ API类型变化事件已绑定');
            }

            // 初始化UI
            updateUI();

            console.log('=== 初始化完成 ===');
            console.log('页面已准备就绪，所有功能应该正常工作');
        });

        // 全局错误处理
        window.addEventListener('error', function(e) {
            console.error('全局错误:', e.error);
        });

        console.log('脚本加载完成');
    </script>
</body>
</html>
