<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地大模型聊天界面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .modal {
            transform: translate(-50%, -50%) scale(0.9);
            transition: all 0.3s ease;
            opacity: 0;
        }
        .modal.active {
            transform: translate(-50%, -50%) scale(1);
            opacity: 1;
        }
        .modal-backdrop {
            opacity: 0;
            pointer-events: none;
            transition: all 0.3s ease;
        }
        .modal-backdrop.active {
            opacity: 1;
            pointer-events: auto;
        }
        .btn-hover:hover {
            transform: translateY(-1px);
        }
        .status-bar {
            position: sticky;
            top: 0;
            z-index: 20;
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 状态栏 -->
    <div class="status-bar bg-white/90 border-b border-gray-200 px-4 py-2">
        <div class="flex items-center justify-between max-w-4xl mx-auto">
            <div class="flex items-center space-x-3">
                <div class="flex items-center space-x-2">
                    <div id="status-indicator" class="w-3 h-3 bg-gray-400 rounded-full"></div>
                    <span id="status-text" class="text-sm text-gray-600">未连接</span>
                </div>
                <div id="model-info" class="text-sm text-gray-500">未选择模型</div>
            </div>
            <div class="flex items-center space-x-2">
                <button id="open-settings-btn" class="text-gray-500 hover:text-blue-600 transition-colors" title="设置">
                    <i class="fa fa-cog"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 主界面 -->
    <div class="max-w-4xl mx-auto p-4">
        <!-- 聊天容器 -->
        <div id="chat-container" class="bg-white rounded-lg shadow-sm border border-gray-200 mb-4 h-96 overflow-y-auto p-4">
            <div class="text-center text-gray-500 mt-20">
                <i class="fa fa-comments text-4xl mb-4"></i>
                <p>开始与AI对话</p>
                <p class="text-sm mt-2">请先在设置中配置API连接</p>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-end space-x-3">
                <div class="flex-1">
                    <textarea id="user-input" 
                        class="w-full border border-gray-300 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none" 
                        rows="2" 
                        placeholder="输入您的问题..."></textarea>
                </div>
                <button id="send-button" 
                    class="bg-blue-600 hover:bg-blue-700 text-white rounded-lg px-6 py-3 transition-colors flex items-center space-x-2">
                    <i class="fa fa-paper-plane"></i>
                    <span>发送</span>
                </button>
            </div>
            <div class="flex justify-between items-center mt-3">
                <div class="flex space-x-2">
                    <button id="clear-chat" class="text-gray-500 hover:text-red-600 transition-colors text-sm">
                        <i class="fa fa-trash mr-1"></i>清空对话
                    </button>
                </div>
                <button id="settings-button" class="text-gray-500 hover:text-blue-600 transition-colors text-sm">
                    <i class="fa fa-cog mr-1"></i>设置
                </button>
            </div>
        </div>
    </div>

    <!-- 设置弹窗 -->
    <div id="modal-backdrop" class="modal-backdrop fixed inset-0 bg-black bg-opacity-50 z-40">
        <div id="settings-modal" class="modal fixed top-1/2 left-1/2 bg-white rounded-lg shadow-xl z-50 w-full max-w-md mx-4">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold">API设置</h3>
                    <button id="close-settings" class="text-gray-400 hover:text-gray-600">
                        <i class="fa fa-times"></i>
                    </button>
                </div>

                <div class="space-y-4">
                    <!-- API类型选择 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">API类型</label>
                        <select id="api-type" class="w-full border border-gray-300 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="ollama">Ollama</option>
                            <option value="lm-studio">LM Studio</option>
                            <option value="openai-compatible">OpenAI兼容</option>
                        </select>
                    </div>

                    <!-- API地址 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">API地址</label>
                        <input id="api-url" type="text" 
                            class="w-full border border-gray-300 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="http://localhost:11434">
                    </div>

                    <!-- API密钥 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">API密钥（可选）</label>
                        <input id="api-key" type="password" 
                            class="w-full border border-gray-300 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="留空表示无需密钥">
                    </div>

                    <!-- 模型选择 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">模型</label>
                        <select id="model-select" class="w-full border border-gray-300 rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">请先测试连接</option>
                        </select>
                    </div>

                    <!-- 按钮组 -->
                    <div class="flex space-x-3 pt-4">
                        <button id="test-connection" 
                            class="flex-1 bg-blue-600 text-white rounded-lg py-3 hover:bg-blue-700 transition-colors">
                            <i class="fa fa-plug mr-2"></i>测试连接
                        </button>
                        <button id="save-settings" 
                            class="flex-1 bg-green-600 text-white rounded-lg py-3 hover:bg-green-700 transition-colors">
                            <i class="fa fa-save mr-2"></i>保存
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('页面开始加载...');

        // 全局配置对象
        const config = {
            apiType: 'ollama',
            apiUrl: 'http://localhost:11434',
            apiKey: '',
            model: '',
            
            save() {
                localStorage.setItem('llm-chat-config', JSON.stringify(this));
                console.log('配置已保存');
            },
            
            load() {
                const saved = localStorage.getItem('llm-chat-config');
                if (saved) {
                    Object.assign(this, JSON.parse(saved));
                    console.log('配置已加载');
                }
            }
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成，开始初始化...');
            
            // 加载配置
            config.load();
            
            // 获取DOM元素
            const elements = {
                settingsButton: document.getElementById('settings-button'),
                openSettingsBtn: document.getElementById('open-settings-btn'),
                closeSettings: document.getElementById('close-settings'),
                saveSettings: document.getElementById('save-settings'),
                testConnection: document.getElementById('test-connection'),
                sendButton: document.getElementById('send-button'),
                clearChat: document.getElementById('clear-chat'),
                userInput: document.getElementById('user-input'),
                chatContainer: document.getElementById('chat-container'),
                modalBackdrop: document.getElementById('modal-backdrop'),
                settingsModal: document.getElementById('settings-modal'),
                apiType: document.getElementById('api-type'),
                apiUrl: document.getElementById('api-url'),
                apiKey: document.getElementById('api-key'),
                modelSelect: document.getElementById('model-select'),
                statusIndicator: document.getElementById('status-indicator'),
                statusText: document.getElementById('status-text'),
                modelInfo: document.getElementById('model-info')
            };
            
            // 检查关键元素
            Object.entries(elements).forEach(([name, element]) => {
                if (element) {
                    console.log(`✓ ${name} 元素找到`);
                } else {
                    console.error(`✗ ${name} 元素未找到`);
                }
            });

            // 更新UI显示当前配置
            function updateUI() {
                elements.apiType.value = config.apiType;
                elements.apiUrl.value = config.apiUrl;
                elements.apiKey.value = config.apiKey;
                elements.modelSelect.value = config.model;

                // 更新状态显示
                if (config.model) {
                    elements.modelInfo.textContent = config.model;
                    elements.statusText.textContent = '已配置';
                    elements.statusIndicator.className = 'w-3 h-3 bg-yellow-400 rounded-full';
                } else {
                    elements.modelInfo.textContent = '未选择模型';
                    elements.statusText.textContent = '未连接';
                    elements.statusIndicator.className = 'w-3 h-3 bg-gray-400 rounded-full';
                }
            }

            // 设置弹窗控制
            function openSettings() {
                console.log('打开设置弹窗');
                elements.modalBackdrop.classList.add('active');
                elements.settingsModal.classList.add('active');
                updateUI();
            }

            function closeSettings() {
                console.log('关闭设置弹窗');
                elements.modalBackdrop.classList.remove('active');
                elements.settingsModal.classList.remove('active');
            }

            // 测试API连接
            async function testConnection() {
                console.log('开始测试连接...');
                const button = elements.testConnection;
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fa fa-spinner fa-spin mr-2"></i>测试中...';
                button.disabled = true;

                try {
                    const apiType = elements.apiType.value;
                    const apiUrl = elements.apiUrl.value;

                    let endpoint = '';
                    if (apiType === 'ollama') {
                        endpoint = '/api/tags';
                    } else if (apiType === 'lm-studio') {
                        endpoint = '/v1/models';
                    } else {
                        endpoint = '/v1/models';
                    }

                    const response = await fetch(apiUrl + endpoint, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            ...(elements.apiKey.value && { 'Authorization': `Bearer ${elements.apiKey.value}` })
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        console.log('连接成功:', data);

                        // 更新模型列表
                        elements.modelSelect.innerHTML = '<option value="">请选择模型</option>';

                        if (apiType === 'ollama' && data.models) {
                            data.models.forEach(model => {
                                const option = document.createElement('option');
                                option.value = model.name;
                                option.textContent = model.name;
                                elements.modelSelect.appendChild(option);
                            });
                        } else if (data.data) {
                            data.data.forEach(model => {
                                const option = document.createElement('option');
                                option.value = model.id;
                                option.textContent = model.id;
                                elements.modelSelect.appendChild(option);
                            });
                        }

                        alert(`连接成功！找到 ${data.models?.length || data.data?.length || 0} 个模型`);
                        elements.statusIndicator.className = 'w-3 h-3 bg-green-400 rounded-full';
                        elements.statusText.textContent = '连接成功';
                    } else {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                } catch (error) {
                    console.error('连接失败:', error);
                    alert(`连接失败: ${error.message}`);
                    elements.statusIndicator.className = 'w-3 h-3 bg-red-400 rounded-full';
                    elements.statusText.textContent = '连接失败';
                } finally {
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            }

            // 保存设置
            function saveSettings() {
                config.apiType = elements.apiType.value;
                config.apiUrl = elements.apiUrl.value;
                config.apiKey = elements.apiKey.value;
                config.model = elements.modelSelect.value;
                config.save();

                updateUI();
                closeSettings();
                alert('设置已保存！');
                console.log('设置已保存');
            }

            // 发送消息
            async function sendMessage() {
                const message = elements.userInput.value.trim();
                if (!message) return;

                if (!config.model) {
                    alert('请先在设置中选择模型');
                    return;
                }

                console.log('发送消息:', message);
                elements.userInput.value = '';

                // 添加用户消息到聊天容器
                addMessage('user', message);

                // 显示AI正在思考
                const thinkingId = addMessage('assistant', '正在思考...', true);

                try {
                    // 构建API请求
                    let apiUrl, requestBody;

                    if (config.apiType === 'ollama') {
                        apiUrl = `${config.apiUrl}/api/generate`;
                        requestBody = {
                            model: config.model,
                            prompt: message,
                            stream: false
                        };
                    } else {
                        apiUrl = `${config.apiUrl}/chat/completions`;
                        requestBody = {
                            model: config.model,
                            messages: [{ role: 'user', content: message }],
                            stream: false
                        };
                    }

                    const response = await fetch(apiUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            ...(config.apiKey && { 'Authorization': `Bearer ${config.apiKey}` })
                        },
                        body: JSON.stringify(requestBody)
                    });

                    if (response.ok) {
                        const data = await response.json();
                        let aiResponse = '';

                        if (config.apiType === 'ollama') {
                            aiResponse = data.response || '抱歉，没有收到回复';
                        } else {
                            aiResponse = data.choices?.[0]?.message?.content || '抱歉，没有收到回复';
                        }

                        // 更新AI消息
                        updateMessage(thinkingId, aiResponse);
                    } else {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                } catch (error) {
                    console.error('发送消息失败:', error);
                    updateMessage(thinkingId, `错误: ${error.message}`);
                }
            }

            // 添加消息到聊天容器
            function addMessage(role, content, isTemporary = false) {
                const messageId = 'msg-' + Date.now();
                const messageDiv = document.createElement('div');
                messageDiv.id = messageId;
                messageDiv.className = `mb-4 ${role === 'user' ? 'text-right' : 'text-left'}`;

                const bubble = document.createElement('div');
                bubble.className = `inline-block max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    role === 'user'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 text-gray-800'
                }`;
                bubble.textContent = content;

                messageDiv.appendChild(bubble);
                elements.chatContainer.appendChild(messageDiv);
                elements.chatContainer.scrollTop = elements.chatContainer.scrollHeight;

                return messageId;
            }

            // 更新消息内容
            function updateMessage(messageId, newContent) {
                const messageDiv = document.getElementById(messageId);
                if (messageDiv) {
                    const bubble = messageDiv.querySelector('div');
                    bubble.textContent = newContent;
                }
            }

            // 清空聊天
            function clearChat() {
                if (confirm('确定要清空所有对话吗？')) {
                    elements.chatContainer.innerHTML = `
                        <div class="text-center text-gray-500 mt-20">
                            <i class="fa fa-comments text-4xl mb-4"></i>
                            <p>开始与AI对话</p>
                            <p class="text-sm mt-2">请先在设置中配置API连接</p>
                        </div>
                    `;
                    console.log('聊天已清空');
                }
            }

            // 绑定事件监听器
            console.log('开始绑定事件监听器...');

            // 设置按钮事件
            if (elements.settingsButton) {
                elements.settingsButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('主设置按钮被点击');
                    openSettings();
                });
                console.log('✓ 主设置按钮事件已绑定');
            }

            if (elements.openSettingsBtn) {
                elements.openSettingsBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('状态栏设置按钮被点击');
                    openSettings();
                });
                console.log('✓ 状态栏设置按钮事件已绑定');
            }

            // 关闭设置事件
            if (elements.closeSettings) {
                elements.closeSettings.addEventListener('click', closeSettings);
                console.log('✓ 关闭设置按钮事件已绑定');
            }

            // 模态框背景点击关闭
            if (elements.modalBackdrop) {
                elements.modalBackdrop.addEventListener('click', function(e) {
                    if (e.target === elements.modalBackdrop) {
                        closeSettings();
                    }
                });
                console.log('✓ 模态框背景点击事件已绑定');
            }

            // 测试连接按钮
            if (elements.testConnection) {
                elements.testConnection.addEventListener('click', testConnection);
                console.log('✓ 测试连接按钮事件已绑定');
            }

            // 保存设置按钮
            if (elements.saveSettings) {
                elements.saveSettings.addEventListener('click', saveSettings);
                console.log('✓ 保存设置按钮事件已绑定');
            }

            // 发送消息按钮
            if (elements.sendButton) {
                elements.sendButton.addEventListener('click', sendMessage);
                console.log('✓ 发送按钮事件已绑定');
            }

            // 输入框回车发送
            if (elements.userInput) {
                elements.userInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendMessage();
                    }
                });
                console.log('✓ 输入框回车事件已绑定');
            }

            // 清空聊天按钮
            if (elements.clearChat) {
                elements.clearChat.addEventListener('click', clearChat);
                console.log('✓ 清空聊天按钮事件已绑定');
            }

            // API类型变化时更新默认URL
            if (elements.apiType) {
                elements.apiType.addEventListener('change', function() {
                    const apiType = this.value;
                    if (apiType === 'ollama') {
                        elements.apiUrl.value = 'http://localhost:11434';
                    } else if (apiType === 'lm-studio') {
                        elements.apiUrl.value = 'http://localhost:1234/v1';
                    } else {
                        elements.apiUrl.value = 'http://localhost:1234/v1';
                    }
                });
                console.log('✓ API类型变化事件已绑定');
            }

            // 初始化UI
            updateUI();

            console.log('=== 初始化完成 ===');
            console.log('页面已准备就绪，所有功能应该正常工作');
        });

        // 全局错误处理
        window.addEventListener('error', function(e) {
            console.error('全局错误:', e.error);
        });

        console.log('脚本加载完成');
    </script>
</body>
</html>
