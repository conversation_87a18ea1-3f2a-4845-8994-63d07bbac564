<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>监控功能测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">📊 监控功能测试页面</h1>
        
        <!-- 测试控制面板 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">🎮 测试控制</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button id="test-chars" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    <i class="fa fa-font mr-2"></i>测试字符计数
                </button>
                <button id="test-timing" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    <i class="fa fa-clock mr-2"></i>测试计时功能
                </button>
                <button id="test-streaming" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    <i class="fa fa-tachometer-alt mr-2"></i>测试流式速度
                </button>
                <button id="test-connection" class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600">
                    <i class="fa fa-link mr-2"></i>测试连接状态
                </button>
            </div>
        </div>

        <!-- 模拟监控面板 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">📈 实时监控面板</h2>
            
            <!-- 第一行：基础统计 -->
            <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3 mb-4">
                <div class="bg-gray-50 rounded-lg p-3 text-center border-l-4 border-blue-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-comments mr-1"></i>对话轮数
                    </div>
                    <div id="conversation-count" class="font-semibold text-blue-600">0</div>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-3 text-center border-l-4 border-green-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-clock mr-1"></i>响应时间
                    </div>
                    <div id="response-time" class="font-semibold text-green-600">--</div>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-3 text-center border-l-4 border-purple-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-font mr-1"></i>总字符数
                    </div>
                    <div id="total-chars" class="font-semibold text-purple-600">0</div>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-3 text-center border-l-4 border-indigo-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-hourglass-half mr-1"></i>会话时长
                    </div>
                    <div id="session-duration" class="font-semibold text-indigo-600">00:00:00</div>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-3 text-center border-l-4 border-orange-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-calculator mr-1"></i>平均字符
                    </div>
                    <div id="average-chars" class="font-semibold text-orange-600">--</div>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-3 text-center border-l-4 border-teal-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-cog mr-1"></i>当前模式
                    </div>
                    <div id="current-mode" class="font-semibold text-teal-600">文本</div>
                </div>
            </div>
            
            <!-- 第二行：性能指标 -->
            <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3">
                <div class="bg-gray-50 rounded-lg p-3 text-center border-l-4 border-red-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-bolt mr-1"></i>首字节延迟
                    </div>
                    <div id="first-byte-time" class="font-semibold text-red-600">--</div>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-3 text-center border-l-4 border-yellow-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-tachometer-alt mr-1"></i>输出速度
                    </div>
                    <div id="streaming-speed" class="font-semibold text-yellow-600">--</div>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-3 text-center border-l-4 border-green-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-link mr-1"></i>连接状态
                    </div>
                    <div id="connection-status" class="font-semibold text-green-600">未连接</div>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-3 text-center border-l-4 border-blue-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-cube mr-1"></i>模型状态
                    </div>
                    <div id="model-status" class="font-semibold text-blue-600">未知</div>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-3 text-center border-l-4 border-pink-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-wifi mr-1"></i>网络延迟
                    </div>
                    <div id="network-latency" class="font-semibold text-pink-600">--</div>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-3 text-center border-l-4 border-gray-500">
                    <div class="text-xs text-gray-500 mb-1 flex items-center justify-center">
                        <i class="fa fa-chart-bar mr-1"></i>详细统计
                    </div>
                    <button id="show-details" class="font-semibold text-gray-600 hover:text-gray-800">查看</button>
                </div>
            </div>
        </div>

        <!-- 测试日志 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">📝 测试日志</h2>
            <div id="test-log" class="bg-gray-50 rounded p-4 h-64 overflow-y-auto font-mono text-sm">
                <div class="text-green-600">[系统] 监控功能测试页面已加载</div>
            </div>
        </div>
    </div>

    <script>
        // 模拟监控数据
        let testData = {
            conversationCount: 0,
            userChars: 0,
            aiChars: 0,
            sessionStart: Date.now(),
            firstMessageTime: null,
            responseTime: 0,
            firstByteTime: 0,
            streamingSpeed: 0,
            connectionStatus: 'disconnected',
            modelStatus: 'unknown',
            networkLatency: 0
        };

        // 日志函数
        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: 'text-blue-600',
                success: 'text-green-600',
                warning: 'text-yellow-600',
                error: 'text-red-600'
            };
            
            const logEntry = document.createElement('div');
            logEntry.className = colors[type] || 'text-gray-600';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // 更新显示
        function updateDisplay() {
            // 基础统计
            document.getElementById('conversation-count').textContent = testData.conversationCount;
            document.getElementById('response-time').textContent = testData.responseTime > 0 ? `${(testData.responseTime / 1000).toFixed(1)}s` : '--';
            document.getElementById('total-chars').textContent = (testData.userChars + testData.aiChars).toLocaleString();
            
            // 会话时长
            const duration = Math.floor((Date.now() - (testData.firstMessageTime || testData.sessionStart)) / 1000);
            const hours = Math.floor(duration / 3600);
            const minutes = Math.floor((duration % 3600) / 60);
            const seconds = duration % 60;
            document.getElementById('session-duration').textContent = 
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            
            // 平均字符
            const avgChars = testData.conversationCount > 0 ? 
                Math.round((testData.userChars + testData.aiChars) / testData.conversationCount) : 0;
            document.getElementById('average-chars').textContent = avgChars > 0 ? avgChars.toLocaleString() : '--';
            
            // 性能指标
            document.getElementById('first-byte-time').textContent = testData.firstByteTime > 0 ? `${testData.firstByteTime}ms` : '--';
            document.getElementById('streaming-speed').textContent = testData.streamingSpeed > 0 ? `${testData.streamingSpeed} 字符/秒` : '--';
            document.getElementById('connection-status').textContent = testData.connectionStatus;
            document.getElementById('model-status').textContent = testData.modelStatus;
            document.getElementById('network-latency').textContent = testData.networkLatency > 0 ? `${testData.networkLatency}ms` : '--';
        }

        // 测试函数
        document.getElementById('test-chars').addEventListener('click', function() {
            testData.userChars += Math.floor(Math.random() * 100) + 50;
            testData.aiChars += Math.floor(Math.random() * 200) + 100;
            log(`字符计数测试：用户+${testData.userChars}，AI+${testData.aiChars}`, 'success');
            updateDisplay();
        });

        document.getElementById('test-timing').addEventListener('click', function() {
            testData.responseTime = Math.floor(Math.random() * 3000) + 500;
            testData.firstByteTime = Math.floor(Math.random() * 1000) + 100;
            if (!testData.firstMessageTime) {
                testData.firstMessageTime = Date.now();
            }
            log(`计时测试：响应时间${testData.responseTime}ms，首字节${testData.firstByteTime}ms`, 'success');
            updateDisplay();
        });

        document.getElementById('test-streaming').addEventListener('click', function() {
            testData.streamingSpeed = Math.floor(Math.random() * 80) + 20;
            testData.conversationCount++;
            log(`流式速度测试：${testData.streamingSpeed}字符/秒，对话轮数+1`, 'success');
            updateDisplay();
        });

        document.getElementById('test-connection').addEventListener('click', function() {
            const statuses = ['connected', 'connecting', 'failed', 'disconnected'];
            const modelStatuses = ['loaded', 'loading', 'failed', 'unknown'];
            testData.connectionStatus = statuses[Math.floor(Math.random() * statuses.length)];
            testData.modelStatus = modelStatuses[Math.floor(Math.random() * modelStatuses.length)];
            testData.networkLatency = Math.floor(Math.random() * 500) + 50;
            log(`连接状态测试：${testData.connectionStatus}，模型：${testData.modelStatus}，延迟：${testData.networkLatency}ms`, 'success');
            updateDisplay();
        });

        // 定时更新
        setInterval(updateDisplay, 1000);
        
        // 初始化
        log('所有测试功能已就绪，点击按钮开始测试', 'info');
        updateDisplay();
    </script>
</body>
</html>
