<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模型能力测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <h1>模型能力匹配测试</h1>
    <div id="test-results"></div>

    <script>
        // 从主页面复制模型能力数据库
        const modelCapabilities = {
            // === 用户的Ollama模型 ===
            'llama3.3:latest': {
                text: { supported: true, description: 'Meta最新的Llama 3.3模型，70B参数规模，在推理、代码生成和复杂问答方面表现卓越，支持多语言对话。' },
                image: { supported: false, description: '此模型专注于文本生成，不支持图像理解功能。' },
                audio: { supported: false, description: '此模型不支持语音交互功能。' }
            },
            'mistral-small3.2:latest': {
                text: { supported: true, description: 'Mistral AI的Small 3.2模型，22B参数，在保持高质量输出的同时提供快速响应，擅长逻辑推理和代码任务。' },
                image: { supported: false, description: '此模型专注于文本处理，不支持图像理解功能。' },
                audio: { supported: false, description: '此模型不支持语音交互功能。' }
            },
            'yasserrmd/Qwen2.5-7B-Instruct-1M:latest': {
                text: { supported: true, description: 'Qwen2.5-7B指令微调版本，支持100万token超长上下文，在中英文理解、长文档分析和复杂推理方面表现优秀。' },
                image: { supported: false, description: '此模型专注于长文本处理，不支持图像理解功能。' },
                audio: { supported: false, description: '此模型不支持语音交互功能。' }
            },
            'erwan2/DeepSeek-Janus-Pro-7B:latest': {
                text: { supported: true, description: 'DeepSeek Janus Pro多模态模型，7B参数，专为视觉-语言任务设计，在图像理解和文本生成方面均表现出色。' },
                image: { supported: true, description: '强大的视觉理解能力，支持图像描述、场景分析、OCR文字识别、图表解读和复杂视觉推理任务。' },
                audio: { supported: false, description: '此模型不支持语音交互功能。' }
            },
            'qwen2.5vl:latest': {
                text: { supported: true, description: '阿里巴巴Qwen2.5-VL多模态模型，在中英文文本生成和理解方面表现优异，特别适合中文用户。' },
                image: { supported: true, description: '先进的视觉语言模型，支持图像理解、中文OCR、图表分析、场景描述和多轮视觉对话，在中文视觉问答方面表现突出。' },
                audio: { supported: false, description: '此模型不支持语音交互功能。' }
            },
            'qwen3:latest': {
                text: { supported: true, description: 'Qwen3最新版本，在中英文理解、代码生成、数学推理和创意写作方面有显著提升，响应速度快。' },
                image: { supported: false, description: '此模型专注于文本处理，不支持图像理解功能。' },
                audio: { supported: false, description: '此模型不支持语音交互功能。' }
            },
            'nomic-embed-text:latest': {
                text: { supported: false, description: '这是一个文本嵌入模型，用于生成文本向量表示，不适用于对话生成任务。请选择其他对话模型。' },
                image: { supported: false, description: '嵌入模型不支持图像理解功能。' },
                audio: { supported: false, description: '嵌入模型不支持语音交互功能。' }
            },
            'default': {
                text: { supported: true, description: '未知模型类型，假设支持基础文本对话功能。建议查看模型文档确认具体能力。' },
                image: { supported: false, description: '未知模型的图像理解能力，建议查看模型文档或尝试上传图片测试。' },
                audio: { supported: false, description: '未知模型的语音交互能力，建议查看模型文档确认是否支持。' }
            }
        };

        // 智能模型匹配函数
        function getModelCapabilities(modelName) {
            // 直接匹配
            if (modelCapabilities[modelName]) {
                return modelCapabilities[modelName];
            }
            
            // 模糊匹配 - 处理版本标签和命名空间
            const normalizedName = modelName.toLowerCase();
            
            // 匹配规则：检查模型名称中是否包含已知模型的关键词
            const modelKeys = Object.keys(modelCapabilities);
            for (const key of modelKeys) {
                if (key === 'default') continue;
                
                const keyLower = key.toLowerCase();
                // 检查是否为完全匹配或包含关系
                if (normalizedName.includes(keyLower) || keyLower.includes(normalizedName)) {
                    return modelCapabilities[key];
                }
                
                // 特殊处理：移除版本标签后匹配
                const baseModelName = normalizedName.split(':')[0].split('/').pop();
                const baseKeyName = keyLower.split(':')[0].split('/').pop();
                if (baseModelName === baseKeyName) {
                    return modelCapabilities[key];
                }
            }
            
            // 如果没有找到匹配，返回默认配置
            return modelCapabilities['default'];
        }

        // 测试用例
        const testCases = [
            // 完全匹配测试
            { input: 'llama3.3:latest', expected: 'llama3.3:latest', description: '完全匹配 - Llama 3.3' },
            { input: 'qwen2.5vl:latest', expected: 'qwen2.5vl:latest', description: '完全匹配 - Qwen2.5VL' },
            { input: 'erwan2/DeepSeek-Janus-Pro-7B:latest', expected: 'erwan2/DeepSeek-Janus-Pro-7B:latest', description: '完全匹配 - DeepSeek Janus Pro' },
            
            // 模糊匹配测试
            { input: 'llama3.3', expected: 'llama3.3:latest', description: '模糊匹配 - 无版本标签的Llama 3.3' },
            { input: 'qwen2.5vl', expected: 'qwen2.5vl:latest', description: '模糊匹配 - 无版本标签的Qwen2.5VL' },
            { input: 'mistral-small3.2', expected: 'mistral-small3.2:latest', description: '模糊匹配 - 无版本标签的Mistral Small' },
            
            // 命名空间处理测试
            { input: 'DeepSeek-Janus-Pro-7B:latest', expected: 'erwan2/DeepSeek-Janus-Pro-7B:latest', description: '命名空间匹配 - DeepSeek' },
            { input: 'Qwen2.5-7B-Instruct-1M:latest', expected: 'yasserrmd/Qwen2.5-7B-Instruct-1M:latest', description: '命名空间匹配 - Qwen2.5 Instruct' },
            
            // 未知模型测试
            { input: 'unknown-model:latest', expected: 'default', description: '未知模型 - 应返回默认配置' },
            { input: 'gpt-4', expected: 'default', description: '未知模型 - GPT-4' }
        ];

        // 运行测试
        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            let passedTests = 0;
            let totalTests = testCases.length;

            testCases.forEach((testCase, index) => {
                const result = getModelCapabilities(testCase.input);
                const expectedResult = modelCapabilities[testCase.expected];
                
                const passed = JSON.stringify(result) === JSON.stringify(expectedResult);
                if (passed) passedTests++;

                const resultDiv = document.createElement('div');
                resultDiv.className = `test-result ${passed ? 'success' : 'error'}`;
                resultDiv.innerHTML = `
                    <strong>测试 ${index + 1}: ${testCase.description}</strong><br>
                    输入: "${testCase.input}"<br>
                    期望匹配: "${testCase.expected}"<br>
                    实际结果: ${passed ? '✅ 通过' : '❌ 失败'}<br>
                    ${passed ? '' : `实际匹配到: "${JSON.stringify(result).substring(0, 100)}..."`}
                `;
                resultsDiv.appendChild(resultDiv);
            });

            // 添加总结
            const summaryDiv = document.createElement('div');
            summaryDiv.className = `test-result ${passedTests === totalTests ? 'success' : 'info'}`;
            summaryDiv.innerHTML = `
                <strong>测试总结</strong><br>
                通过: ${passedTests}/${totalTests}<br>
                成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%
            `;
            resultsDiv.appendChild(summaryDiv);
        }

        // 页面加载后运行测试
        window.onload = runTests;
    </script>
</body>
</html>
