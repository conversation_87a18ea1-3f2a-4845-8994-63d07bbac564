<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI聊天助手 - 测试版</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .status-online {
            background: linear-gradient(45deg, #10b981, #34d399);
            animation: pulse 2s infinite;
        }
        .status-offline {
            background: linear-gradient(45deg, #ef4444, #f87171);
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        .user-message {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        }
        .ai-message {
            background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <!-- 头部状态栏 -->
    <header class="bg-white/80 backdrop-blur-md border-b border-gray-200/50 px-4 py-3 sticky top-0 z-30">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-robot text-white text-sm"></i>
                </div>
                <div>
                    <h1 class="text-lg font-semibold text-gray-800">AI聊天助手</h1>
                    <div class="flex items-center space-x-2 text-sm">
                        <div id="status-indicator" class="w-3 h-3 rounded-full status-offline"></div>
                        <span id="status-text" class="text-gray-600">未连接</span>
                        <span class="text-gray-400">|</span>
                        <span id="model-info" class="text-gray-600">未选择模型</span>
                    </div>
                </div>
            </div>
            <button id="settings-btn" class="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                <i class="fas fa-cog"></i>
            </button>
        </div>
    </header>

    <!-- 主聊天区域 -->
    <main class="container mx-auto px-4 py-6 max-w-4xl">
        <div id="chat-container" class="bg-white/70 backdrop-blur-sm rounded-2xl shadow-lg p-6 mb-6 min-h-96 max-h-96 overflow-y-auto">
            <div id="welcome-message" class="text-center py-16">
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-robot text-white text-2xl"></i>
                </div>
                <h2 class="text-xl font-semibold text-gray-800 mb-2">AI聊天助手</h2>
                <p class="text-gray-500 mb-6">开始与AI进行智能对话</p>
                <button id="quick-setup" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-rocket mr-2"></i>快速设置
                </button>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="bg-white/70 backdrop-blur-sm rounded-2xl shadow-lg p-4">
            <div class="flex items-end space-x-3">
                <div class="flex-1">
                    <textarea 
                        id="user-input" 
                        placeholder="输入您的消息..." 
                        class="w-full p-3 border border-gray-200 rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        rows="1"
                        style="min-height: 44px; max-height: 120px;"
                    ></textarea>
                </div>
                <button id="send-btn" class="bg-blue-600 text-white p-3 rounded-xl hover:bg-blue-700 transition-colors min-w-[44px] h-[44px] flex items-center justify-center">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </main>

    <!-- 设置模态框 -->
    <div id="settings-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div id="settings-content" class="bg-white rounded-2xl shadow-2xl w-full max-w-md transform transition-all duration-300 scale-95 opacity-0">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-semibold text-gray-800">AI服务设置</h3>
                        <button id="close-settings" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">服务类型</label>
                            <select id="api-type" class="w-full p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="ollama">Ollama</option>
                                <option value="lm-studio">LM Studio</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">API地址</label>
                            <input type="text" id="api-url" value="http://localhost:11434" class="w-full p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">模型选择</label>
                            <select id="model-select" class="w-full p-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择模型</option>
                            </select>
                        </div>

                        <div class="flex space-x-3 pt-4">
                            <button id="auto-detect" class="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-search mr-2"></i>自动检测
                            </button>
                            <button id="test-connection" class="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-plug mr-2"></i>测试连接
                            </button>
                        </div>

                        <button id="save-settings" class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                            <i class="fas fa-save mr-2"></i>保存设置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载遮罩 -->
    <div id="loading-overlay" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white rounded-2xl p-8 text-center">
                <div class="w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
                <p class="text-gray-600">正在处理...</p>
            </div>
        </div>
    </div>

    <script>
        class ChatApp {
            constructor() {
                this.config = {
                    apiType: 'ollama',
                    apiUrl: 'http://localhost:11434',
                    model: ''
                };
                this.isConnected = false;
                this.init();
            }

            init() {
                this.loadConfig();
                this.bindEvents();
                this.updateUI();
                console.log('AI聊天助手已初始化');
            }

            loadConfig() {
                const saved = localStorage.getItem('test-chat-config');
                if (saved) {
                    this.config = { ...this.config, ...JSON.parse(saved) };
                }
            }

            saveConfig() {
                localStorage.setItem('test-chat-config', JSON.stringify(this.config));
            }

            bindEvents() {
                document.getElementById('settings-btn').addEventListener('click', () => this.openSettings());
                document.getElementById('quick-setup').addEventListener('click', () => this.openSettings());
                document.getElementById('close-settings').addEventListener('click', () => this.closeSettings());
                document.getElementById('auto-detect').addEventListener('click', () => this.autoDetect());
                document.getElementById('test-connection').addEventListener('click', () => this.testConnection());
                document.getElementById('save-settings').addEventListener('click', () => this.saveSettings());
                document.getElementById('send-btn').addEventListener('click', () => this.sendMessage());

                const userInput = document.getElementById('user-input');
                userInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                userInput.addEventListener('input', () => {
                    userInput.style.height = 'auto';
                    userInput.style.height = Math.min(userInput.scrollHeight, 120) + 'px';
                });

                document.getElementById('settings-modal').addEventListener('click', (e) => {
                    if (e.target.id === 'settings-modal') {
                        this.closeSettings();
                    }
                });
            }

            updateUI() {
                this.updateStatus();
                this.updateModelInfo();
                this.updateSettingsForm();
            }

            updateStatus() {
                const indicator = document.getElementById('status-indicator');
                const text = document.getElementById('status-text');
                
                if (this.isConnected) {
                    indicator.className = 'w-3 h-3 rounded-full status-online';
                    text.textContent = '已连接';
                } else {
                    indicator.className = 'w-3 h-3 rounded-full status-offline';
                    text.textContent = '未连接';
                }
            }

            updateModelInfo() {
                const modelInfo = document.getElementById('model-info');
                modelInfo.textContent = this.config.model || '未选择模型';
            }

            updateSettingsForm() {
                document.getElementById('api-type').value = this.config.apiType;
                document.getElementById('api-url').value = this.config.apiUrl;
            }

            openSettings() {
                const modal = document.getElementById('settings-modal');
                const content = document.getElementById('settings-content');
                
                modal.classList.remove('hidden');
                setTimeout(() => {
                    content.style.transform = 'scale(1)';
                    content.style.opacity = '1';
                }, 10);
                
                this.updateSettingsForm();
            }

            closeSettings() {
                const modal = document.getElementById('settings-modal');
                const content = document.getElementById('settings-content');
                
                content.style.transform = 'scale(0.95)';
                content.style.opacity = '0';
                
                setTimeout(() => {
                    modal.classList.add('hidden');
                }, 300);
            }

            showLoading() {
                document.getElementById('loading-overlay').classList.remove('hidden');
            }

            hideLoading() {
                document.getElementById('loading-overlay').classList.add('hidden');
            }

            showNotification(message, type = 'info') {
                alert(message); // 简化版通知
            }

            async autoDetect() {
                this.showLoading();
                
                try {
                    const response = await fetch('http://localhost:11434/api/tags');
                    if (response.ok) {
                        const data = await response.json();
                        this.config.apiType = 'ollama';
                        this.config.apiUrl = 'http://localhost:11434';
                        this.updateSettingsForm();
                        this.populateModels(data, 'ollama');
                        this.showNotification(`检测到 Ollama，发现 ${data.models?.length || 0} 个模型`);
                    } else {
                        throw new Error('Ollama 未运行');
                    }
                } catch (error) {
                    this.showNotification('未检测到可用的AI服务');
                } finally {
                    this.hideLoading();
                }
            }

            async testConnection() {
                this.showLoading();
                
                try {
                    const apiUrl = document.getElementById('api-url').value;
                    const response = await fetch(apiUrl + '/api/tags');
                    
                    if (response.ok) {
                        const data = await response.json();
                        this.populateModels(data, 'ollama');
                        this.showNotification('连接成功！');
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                } catch (error) {
                    this.showNotification(`连接失败: ${error.message}`);
                } finally {
                    this.hideLoading();
                }
            }

            populateModels(data, apiType) {
                const modelSelect = document.getElementById('model-select');
                modelSelect.innerHTML = '<option value="">请选择模型</option>';
                
                if (data.models) {
                    data.models.forEach(model => {
                        const option = document.createElement('option');
                        option.value = model.name;
                        option.textContent = model.name;
                        modelSelect.appendChild(option);
                    });
                }
            }

            saveSettings() {
                this.config.apiType = document.getElementById('api-type').value;
                this.config.apiUrl = document.getElementById('api-url').value;
                this.config.model = document.getElementById('model-select').value;
                
                if (!this.config.model) {
                    this.showNotification('请选择一个模型');
                    return;
                }
                
                this.saveConfig();
                this.isConnected = true;
                this.updateUI();
                this.closeSettings();
                this.showNotification('设置已保存');
            }

            async sendMessage() {
                const input = document.getElementById('user-input');
                const message = input.value.trim();
                
                if (!message) return;
                
                if (!this.config.model) {
                    this.showNotification('请先配置AI服务');
                    this.openSettings();
                    return;
                }
                
                input.value = '';
                input.style.height = 'auto';
                
                // 隐藏欢迎消息
                const welcomeMessage = document.getElementById('welcome-message');
                if (welcomeMessage) {
                    welcomeMessage.style.display = 'none';
                }
                
                this.addMessage('user', message);
                const thinkingId = this.addMessage('assistant', 'AI正在思考...', true);
                
                try {
                    const response = await fetch(`${this.config.apiUrl}/api/generate`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            model: this.config.model,
                            prompt: message,
                            stream: false
                        })
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        this.updateMessage(thinkingId, data.response || '抱歉，没有收到回复');
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                } catch (error) {
                    this.updateMessage(thinkingId, `❌ 发送失败: ${error.message}`);
                }
            }

            addMessage(role, content, isThinking = false) {
                const messageId = 'msg-' + Date.now();
                const messageDiv = document.createElement('div');
                messageDiv.id = messageId;
                messageDiv.className = `mb-4 ${role === 'user' ? 'flex justify-end' : 'flex justify-start'}`;
                
                const bubble = document.createElement('div');
                bubble.className = `max-w-xs sm:max-w-md px-4 py-3 rounded-2xl ${
                    role === 'user' 
                        ? 'user-message text-white rounded-br-md' 
                        : 'ai-message text-gray-800 rounded-bl-md'
                }`;
                
                bubble.innerHTML = `
                    <div class="whitespace-pre-wrap break-words">${content}</div>
                    <div class="text-xs mt-2 ${role === 'user' ? 'text-blue-200' : 'text-gray-500'}">${new Date().toLocaleTimeString()}</div>
                `;
                
                messageDiv.appendChild(bubble);
                
                const chatContainer = document.getElementById('chat-container');
                chatContainer.appendChild(messageDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;
                
                return messageId;
            }

            updateMessage(messageId, newContent) {
                const messageDiv = document.getElementById(messageId);
                if (messageDiv) {
                    const bubble = messageDiv.querySelector('div');
                    bubble.innerHTML = `
                        <div class="whitespace-pre-wrap break-words">${newContent}</div>
                        <div class="text-xs mt-2 text-gray-500">${new Date().toLocaleTimeString()}</div>
                    `;
                }
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            window.chatApp = new ChatApp();
        });
    </script>
</body>
</html>
